# Development Mode

This document explains how to use the development mode in the empty shelf detection application.

## Overview

The development mode provides functionality to speed up development by automating common tasks such as:

1. Bypassing the startup dialog
2. Automatically starting the camera
3. Automatically creating a presence check ROI
4. Automatically enabling detection

This is useful during development to avoid repetitive manual steps and focus on the code you're working on.

## How to Enable/Disable Development Mode

### Using the Command Line

The simplest way to enable or disable development mode is to use the `dev_mode_cli.py` script:

```bash
# Enable development mode
python dev_mode_cli.py --enable

# Disable development mode
python dev_mode_cli.py --disable

# Show current configuration
python dev_mode_cli.py --status
```

## Configuration Options

The development mode can be configured using the `dev_mode_cli.py` script:

```bash
# Show help
python dev_mode_cli.py --help

# Configure specific options
python dev_mode_cli.py --bypass-dialog --auto-start-camera --auto-enable-detection
python dev_mode_cli.py --roi-width 0.8 --roi-height 0.8 --roi-x 0.1 --roi-y 0.1
python dev_mode_cli.py --roi-label "Auto Presence Check" --roi-type presence_check
python dev_mode_cli.py --start-delay 1000 --detection-delay 2000
```

## How It Works

The development mode is initialized in `main3.py` before the application starts. It modifies the behavior of the `MainWindow` class to add development mode functionality.

The configuration is stored in `config/dev_mode_config.json` and can be edited directly or using the `dev_mode_cli.py` script.

## Troubleshooting

If development mode is not working:

1. Check if it's enabled: `python dev_mode_cli.py --status`
2. Check if the configuration file exists: `config/dev_mode_config.json`
3. Try running with verbose logging: `python main3.py --verbose`
