{
    // Python settings
    "python.envFile": "${workspaceFolder}/.env",
    "python.analysis.typeCheckingMode": "basic",
    "python.analysis.autoImportCompletions": true,
    "python.analysis.inlayHints.functionReturnTypes": true,
    "python.analysis.inlayHints.variableTypes": true,
    "python.analysis.extraPaths": [
        "${workspaceFolder}",
        "${workspaceFolder}/empty-shelf-detection"
    ],

    "python.testing.pytestEnabled": true,
    "python.testing.unittestEnabled": false,
    "python.testing.nosetestsEnabled": false,
    "python.testing.pytestArgs": [
        "tests"
    ],

    // Docker settings
    "docker.containers.sortBy": "CreatedTime",
    "docker.containers.description": [
        "ContainerName",
        "Status",
        "Ports"
    ],
    // Editor code actions
    "editor.codeActionsOnSave": {
        "source.organizeImports": "explicit"
    },
    // Formatter settings for different file types
    "[jsonc]": {
        "editor.defaultFormatter": "vscode.json-language-features"
    },
    "[java]": {
        "editor.defaultFormatter": "redhat.java"
    },
    // Editor settings
    "editor.formatOnSave": true,
    "editor.formatOnPaste": false,
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.detectIndentation": true,
    "editor.rulers": [
        160
    ],
    "editor.suggestSelection": "first",
    "editor.wordWrap": "off",
    "editor.renderWhitespace": "boundary",
    "editor.bracketPairColorization.enabled": true,
    "editor.guides.bracketPairs": true,
    "editor.linkedEditing": true,
    "editor.minimap.maxColumn": 160,
    "editor.minimap.showSlider": "always",
    "editor.smoothScrolling": true,
    "editor.stickyScroll.enabled": true,
    "editor.guides.indentation": true,
    "editor.guides.highlightActiveIndentation": true,
    "editor.suggest.preview": true,
    "editor.suggest.showStatusBar": true,
    // Files settings
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,
    "files.exclude": {
        "**/.git": true,
        "**/.svn": true,
        "**/.hg": true,
        "**/CVS": true,
        "**/.DS_Store": true,
        "**/Thumbs.db": true,
        "**/__pycache__": true,
        "**/.pytest_cache": true,
        "**/*.pyc": true,
        "**/.idea": true,
        "**/dist": true,
        "**/build": true,
        "**/.ipynb_checkpoints": true,
        "**/resources.py": true
    },
    "files.associations": {
        "*.yml": "yaml",
        "*.yaml": "yaml",
        "*.json": "json",
        "*.env*": "dotenv",
        "*.py": "python",
        "*.pyw": "python",
        "*.pyi": "python",
        "*.ipynb": "jupyter-notebook"
    },
    "files.eol": "\n",
    "files.insertFinalNewline": true,
    "files.trimTrailingWhitespace": true,
    "files.watcherExclude": {
        "**/.git/objects/**": true,
        "**/.git/subtree-cache/**": true,
        "**/node_modules/**": true,
        "**/target/**": true,
        "**/build/**": true,
        "**/dist/**": true,
        "**/resources.py": true
    },
    // Terminal settings
    "terminal.integrated.defaultProfile.windows": "PowerShell",
    "terminal.integrated.defaultProfile.linux": "bash",
    "terminal.integrated.scrollback": 5000,
    "terminal.integrated.persistentSessionReviveProcess": "never",
    "terminal.integrated.tabs.enabled": true,
    "terminal.integrated.cursorBlinking": true,
    "terminal.integrated.profiles.osx": {
        "zsh": {
            "path": "zsh",
            "icon": "terminal"
        },
        "bash": {
            "path": "bash",
            "icon": "terminal-bash"
        }
    },
    "terminal.integrated.defaultProfile.osx": "zsh",
    // Git settings
    "git.autofetch": true,
    "git.confirmSync": false,
    "git.enableSmartCommit": true,
    "git.branchProtection": [
        "master",
        "develop"
    ],
    "git.branchProtectionPrompt": "alwaysPrompt",
    "git.mergeEditor": true,
    "diffEditor.ignoreTrimWhitespace": false,
    // Explorer settings
    "explorer.compactFolders": false,
    "explorer.confirmDelete": true,
    "explorer.confirmDragAndDrop": true,
    "explorer.fileNesting.enabled": true,
    "explorer.fileNesting.patterns": {
        "*.py": "${capture}_test.py, ${capture}.test.py, test_${capture}.py, ${capture}.spec.py, ${capture}.pyi",
        "requirements.txt": "requirements*.txt, requirements-*.txt",
        "setup.py": "setup.cfg, pyproject.toml, MANIFEST.in"
    },
    // Search settings
    "search.exclude": {
        "**/__pycache__": true,
        "**/.pytest_cache": true,
        "**/*.pyc": true,
        "**/*.code-search": true,
        "**/dist": true,
        "**/build": true,
        "**/.ipynb_checkpoints": true,
        "**/venv": true,
        "**/.venv": true,
        "**/logs": true,
        "**/resources.py": true,
        "**/mmdetection": true


    },
    // Workbench settings
    "workbench.colorTheme": "Default Dark+",
    "workbench.iconTheme": "material-icon-theme",
    "workbench.editor.enablePreview": true,
    "workbench.editor.highlightModifiedTabs": true,
    "workbench.editor.limit.enabled": true,
    "workbench.editor.limit.value": 10,
    "workbench.startupEditor": "none",
    "workbench.editor.tabSizing": "shrink",
    "workbench.editor.decorations.badges": true,
    "workbench.editor.decorations.colors": true,
    "workbench.tree.indent": 16,
    "workbench.tree.renderIndentGuides": "always",
    "workbench.list.smoothScrolling": true,
    "workbench.commandPalette.history": 50,
    // GitHub Copilot settings
    "editor.inlineSuggest.enabled": true,
    "editor.inlineSuggest.showToolbar": "always",
    // Vibe coding settings
    "editor.fontFamily": "'JetBrains Mono', 'Fira Code', Consolas, 'Courier New', monospace",
    "editor.fontLigatures": true,
    "editor.fontSize": 14,
    "editor.lineHeight": 22,
    "editor.cursorBlinking": "phase",
    "editor.cursorStyle": "line",
    "editor.letterSpacing": 0.5,
    "workbench.colorCustomizations": {
        "editorCursor.foreground": "#64FFDA",
        "editor.lineHighlightBackground": "#1F2937",
        "editor.selectionBackground": "#3B4252",
        "editor.selectionHighlightBackground": "#4C566A80"
    },
    // Performance settings
    "extensions.ignoreRecommendations": false,

    // Configuration for the dedicated Mypy Type Checker extension (ms-python.mypy-type-checker)
    "mypy-type-checker.importStrategy": "fromEnvironment",
    "mypy-type-checker.args": [
        "--config-file=${workspaceFolder}/mypy.ini"
    ],
    // Formatting is now managed by the dedicated Black Formatter extension (ms-python.black-formatter)
    "flake8.args": [
        "--max-line-length=160",
        "--extend-ignore=E203,E302,E305,W503,E303"
    ],
    "python.analysis.diagnosticSeverityOverrides": {
        "reportMissingImports": "none",
        "reportUnknownMemberType": "none",
        "reportOptionalMemberAccess": "none",
        "reportOptionalOperand": "none"
    },
    // Black formatter configuration
    "black-formatter.args": ["--line-length", "160"],

    // Enable Black as the default Python formatter using the dedicated extension
    "[python]": {
        "editor.defaultFormatter": "ms-python.black-formatter",
        "editor.formatOnSave": true
    },
    "chat.mcp.discovery.enabled": true,

    "mcp": {
        "inputs": [],
        "servers": {
            "mcp-server-time": {
                "command": "python",
                "args": [
                    "-m",
                    "mcp_server_time",
                    "--local-timezone=America/Los_Angeles"
                ],
                "env": {}
            }
        }
    }
}
