{"version": "0.2.0", "configurations": [{"name": "Python: Main GUI", "type": "python", "request": "launch", "program": "${workspaceFolder}/empty-shelf-detection/main_gui.py", "console": "integratedTerminal", "justMyCode": false, "env": {"PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/empty-shelf-detection"}}, {"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "justMyCode": false, "env": {"PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/empty-shelf-detection"}}, {"name": "Python: Debug Tests", "type": "python", "request": "launch", "program": "${file}", "purpose": ["debug-test"], "console": "integratedTerminal", "justMyCode": false, "env": {"PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/empty-shelf-detection"}}, {"name": "Python: Seamless Mode", "type": "python", "request": "launch", "program": "${workspaceFolder}/empty-shelf-detection/main_seamless.py", "args": ["--offline"], "console": "integratedTerminal", "justMyCode": false, "env": {"PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/empty-shelf-detection"}}]}