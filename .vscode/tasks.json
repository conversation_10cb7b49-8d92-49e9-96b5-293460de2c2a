{
    "version": "2.0.0",
    "inputs": [
        {
            "id": "pythonInterpreter",
            "type": "command",
            "command": "python.interpreterPath"
        }
    ],
    "tasks": [
        {
            "label": "Run Python: Main GUI",
            "type": "shell",
            "command": "${input:pythonInterpreter}",
            "args": [
                "${workspaceFolder}/empty-shelf-detection/main_gui.py"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "reveal": "always",
                "panel": "new",
                "focus": true
            },
            "problemMatcher": [],
            "options": {
                "env": {
                    "PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/empty-shelf-detection"
                }
            }
        },
        {
            "label": "Run Python: Seamless Mode",
            "type": "shell",
            "command": "${input:pythonInterpreter}",
            "args": [
                "${workspaceFolder}/empty-shelf-detection/main_seamless.py",
                "--offline"
            ],
            "group": "build",
            "presentation": {
                "reveal": "always",
                "panel": "new",
                "focus": true
            },
            "problemMatcher": [],
            "options": {
                "env": {
                    "PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/empty-shelf-detection"
                }
            }
        },
        {
            "label": "Format Python Files (Black)",
            "type": "shell",
            "command": "${input:pythonInterpreter}",
            "args": [
                "-m", "black",
                "--line-length", "200",
                "${workspaceFolder}/empty-shelf-detection",
                "${workspaceFolder}/tests"
            ],
            "group": "build",
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            },
            "problemMatcher": []
        },
        {
            "label": "Lint Python Files (Flake8)",
            "type": "shell",
            "command": "${input:pythonInterpreter}",
            "args": [
                "-m", "flake8",
                "${workspaceFolder}/empty-shelf-detection",
                "${workspaceFolder}/tests",
                "--max-line-length=200",
            ],
            "group": "build",
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            },
            "problemMatcher": []
        },
        {
            "label": "Run Python Tests (Pytest)",
            "type": "shell",
            "command": "${input:pythonInterpreter}",
            "args": [
                "-m", "pytest",
                "${workspaceFolder}/tests",
                "-v"
            ],
            "group": "test",
            "presentation": {
                "reveal": "always",
                "panel": "dedicated"
            },
            "problemMatcher": []
        },
        {
            "label": "Run mypy Type Check",
            "type": "shell",
            "command": "${input:pythonInterpreter}",
            "args": [
                "-m", "mypy",
                "--config-file=${workspaceFolder}/.mypy.ini",
                "${workspaceFolder}/empty-shelf-detection",
                "${workspaceFolder}/tests"
            ],
            "group": "test",
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            },
            "problemMatcher": []
        },
        {
            "label": "Install Project Dependencies",
            "type": "shell",
            "command": "${input:pythonInterpreter}",
            "args": [
                "-m", "pip",
                "install",
                "-r",
                "${workspaceFolder}/empty-shelf-detection/requirements.txt"
            ],
            "group": "none",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "problemMatcher": []
        }
    ]
}
