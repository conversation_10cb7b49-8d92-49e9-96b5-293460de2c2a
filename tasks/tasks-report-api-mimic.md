# API Mimic for Offline Mode Task

## Objective
Implement an offline mode for the seamless tracking system that:
1. Takes a photo when reporting methods are called
2. Saves the photo with a unique ID
3. Creates a log file that includes the ID of the image for traceability. Use trackers reporting format report file.

## Current Structure
- The ShelveTracker class in tracker.py handles reporting to the API
- When in online mode, it sends data to a remote API endpoint
- There's currently no comprehensive offline mode that captures visual evidence

## Requirements
- Implement a command-line parameter `--offline` and  `store_true` to enable offline mode and send photo mode.
- Capture an image at the moment a report would be sent to the API
- Generate a unique ID for each captured image
- Save the image with the ID in the filename
- Create or append to a log file with:
  - Image ID
  - Timestamp
  - Report data (same data that would be sent to API)
  - Camera name and shelve ID
- Ensure the system works seamlessly in both online and offline modes

## Steps
- [x] Review current reporting flow in ShelveTracker class
- [x] Understand how command-line arguments are processed in the application
- [x] Modify the configuration to include an offline mode flag
- [x] Keep last image so it can be used for offline reporting
- [x] Update the image capture mechanism for the reporting method
- [x] Implement unique ID generation for images(time + camera name)
- [x] Create a directory structure for storing offline data and log file.
- [x] Implement image saving functionality
- [x] Design and implement the log file format
- [x] Add logging functionality to record report details with image IDs
- [x] Test the offline mode with various scenarios
- [x] Document the new functionality in the docs
- [x] Organize tracker classes so that they are easier to read, understand, and modify.

## Technical Considerations
- The `store_true` argument in argparse means the parameter doesn't take a value - its presence sets the value to True
- Need to ensure the camera frame is accessible at the time of reporting
- Consider using ISO format timestamps for filenames and logs
- Log files should be in a human-readable format (e.g., JSON or CSV)
- Directory structure should organize data by date and camera

## Expected Outcome
When running in offline mode:
- No API calls will be made
- Images will be captured at reporting time
- Images will be saved with unique IDs
- A log file will record all reporting events with image references
- The system will function normally otherwise

## Progress

### Completed
We have successfully implemented the offline mode for the seamless tracking system. The implementation includes:

1. Adding the `--offline` command-line parameter
2. Modifying the configuration to include offline mode settings
3. Capturing camera frames at the time of reporting
4. Generating unique IDs for images based on timestamp and camera name
5. Creating a structured directory system for storing offline data
6. Implementing image saving functionality
7. Designing and implementing a JSON-based log file format
8. Adding comprehensive logging of report details with image references
9. Creating documentation for the offline mode

### Remaining
All tasks have been completed. The tracker classes have been organized to make them easier to read, understand, and modify. We have:

1. Organized imports into logical groups (standard library, third-party, application)
2. Added clear section comments to improve readability
3. Improved error handling with better exception handling
4. Fixed code duplication issues
5. Ensured consistent naming conventions
6. Added proper documentation for methods and classes
