# Task: Remove Duplicate Config File

## Aim
Remove the duplicate `config.yaml` file and update references to ensure the application uses only one configuration file.

## Current State
There are currently two `config.yaml` files:
1. `./config/config.yaml` (in the project root)
2. `./empty-shelf-detection/config/config.yaml` (in the empty-shelf-detection subdirectory)

This duplication can cause confusion and inconsistencies in configuration.

## Steps to Complete
1. Compare the content of both files to determine which one to keep
2. Identify all references to the config files in the codebase
3. Update references to point to the single config file
4. Remove the duplicate file
5. Test the application to ensure it still works correctly

## Status
✅ Completed

## Resolution
1. Identified the duplicate config file at `empty-shelf-detection/empty-shelf-detection/config/config.yaml`
2. Confirmed that the main config file at `empty-shelf-detection/config/config.yaml` is the one being used by the application
3. Verified that there are no references to the duplicate file in the codebase
4. Removed the duplicate file
5. The application will continue to use the main config file without any changes needed
