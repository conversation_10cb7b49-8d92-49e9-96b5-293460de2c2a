# Tracker Scheduler System Analysis

## Overview

The tracker scheduler system in the empty-shelf-detection project is responsible for managing the execution of various trackers across multiple cameras. It follows a tracker-centric approach where trackers drive the process rather than cameras. This analysis focuses on understanding the current implementation and proposing enhancements for time-based scheduling.

## Key Components

### 1. TrackerScheduler Class

The `TrackerScheduler` class (in `core/tracker_scheduler.py`) is the central component that:

- Periodically checks all registered trackers
- Determines which trackers should run based on their `should_run()` method
- Opens camera connections on-demand for trackers that need to run
- Processes a single frame for each tracker
- Closes the camera connection immediately after processing

### 2. TrackerTask Class

The `TrackerTask` class represents a single processing task for a specific tracker, containing:
- Camera name
- RTSP URL
- Tracker instance
- Creation timestamp
- Tracker type

### 3. Worker Threads

The scheduler uses a pool of worker threads to process tasks in parallel:
- Each worker thread gets tasks from a queue
- Processes the task by opening the camera, reading a frame, and running the tracker
- <PERSON><PERSON> reporting if needed
- Closes the camera connection
- Updates statistics

### 4. Scheduling Logic

The current scheduling logic is implemented in the `_scheduler_loop` method:
1. Gets all registered trackers from the `DetectionModeManager`
2. Checks each tracker to see if it should run using `tracker.should_run(current_time)`
3. Creates a task for each tracker that should run
4. Adds the task to a queue for processing by worker threads

## Current Time-Based Scheduling Implementation

The current implementation relies on the `should_run()` method of each tracker to determine when it should be executed. This method is expected to be implemented by each tracker class and should return `True` when the tracker should run.

The key scheduling-related code is in the `_check_and_schedule_tracker` method:

```python
def _check_and_schedule_tracker(self, camera_name, tracker, camera_urls, current_time):
    # Skip disabled trackers
    if tracker.is_disabled():
        return

    # Check if a task for this camera and tracker type is already running
    task_key = (camera_name, tracker.__class__.__name__)
    
    with self.running_tasks_lock:
        # If a task is already running and recent (within last 5 seconds), skip
        if task_key in self.running_tasks:
            last_run_time = self.running_tasks[task_key]
            if current_time - last_run_time < 5.0:
                self.logger.debug(f"Skipping task creation for {camera_name} with {tracker.__class__.__name__} - already running")
                return

    # Check if the tracker should run
    if not tracker.should_run(current_time):
        return
        
    # Get the camera URL
    rtsp_url = camera_urls.get(camera_name)
    if not rtsp_url:
        self.logger.warning(f"No URL found for camera {camera_name}, skipping")
        return

    # Create and queue the task
    self._create_and_queue_task(camera_name, rtsp_url, tracker, task_key, current_time)
```

## GUI Integration

The scheduler system is integrated with the GUI through:

1. **ROIGui.py**: The main GUI application that allows users to:
   - Define regions of interest (ROIs)
   - Configure trackers
   - Start/stop camera monitoring

2. **DockManager.py**: Manages the dock widgets in the GUI, including:
   - Camera tree view
   - File list
   - Label list

## Limitations of Current Implementation

1. **Limited Time-Based Scheduling**: The current implementation doesn't have explicit support for:
   - Running trackers only during specific hours (e.g., after 8 PM)
   - Pausing trackers during specific time periods (e.g., 12:00-13:00)
   
2. **No GUI for Time Scheduling**: There's no user interface to configure time-based scheduling rules.

3. **Centralized Configuration**: Time-based rules are not centrally managed or stored in a configuration file.

## Proposed Enhancements for Time-Based Scheduling

### 1. Extend BaseTracker Class

Enhance the `BaseTracker` class to include time-based scheduling capabilities:

```python
class BaseTracker:
    def __init__(self):
        # Existing initialization
        
        # Time-based scheduling properties
        self.active_hours = {
            "start": None,  # Format: "HH:MM" or None for no restriction
            "end": None,    # Format: "HH:MM" or None for no restriction
        }
        self.pause_periods = []  # List of {"start": "HH:MM", "end": "HH:MM"} dictionaries
    
    def should_run(self, current_time):
        # Check if current time is within active hours
        if not self._is_within_active_hours(current_time):
            return False
            
        # Check if current time is within a pause period
        if self._is_within_pause_period(current_time):
            return False
            
        # Existing should_run logic
        return True
    
    def _is_within_active_hours(self, current_time):
        if not self.active_hours["start"] and not self.active_hours["end"]:
            return True  # No time restrictions
            
        current_datetime = datetime.fromtimestamp(current_time)
        current_time_str = current_datetime.strftime("%H:%M")
        
        # Check if we should only run after a certain time
        if self.active_hours["start"] and not self.active_hours["end"]:
            return current_time_str >= self.active_hours["start"]
            
        # Check if we should only run before a certain time
        if not self.active_hours["start"] and self.active_hours["end"]:
            return current_time_str <= self.active_hours["end"]
            
        # Check if current time is between start and end
        # Handle overnight periods (e.g., 22:00 to 06:00)
        if self.active_hours["start"] <= self.active_hours["end"]:
            return self.active_hours["start"] <= current_time_str <= self.active_hours["end"]
        else:
            return current_time_str >= self.active_hours["start"] or current_time_str <= self.active_hours["end"]
    
    def _is_within_pause_period(self, current_time):
        if not self.pause_periods:
            return False  # No pause periods
            
        current_datetime = datetime.fromtimestamp(current_time)
        current_time_str = current_datetime.strftime("%H:%M")
        
        for period in self.pause_periods:
            start = period["start"]
            end = period["end"]
            
            # Handle overnight periods
            if start <= end:
                if start <= current_time_str <= end:
                    return True
            else:
                if current_time_str >= start or current_time_str <= end:
                    return True
                    
        return False
```

### 2. Configuration Storage

Store time-based scheduling configuration in the `config.yaml` file:

```yaml
scheduler:
  num_workers: 4
  time_based_rules:
    default:
      active_hours:
        start: null  # null means no restriction
        end: null
      pause_periods: []
    night_only:
      active_hours:
        start: "20:00"  # Run only after 8 PM
        end: "06:00"    # Until 6 AM
      pause_periods: []
    business_hours:
      active_hours:
        start: "09:00"  # Run from 9 AM
        end: "17:00"    # Until 5 PM
      pause_periods:
        - start: "12:00"  # Pause during lunch
          end: "13:00"
```

### 3. GUI Integration

Add a time scheduling configuration panel to the GUI:

1. **Time Schedule Tab**: Add a new tab in the settings dialog for time-based scheduling
2. **Rule Templates**: Provide predefined rule templates (e.g., "Night Only", "Business Hours")
3. **Custom Rules**: Allow users to create custom time rules
4. **Rule Assignment**: Assign rules to specific trackers or tracker types

## Implementation Plan

1. **Extend BaseTracker**: Add time-based scheduling methods to the `BaseTracker` class
2. **Update Configuration**: Modify `config.yaml` to include time-based scheduling rules
3. **Enhance GUI**: Add time scheduling configuration to the settings dialog
4. **Update Initialization**: Modify tracker initialization to load time-based rules
5. **Testing**: Create test cases for various time-based scenarios

## Conclusion

The current tracker scheduler system provides a solid foundation for managing tracker execution. By extending it with time-based scheduling capabilities, we can provide more flexibility and control over when trackers run, allowing for scenarios like:

- Running specific models only during night hours (after 8 PM)
- Pausing processing during lunch breaks (12:00-13:00)
- Defining business hours for certain types of monitoring

These enhancements will make the system more versatile and efficient, especially for retail environments with varying monitoring needs throughout the day.
