# Time-Based Scheduling and Tracker Configuration

## Current State Analysis

The current system has:
- `BaseTracker` class with `should_run()` and `should_report()` methods
- `TrackerScheduler` that processes tracker tasks and handles reporting
- `CoreConfig` for configuration management
- `Shape` class for storing ROI information
- `ReportingService` for sending reports to API or saving locally
- Seamless/batch mode that initializes trackers from annotations via `TrackerScheduler.initialize_trackers_from_annotations()`

The system currently lacks:
- Time-based scheduling for trackers (running only during specific hours)
- Ability to pause trackers during specific time periods (e.g., 12:00-13:00)
- User interface to configure time-based scheduling rules
- Storage of time-based scheduling configuration templates in a folder and reading those templates.
- Storage of time-based scheduling configuration inside annotations
- Customizable check intervals and report intervals per tracker
- Validation of scheduling configuration before saving to annotations

## Implementation Plan

### Step 1: Update Configuration Structure (2 hours) ⬜
1. ⬜ Update `config.yaml` to include time-based scheduling settings
2. ⬜ Add methods to `CoreConfig` to handle these new settings
3. ⬜ Create a schema for time-based rules and intervals

### Step 2: Update BaseTracker Class (4 hours) ⬜
1. ⬜ Add time-based scheduling properties to `BaseTracker`
2. ⬜ Implement time checking methods in `BaseTracker`
3. ⬜ Update `should_run()` method to consider time-based rules
4. ⬜ Add support for custom check and report intervals

### Step 3: Update Shape Class and Related Classes (3 hours) ⬜
1. ⬜ Add time-based scheduling properties to `Shape` class (rule name, active hours, pause periods, check interval, report interval)
2. ⬜ Update `to_dict()` and constructor to handle these properties
3. ⬜ Update all related reading and writing classes to support the new properties

### Step 4: Update Tracker Implementations (4 hours) ⬜
1. ⬜ Update `ShelfTracker`, `PresenceStateTracker`, and `PersonTracker` to use the new time-based scheduling
2. ⬜ Implement logic to read properties from Shape if they exist, otherwise use config properties
3. ⬜ Update `should_run()` and `should_report()` methods to use custom intervals

### Step 5: Create GUI for Tracker Scheduling (6 hours) ⬜
1. ⬜ Create Tracker Scheduling UI Components
2. ⬜ Design and implement UI components
3. ⬜ Implement preset template application
4. ⬜ Integrate with ROIGui and DockManager
5. ⬜ Implement saving and loading

### Step 6: Update TrackerScheduler (2 hours) ⬜
1. ⬜ Update `_check_and_schedule_tracker()` method to support time-based checks
2. ⬜ Add additional checks needed for time-based scheduling
3. ⬜ Implement proper handling of custom intervals

### Step 7: Testing and Documentation (4 hours) ⬜
1. ⬜ Test all components with different configurations
2. ⬜ Document the new features in the user guide
3. ⬜ Update REFACTORING_STATUS.md

## Detailed Implementation Steps

### Step 1: Update Configuration Structure

#### 1.1 Add Time-Based Scheduling Configuration to config.yaml
- ⬜ Add a new section for time-based scheduling configuration
- ⬜ Define default time-based rules
- ⬜ Define night-only rule (20:00-06:00)
- ⬜ Define business hours rule (09:00-17:00 with lunch break 12:00-13:00)
- ⬜ Define default check and report intervals for each tracker type

#### 1.1.1 Unified Scheduling Template Structure

All trackers will use a single, comprehensive template structure that defines all aspects of scheduling. The template includes:

```yaml
tracker_scheduling:
  # Which days of the week the tracker will run (0=Sunday, 6=Saturday)
  active_days: [0, 1, 2, 3, 4, 5, 6]  # Default: all days

  # General active hours (outside of specific periods)
  active_hours:
    start: null  # null means no restriction (always active)
    end: null

  # Specific time periods with custom check and report intervals
  active_periods:
    - name: "Morning Rush"
      start: "08:00"
      end: "10:00"
      check_interval: 2  # minutes
      report_interval: 10  # minutes
    - name: "Lunch Time"
      start: "12:00"
      end: "14:00"
      check_interval: 3  # minutes
      report_interval: 15  # minutes

  # Times when the tracker should pause (even during active hours/periods)
  pause_periods:
    - start: "03:00"  # System maintenance window
      end: "04:00"

  # Default intervals (used outside of active_periods)
  check_interval: 10  # minutes
  report_interval: 60  # minutes
```

The application will provide several preset configurations based on this template:

1. **Always Active** - Active all days, all hours
2. **Night Only** - Active from 20:00 to 06:00 every day
3. **Business Hours** - Active 09:00-17:00 Monday-Friday with lunch break
4. **Weekend Only** - Active only on Saturday and Sunday
5. **Peak Hours** - Active only during specific busy periods with faster check/report intervals

Users can select any preset and then modify all values through the UI. The actual values (not the preset name) will be stored in the Shape object.

#### 1.2 Add Methods to CoreConfig
- ⬜ Add method to get a specific time-based rule
- ⬜ Add method to get active hours for a rule
- ⬜ Add method to get pause periods for a rule
- ⬜ Add methods to get check and report intervals for tracker types

#### 1.3 Create Schema for Time-Based Rules
- ⬜ Define the structure for active hours (start/end times)
- ⬜ Define the structure for pause periods
- ⬜ Define the structure for check and report intervals
- ⬜ Document the schema for future reference

### Step 2: Update BaseTracker Class

#### 2.1 Add Time-Based Scheduling Properties
- ⬜ Add active hours property
- ⬜ Add active days property
- ⬜ Add active periods property
- ⬜ Add pause periods property
- ⬜ Add custom check interval property
- ⬜ Add custom report interval property

#### 2.2 Implement Time Checking Methods
- ⬜ Add method to check if current time is within active hours
- ⬜ Add method to check if current day is in active days
- ⬜ Add method to check if current time is within any active period
- ⬜ Add method to check if current time is within a pause period
- ⬜ Handle midnight crossing time ranges (e.g., 22:00-06:00)
- ⬜ Implement special handling for active periods with custom intervals

#### 2.3 Update should_run() Method
- ⬜ Check if current day is in active days
- ⬜ Check if current time is within active hours
- ⬜ Check if current time is within a pause period
- ⬜ Check if current time is within any active period and use its custom interval if applicable
- ⬜ Use custom check interval if provided, otherwise use config value
- ⬜ Only proceed with interval check if time checks pass

#### 2.4 Update should_report() Method
- ⬜ Check if current time is within any active period and use its custom report interval if applicable
- ⬜ Use custom report interval if provided, otherwise use config value
- ⬜ Implement proper interval checking
- ⬜ Ensure consistent reporting behavior with the should_run() method

### Step 3: Update Shape Class and Related Classes

#### 3.1 Add Time-Based Scheduling Properties to Shape
- Add active hours property (start/end times as direct values, not rule name)
- Add active days property (list of day indices, 0=Sunday, 6=Saturday)
- Add active periods property (list of periods with start/end times and custom intervals)
- Add pause periods property (list of start/end time pairs)
- Add check interval property (in minutes)
- Add report interval property (in minutes)
- Do not store rule name in the Shape object, only the actual values

#### 3.2 Update Shape Methods
- Update constructor to accept new properties
- Update to_dict() to include new properties in serialization
- Add default values for new properties
- Implement validation methods to ensure time values are in correct format
- Remove any backward compatibility code as it's not needed

#### 3.3 Update Reading and Writing Classes

##### 3.3.1 Update Shape.from_dict() Method
- Enhance the `from_dict()` method to read time-based properties from serialized data
- Add support for active_hours, pause_periods, check_interval, and report_interval
- Implement proper type conversion and validation

##### 3.3.2 Update XML Reader/Writer (PascalVocReader/PascalVocWriter)
- Add XML elements for active_hours, pause_periods, check_interval, and report_interval
- Update the `parse_xml()` method to extract these properties
- Update the `append_objects()` method to include these properties in XML output
- Implement proper serialization of complex structures (like pause_periods list)

##### 3.3.3 Update JSON Reader/Writer (CreateMLReader)
- Add support for time-based properties in JSON format
- Ensure proper serialization/deserialization of these properties
- Handle nested structures appropriately

##### 3.3.4 Update YOLO Reader/Writer (YoloReader)
- Add support for time-based properties in the companion JSON file
- Update the loading and saving of metadata

##### 3.3.5 Update FileHandler Methods
- Ensure the `load_annotations()` method properly handles the new properties
- Add validation in the `save_annotations()` method to prevent invalid data
- Make sure batch mode can properly read and apply these properties through AnnotationReader

### Step 4: Update Tracker Implementations

#### 4.1 Update BaseTracker.set_shape_data() Method
- Enhance the method to extract time-based properties from Shape objects
- Read active_hours, pause_periods, check_interval, and report_interval from Shape
- Apply these values to the tracker instance
- Implement fallback to config values if Shape properties are not present
- Add validation to ensure the loaded values are in the correct format
- Ensure this works properly in both GUI and batch/seamless modes
- Log detailed information about the loaded scheduling configuration

#### 4.2 Update ShelfTracker
- Override should_run() to incorporate time-based checks
- Override should_report() to use custom report interval
- Ensure proper handling of time-based properties in batch/seamless mode

#### 4.3 Update PresenceStateTracker
- Override should_run() to incorporate time-based checks
- Override should_report() to use custom report interval
- Ensure proper handling of time-based properties in batch/seamless mode

#### 4.4 Update PersonTracker
- Override should_run() to incorporate time-based checks
- Override should_report() to use custom report interval
- Ensure proper handling of time-based properties in batch/seamless mode

### Step 5: Create GUI for Tracker Scheduling

#### 5.1 Create Tracker Scheduling UI Components

##### 5.1.1 Create TrackerSchedulingDock Class
- Create a new dock widget class in `empty-shelf-detection/gui/TrackerSchedulingDock.py`
- Implement as a QDockWidget that can be added to the right dock area in ROIGui
- Design the dock to show scheduling configuration for the currently selected shape
- Include template selection dropdown and quick edit controls
- Add visual indicators for active/inactive periods
- Integrate with the existing DockManager class

##### 5.1.2 Create TrackerSchedulingDialog Class
- Create a new dialog class in `empty-shelf-detection/helpers/TrackerSchedulingDialog.py`
- Model after existing dialog classes like `CameraSettingsDialog.py`
- Implement as a modal dialog for advanced configuration
- Launch from a "Advanced Settings" button in the dock widget
- Use for creating and managing templates

#### 5.2 UI Design and Components
- Use QCalendarWidget for date selection and visualization of active days
- Use QTimeEdit widgets for time range selection with intuitive spinners
- Implement custom QTableWidget for active periods with editable cells
- Use QCheckBox widgets for day selection (arranged horizontally)
- Add QSpinBox widgets for interval configuration with appropriate ranges
- Use QTabWidget to organize different aspects of scheduling (General, Active Periods, Pause Periods)
- Implement a color-coded visual indicator for active/inactive time periods
- Add a preview panel showing the current schedule in a timeline view

#### 5.3 UI Layout and Styling
- Create a clean, modern interface with proper spacing and alignment
- Use consistent color scheme that matches the application's existing style
- Implement responsive layout that adjusts to different screen sizes
- Add tooltips for all controls to explain their purpose
- Use icons for common actions (add, remove, edit)
- Ensure all UI elements have proper keyboard navigation
- Apply selected configuration to the dock in the top right of the application
- Allow users to modify all settings through direct interaction after initial selection

#### 5.4 Preset Template Application
- When a preset template is selected, populate all fields with the template values
- Apply these values directly to the Shape properties when saved
- Display all template values in the UI for immediate editing
- Allow users to modify any and all values after template selection
- Validate all values before saving to ensure they are in the correct format
- Store the actual configuration values in the Shape object, not the template name
- Provide visual feedback when a template is applied

#### 5.5 Integrate with ROIGui and DockManager

##### 5.5.1 Update DockManager
- Add a new method `setup_scheduling_dock()` in DockManager class
- Register the new dock in the DockManager's docks dictionary
- Update `DockManager.setup_dock_areas()` to include the scheduling dock
- Implement methods to update the scheduling dock when shapes are selected

##### 5.5.2 Update ROIGui
- Add a new action in `ROIGui.setup_menus()` for toggling the scheduling dock
- Update `ROIGui.shape_selection_changed()` to update the scheduling dock with selected shape data
- Add a button in the toolbar in `ROIGui.setup_camera_mode_toolbar()`
- Implement a method to apply scheduling settings to selected shapes
- Update `ROIGui.save_labels()` to ensure scheduling data is saved with annotations

##### 5.5.3 Implement On-Screen Editing
- Allow direct editing of scheduling properties in the dock widget
- Update the Shape object in real-time as changes are made
- Provide immediate visual feedback in the main canvas
- Implement undo/redo functionality for scheduling changes

#### 5.6 Implement Saving and Loading
- Save the complete scheduling configuration to Shape objects
- Load the complete scheduling configuration from Shape objects
- Update annotation files to include all scheduling data
- Implement proper error handling for invalid configurations
- Ensure validation before saving to prevent invalid data in annotations
- Provide visual feedback during save/load operations

### Step 6: Update TrackerScheduler

#### 6.1 Update _check_and_schedule_tracker() Method
- Ensure time-based checks are properly applied
- Add logging for time-based scheduling decisions
- Handle edge cases in time-based scheduling

#### 6.2 Add Additional Checks
- Add checks for time rule validity
- Add handling for rule changes at runtime
- Ensure proper cleanup of scheduled tasks
- Implement proper handling of custom intervals

### Step 7: Testing and Documentation

#### 7.1 Test with Different Configurations
- Test with default configuration (always active)
- Test with night-only configuration
- Test with business hours configuration
- Test with custom configurations
- Test with custom check and report intervals

#### 7.2 Document New Features
- Update user guide with time-based scheduling instructions
- Add examples of common configurations
- Document the time format and rules
- Document how to set custom check and report intervals

#### 7.3 Update REFACTORING_STATUS.md
- Add entry for time-based scheduling implementation
- Document any known limitations or future improvements
- Update status of related tasks

## Future Improvements

### Notification System (Future Version)

The notification system will be implemented as a separate feature in future versions to notify users of state changes and important events. This system will include:

1. Support for email and webhook notifications
2. GUI for configuring notification preferences
3. Notification templates for different event types
4. Notification history and management
