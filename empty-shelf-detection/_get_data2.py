import os
from dotenv import load_dotenv
from roboflow import <PERSON><PERSON><PERSON>


def download_dataset(download_format: str = "coco") -> None:
    """
    Download dataset from Roboflow.

    Reference: https://docs.roboflow.com/api/download-dataset

    Warning:
        This function requires the environment variable *ROBOFLOW_API_KEY* to be set.
    """
  
    dotenv_path = os.path.join(os.path.dirname(__file__), '.env.example') 
    load_dotenv(dotenv_path)
    os.makedirs("datasets", exist_ok=True)
    print(os.environ.get("ROBOFLOW_API_KEY"));
    print(os.path.dirname(__file__))
    exit
    
    rf = Roboflow(api_key=os.environ.get("ROBOFLOW_API_KEY"))
    project = rf.workspace("thesis-dpgss").project("out-of-stock-tnjj1")
    version = project.version(23)
    dataset = version.download("yolov8")
 
    # Rename dataset
    os.rename(dataset.location, dataset.location + download_format)

    print("Downloaded dataset to: ", dataset.location)


if __name__ == "__main__":
    download_dataset("yolov8")

    