# Configuration System

This document describes the configuration system for the Empty Shelf Detection application.

## Overview

The application uses a centralized configuration system based on the `AppConfig` class. This class handles loading configuration from files, environment variables, and provides default values for all settings.

## Configuration Files

The application uses the following configuration files:

1. **config/config.yaml**: Main configuration file for the application. Contains settings for:
   - Tracker settings
   - ROI tracker settings
   - ROI reporter settings
   - API settings
   - Camera settings
   - Model settings
   - Logging settings
   - Development mode settings
   - Camera configuration file

2. **data/cameras_all.json**: Camera configuration file. Contains information about all cameras, including:
   - NVR information
   - Camera names
   - RTSP URLs
   - ROI annotations

## Configuration Classes

### AppConfig

The `AppConfig` class is a singleton that provides access to the application configuration. It loads the configuration from `config/config.yaml` and provides methods to get and set configuration values.

```python
from config.app_config import AppConfig

# Get the singleton instance
config = AppConfig.get_instance()

# Get a configuration value
value = config.get('section', 'key', default=None)

# Get an entire section
section = config.get_section('section')

# Set a configuration value
config.set('section', 'key', value)
```

### Config (Camera Configuration)

The `Config` class is a singleton that provides access to the camera configuration. It loads the camera configuration from the file specified in `AppConfig` (`camera_config.file`).

```python
from config.config import Config

# Get the singleton instance
camera_config = Config.get_instance()

# Get all camera URLs
camera_urls = camera_config.get_camera_urls()

# Get all camera names
camera_names = camera_config.get_camera_names_form_list()

# Get ROIs for a camera
rois = camera_config.get_rois(camera_name)

# Update ROIs for a camera
camera_config.update_rois(camera_name, new_rois)
```

## Development Mode

Development mode is controlled by the `dev_mode` section in `config/config.yaml`. It provides settings for:

- Enabling/disabling development mode
- Bypassing the startup dialog
- Auto-starting the camera
- Auto-enabling detection
- Configuring the ROI

The development mode is applied by the `dev_mode.py` module, which modifies the `MainWindow` class to add development mode functionality.

```python
import dev_mode
from gui.ROIGui import MainWindow

# Apply development mode to the MainWindow class
dev_mode.apply_dev_mode(MainWindow)
```

## Running in Development Mode

To run the application in development mode, use the `main_gui_dev.py` script:

```bash
python main_gui_dev.py
```

This will start the application with development mode enabled, which will:

1. Bypass the startup dialog
2. Automatically start the camera
3. Automatically enable detection
4. Create a presence check ROI if none exists

## Configuration Migration

If you're migrating from an older version of the application, you may need to update your configuration files. The application now uses a single configuration file (`config/config.yaml`) for all settings, and a single camera configuration file (`data/cameras_all.json`) for all camera information.

The older configuration files (`data/cameras.json` and `data/cameras2.json`) are no longer used.
