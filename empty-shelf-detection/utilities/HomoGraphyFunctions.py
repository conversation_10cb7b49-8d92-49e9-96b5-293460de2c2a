# HomoGraphyFunctions

import cv2
import numpy as np
from libs.shape import Shape
from libs.utils import generate_color_by_text
from PyQt5.QtCore import QPointF


def homography_points_selected(mainwindow, shelve_data, image_points):
    """
    <PERSON><PERSON> called when four homography points are selected.
    """

    width_cm = shelve_data["width_cm"]
    height_cm = shelve_data["height_cm"]
    real_world_points = shelve_data["real_world_points"]

    # Convert to numpy arrays
    image_pts_np = np.array([[p.x(), p.y()] for p in image_points], dtype=np.float32)

    x_min = int(min(p.x() for p in image_points))
    y_min = int(min(p.y() for p in image_points))

    print(f"image_pts_np: {image_points}")

    image_pts_np = np.array([[p.x() - x_min, p.y() - y_min] for p in image_points], dtype=np.float32)

    dst_pts = np.array(shelve_data["real_world_points"], dtype=np.float32)
    print(f"image_pts_np_in_roi: {image_pts_np}")
    print(f"real_pts_np: {dst_pts}")

    # Compute homography matrix
    homography_matrix, status = cv2.findHomography(image_pts_np, dst_pts, method=cv2.RANSAC)

    if homography_matrix is not None and status is not None and all(status):

        # Store homography matrix in annotations

        if homography_matrix is not None:
            # Build a shape via factory
            shape = Shape.create(
                label=shelve_data["name"],
                shape_type="rectangle",
                # Visual properties
                line_color=generate_color_by_text(shelve_data["name"]),
                fill_color=generate_color_by_text(shelve_data["name"]),
                paint_label=False,
                # Shelve properties
                shelve_id=shelve_data["id"],
                magaza_no=shelve_data["magaza_no"],
                shelve_width_cm=width_cm,
                shelve_height_cm=height_cm,
                # Homography properties
                homography=homography_matrix.tolist(),
                homography_points=[[p.x(), p.y()] for p in image_points],
                # Other properties
                fixed=True,
            )
            # Add rectangle corners
            # e.g. top-left, top-right, bottom-right, bottom-left
            x_min = min(p.x() for p in image_points)
            y_min = min(p.y() for p in image_points)
            x_max = max(p.x() for p in image_points)
            y_max = max(p.y() for p in image_points)
            shape.add_point(QPointF(x_min, y_min))
            shape.add_point(QPointF(x_max, y_min))
            shape.add_point(QPointF(x_max, y_max))
            shape.add_point(QPointF(x_min, y_max))
            shape.close()

            mainwindow.canvas.shapes.append(shape)
            mainwindow.add_label(shape)
            mainwindow.canvas.update()
            mainwindow.set_dirty()
            mainwindow.actions.create.setEnabled(True)
            mainwindow.status("ROI annotated and homography saved.")

        # If using camera feed, inform the camera manager
        if hasattr(mainwindow, "camera_manager") and mainwindow.camera_manager.current_camera_thread:
            shelve_id = shelve_data["id"]
            mainwindow.camera_manager.current_camera_thread.set_homography(homography_matrix.tolist(), shelve_id)
    else:
        mainwindow.error_message("Homography Hesaplanırken Hata oluştu", "Matrix hesaplanamıyor")


def annotate_roi(mainwindow, image_points):
    # Convert to x and y arrays for convenience
    xs = [p.x() for p in image_points]
    ys = [p.y() for p in image_points]

    min_x, max_x = min(xs), max(xs)
    min_y, max_y = min(ys), max(ys)

    # Create a shape (rectangle) to mark the ROI
    mainwindow.canvas.set_editing(False)  # switch to create mode
    mainwindow.canvas.set_shape_type("rectangle")

    # Add to canvas and label list
    generate_color = generate_color_by_text(mainwindow.current_shelve_name)

    # Get shelve id (REYON_NO) from the current selection
    shelve_id = mainwindow.shelve_dimensions[mainwindow.current_shelve_name]["id"]
    width_cm = mainwindow.shelve_dimensions[mainwindow.current_shelve_name]["width_cm"]
    height_cm = mainwindow.shelve_dimensions[mainwindow.current_shelve_name]["height_cm"]

    # Create a fixed rectangle shape directly
    roi_shape = Shape.create(
        label=mainwindow.current_shelve_name,
        shape_type="rectangle",
        # Visual properties
        line_color=generate_color,
        paint_label=False,
        # Shelve properties
        shelve_id=shelve_id,  # Use REYON_NO as shelve_id
        shelve_width_cm=width_cm,
        shelve_height_cm=height_cm,
        # Other properties
        difficult=False,
        fixed=True,
        points=[(min_x, min_y), (max_x, min_y), (max_x, max_y), (min_x, max_y)],
    )

    roi_shape.close()

    mainwindow.canvas.shapes.append(roi_shape)
    mainwindow.canvas.update()

    mainwindow.add_label(roi_shape)
    mainwindow.set_dirty()

    # Switch back to edit mode
    mainwindow.canvas.set_editing(True)
    mainwindow.actions.create.setEnabled(True)
    mainwindow.status("ROI annotated and homography saved.")


def map_bbox_to_real_world(bbox, homography_matrix, shelve_data):

    real_world_width = shelve_data["width_cm"]
    real_world_height = shelve_data["height_cm"]
    real_points = shelve_data["real_world_points"]
    # Convert bbox to homogeneous coordinates
    box_pts = np.array(
        [[bbox[0], bbox[1]], [bbox[0], bbox[3]], [bbox[2], bbox[1]], [bbox[2], bbox[3]]],  # Top-left  # Bottom-left  # Top-right  # Bottom-right
        dtype=np.float32,
    ).reshape(-1, 1, 2)

    # Apply homography to transform pixel coordinates to real-world coordinates
    real_pts = cv2.perspectiveTransform(box_pts, homography_matrix).reshape(-1, 2)
    real_x_min = np.min(real_pts[:, 0])
    real_y_min = np.min(real_pts[:, 1])
    real_x_max = np.max(real_pts[:, 0])
    real_y_max = np.max(real_pts[:, 1])

    print(f"map_bbox_to_real_world: ({real_x_min,real_x_max}),({real_y_min,real_y_max})")

    real_bbox = [real_world_width - real_x_max, real_world_width - real_x_min, real_world_height - real_y_max, real_world_height - real_y_min]
    return real_bbox
