import argparse  # Re-added argparse import
import codecs  # Re-added codecs import

# Keep os import (it's duplicated but harmless, keep one)
import csv
import datetime
import logging
import os
import sys  # Re-added sys import

import cv2
import numpy as np

# Import core logging
from core import get_logger, setup_logging
from libs.labelFile import LabelFileFormat  # Keep LabelFileFormat
from PyQt5.QtGui import QImageReader  # Removed QColor import
from PyQt5.QtWidgets import QApplication  # Re-added QApplication import

# Set up logging
setup_logging()
logger = get_logger("utility_functions")

from libs.shape import Shape
from libs.utils import natural_sort, new_icon


def run_main_app(windowApp, __appname__, dependencies, argv=None):
    """
    Standard boilerplate Qt application code.
    Do everything but app.exec_() -- so that we can test the application in one thread
    """
    if not argv:
        argv = []

    app = QApplication(argv)
    app.setApplicationName(__appname__)
    app.setWindowIcon(new_icon("app"))

    # Argument parsing
    argparser = argparse.ArgumentParser()
    argparser.add_argument("image_dir", nargs="?")
    argparser.add_argument("save_dir", nargs="?")

    # Determine the base directory as the directory containing the main script
    if hasattr(sys, "frozen"):
        # If the application is frozen (e.g., packaged with PyInstaller)
        base_dir = os.path.dirname(sys.executable)
    else:
        # Normal execution
        base_dir = os.path.dirname(os.path.abspath(sys.argv[0]))

    argparser.add_argument(
        "class_file",
        default=os.path.join(base_dir, "data", "predefined_classes.txt"),
        nargs="?",
    )

    argparser.add_argument(
        "shelve_file",
        default=os.path.join(base_dir, "data", "predefined_shelves.txt"),
        nargs="?",
    )

    args = argparser.parse_args(argv[1:])

    args.image_dir = args.image_dir and os.path.normpath(args.image_dir)
    args.class_file = args.class_file and os.path.normpath(args.class_file)
    args.shelve_file = args.shelve_file and os.path.normpath(args.shelve_file)
    args.save_dir = args.save_dir and os.path.normpath(args.save_dir)

    # Create window with dependencies
    win = windowApp(
        __appname__,
        dependencies["file_handler"],
        dependencies["setting_handler"],
        args.image_dir,
        args.class_file,
        args.shelve_file,
        args.save_dir,
    )
    win.show()
    return app, win


def read(filename, default=None):
    try:
        reader = QImageReader(filename)
        reader.setAutoTransform(True)
        return reader.read()
    except:
        return default


def scan_all_images(folder_path):
    extensions = [".%s" % fmt.data().decode("ascii").lower() for fmt in QImageReader.supportedImageFormats()]
    images = []

    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.lower().endswith(tuple(extensions)):
                relative_path = os.path.join(root, file)
                path = os.path.abspath(relative_path)
                images.append(path)
    natural_sort(images, key=lambda x: x.lower())
    return images


def load_predefined_classes(self, predef_classes_file):
    if os.path.exists(predef_classes_file) is True:
        with codecs.open(predef_classes_file, "r", "utf8") as f:
            for line in f:
                line = line.strip()
                if self.label_hist is None:
                    self.label_hist = [line]
                else:
                    self.label_hist.append(line)
        self.default_label = self.label_hist[0]


def get_format_meta(format) -> tuple[str, str]:
    """
    returns a tuple containing (title, icon_name) of the selected format
    """
    if format == LabelFileFormat.PASCAL_VOC:
        return "&PascalVOC", "format_voc"
    elif format == LabelFileFormat.YOLO:
        return "&YOLO", "format_yolo"
    elif format == LabelFileFormat.CREATE_ML:
        return "&CreateML", "format_createml"
    return "&PascalVOC", "format_voc"


def load_shelve_data(main_window, file_path):
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Raf bilgileri Okunamadı: {file_path}")

    main_window.shelve_dimensions = {}  # Clear existing shelve dimensions

    with open(file_path, mode="r", newline="", encoding="utf-8") as tsvfile:
        # Use tab as delimiter and handle the actual column names from the file
        reader = csv.DictReader(tsvfile, delimiter="\t")

        for row in reader:
            try:
                # Extract data from the file format
                magaza_no = row["MAGAZA_NO"].strip()
                reyon_no = row["REYON_NO"].strip()
                name = row["REYON_ADI"].strip()

                # Get shelve width based on "REYON UZUNLUĞU (Fiili)"
                width_meters = row["REYON UZUNLUĞU (Fiili)"].strip().strip('"')  # Remove quotes if present
                if width_meters in ["YOK", "", None]:
                    continue

                if "," in width_meters:  # Handle comma as decimal separator
                    width_meters = width_meters.replace(",", ".")

                if width_meters in ["YOK", ""] or width_meters is None:
                    continue

                # Convert width from meters to centimeters with precision
                try:
                    width_cm = float(width_meters) * 100
                except ValueError:
                    logging.error(f"Invalid width value for shelf {name}: {width_meters}")
                    continue

                # Get the shelve height based on "RAF KAT SAYISI (Fiili)"
                shelve_count = row["RAF KAT SAYISI (Fiili)"].strip().strip('"')  # Remove quotes if present
                if shelve_count in ["YOK", "", None]:
                    shelve_count = 0
                else:
                    try:
                        shelve_count = int(shelve_count)
                    except ValueError:
                        logging.error(f"Invalid shelf count for shelf {name}: {shelve_count}")
                        shelve_count = 0

                # Calculate height by multiplying shelf count by 52cm per shelf
                height_cm = shelve_count * 52 if shelve_count > 0 else 260  # Default height if no shelf count

                # Create a unique identifier following the format {magaza_no}_{reyon_no}_{name}
                shelve_index = f"{magaza_no}_{reyon_no}_{name}"

                main_window.shelve_dimensions[shelve_index] = {
                    "id": reyon_no,
                    "name": name,
                    "magaza_no": magaza_no,
                    "height_cm": height_cm,
                    "width_cm": width_cm,
                    "shelve_count": shelve_count,
                    "real_world_points": [
                        (0, 0),  # p1: Top-Left
                        (0, height_cm),  # p2: Bottom-Left
                        (width_cm, 0),  # p3: Top-Right
                        (width_cm, height_cm),  # p4: Bottom-Right
                    ],
                }
            except KeyError as e:
                logging.error(f"Missing expected column in TSV: {e}")
            except ValueError as e:
                logging.error(f"Invalid data type in TSV for row {row}: {e}")


def is_frame_too_dark(frame, threshold=50):
    """
    Determines if the frame is too dark based on the average brightness.
    :param frame: The input image frame.
    :param threshold: The brightness threshold below which the frame is considered too dark.
    :return: True if the frame is too dark, False otherwise.
    """
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    avg_brightness = np.mean(gray)
    return avg_brightness < threshold


def enhance_frame(frame, camera_name=None, camera_save_dir=None):
    """
    Enhances the frame using various image processing techniques to improve visibility in low-light conditions.
    """
    # Convert to YUV color space
    yuv = cv2.cvtColor(frame, cv2.COLOR_BGR2YUV)

    # Apply CLAHE to the Y channel
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    yuv[:, :, 0] = clahe.apply(yuv[:, :, 0])

    # Convert back to BGR
    enhanced_frame = cv2.cvtColor(yuv, cv2.COLOR_YUV2BGR)

    # Apply Gamma Correction
    gamma = 1.5  # Adjust gamma value as needed
    look_up_table = np.array([((i / 255.0) ** gamma) * 255 for i in range(256)]).astype("uint8")
    enhanced_frame = cv2.LUT(enhanced_frame, look_up_table)

    # Apply Contrast Stretching using numpy to avoid cv2.normalize overload issues
    gray = cv2.cvtColor(enhanced_frame, cv2.COLOR_BGR2GRAY)
    p2, p98 = np.percentile(gray, (2, 98))
    enhanced_frame = histogram_equalization(enhanced_frame)

    # Apply Edge Enhancement
    enhanced_frame = edge_enhancement(enhanced_frame)

    # Denoise the image
    enhanced_frame = cv2.fastNlMeansDenoisingColored(enhanced_frame, None, 10, 10, 7, 21)

    # Optional: Save the enhanced frame for verification
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S_%f")
    if camera_name is not None and camera_save_dir is not None:
        filename = f"{camera_name}_enhanced_{timestamp}.jpg"
        file_path = os.path.join(camera_save_dir + "_enhanced", filename)
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        cv2.imwrite(file_path, enhanced_frame)
        logging.info(f"Saved enhanced frame: {file_path}")

    return enhanced_frame


def is_grayscale(frame, tolerance=10):
    """
    Checks if the frame is grayscale by comparing the color channels.
    :param frame: The input image frame.
    :param tolerance: Allowed difference between channels to consider as grayscale.
    :return: True if grayscale, False otherwise.
    """
    b, g, r = cv2.split(frame)
    # Calculate the absolute difference between channels
    diff_bg = cv2.absdiff(b, g)
    diff_br = cv2.absdiff(b, r)
    diff_gr = cv2.absdiff(g, r)
    # If differences are below the tolerance for all pixels, it's grayscale
    if np.all(diff_bg < tolerance) and np.all(diff_br < tolerance) and np.all(diff_gr < tolerance):
        return True
    return False


def enhance_grayscale_frame(frame):
    """
    Enhances a grayscale frame to improve detection.
    :param frame: The input grayscale image frame with three channels.
    :return: Enhanced color image frame.
    """
    try:
        # Ensure frame is a valid numpy array
        if not isinstance(frame, np.ndarray):
            logging.error("Invalid frame format: not a numpy array")
            return frame

        # Check frame dimensions
        if frame.size == 0 or len(frame.shape) < 2:
            logging.error(f"Invalid frame dimensions: {frame.shape}")
            return frame

        # If frame is already single channel, use it directly
        if len(frame.shape) == 2:
            gray = frame
        else:
            # Convert to single channel if it's a 3-channel image
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # Apply CLAHE
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced_gray = clahe.apply(gray)

        # Apply Gamma Correction
        gamma = 1.5  # Adjust as needed
        look_up_table = np.array([((i / 255.0) ** gamma) * 255 for i in range(256)]).astype("uint8")
        enhanced_gray = cv2.LUT(enhanced_gray, look_up_table)

        # Denoise
        enhanced_gray = cv2.fastNlMeansDenoising(enhanced_gray, None, h=10, templateWindowSize=7, searchWindowSize=21)

        # Convert back to three channels
        enhanced_frame = cv2.cvtColor(enhanced_gray, cv2.COLOR_GRAY2BGR)

        return enhanced_frame
    except Exception as e:
        logging.error(f"Error in enhance_grayscale_frame: {str(e)}")
        return frame  # Return original frame if enhancement fails


def save_roi_frame(self, roi_image, main_window, save_interval=100):
    """Saves the ROI image to the designated directory."""
    # Increment frame counter
    self.frame_counter += 1

    if self.frame_counter % save_interval != 1:
        return  # Skip saving this frame

    # Generate a timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S_%f")

    # Define the filename
    filename = f"{self.camera_name}_frame{self.frame_counter}_{timestamp}.jpg"
    file_path = os.path.join(main_window.camera_save_dir + "_04.03", filename)
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    # Save the image using OpenCV
    success = cv2.imwrite(file_path, roi_image)
    if success:
        # Use logging instead of print
        logging.info(f"Saved ROI frame: {file_path}")
    else:
        # Use logging instead of print
        logging.error(f"Failed to save ROI frame: {file_path}")


def contrast_stretching(image):
    """
    Applies contrast stretching to the image.
    """
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Calculate 2nd and 98th percentiles
    p2, p98 = np.percentile(gray, [2, 98])
    # Stretch contrast using numpy clipping
    stretched = np.clip((gray - p2) * 255.0 / (p98 - p2), 0, 255).astype(np.uint8)

    # Convert back to BGR
    stretched_bgr = cv2.cvtColor(stretched, cv2.COLOR_GRAY2BGR)

    return stretched_bgr


def histogram_equalization(image):
    """
    Applies histogram equalization to the image.
    """
    # Convert to YUV color space
    yuv = cv2.cvtColor(image, cv2.COLOR_BGR2YUV)

    # Apply histogram equalization on the Y channel
    yuv[:, :, 0] = cv2.equalizeHist(yuv[:, :, 0])

    # Convert back to BGR
    equalized = cv2.cvtColor(yuv, cv2.COLOR_YUV2BGR)

    return equalized


def edge_enhancement(image):
    """
    Applies edge enhancement to the image.
    """
    # Apply bilateral filter to reduce noise while keeping edges sharp
    filtered = cv2.bilateralFilter(image, d=9, sigmaColor=75, sigmaSpace=75)

    # Detect edges using the Laplacian method
    edges = cv2.Laplacian(filtered, cv2.CV_64F)
    edges = cv2.convertScaleAbs(edges)

    # Combine edges with the original image
    enhanced = cv2.addWeighted(filtered, 0.8, edges, 0.2, 0)

    return enhanced
