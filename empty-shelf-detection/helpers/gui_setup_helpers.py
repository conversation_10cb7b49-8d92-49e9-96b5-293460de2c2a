from libs.stringBundle import StringBundle
from PyQt5.QtWidgets import QDockWidget

from core import get_logger

logger = get_logger(__name__)


def load_init_settings(mainwindow):
    """Loads initial settings like string bundle and recent files."""
    try:
        mainwindow.string_bundle = StringBundle.get_bundle()
        if mainwindow.string_bundle is None:
            logger.error("StringBundle could not be loaded. Using fallback for get_str.")
            # Assign a fallback lambda that returns the ID itself or a placeholder
            mainwindow.get_str = lambda str_id: f"ERR_NO_BUNDLE({str_id})"
        else:
            # Original lambda assignment
            mainwindow.get_str = lambda str_id: mainwindow.string_bundle.get_string(str_id)
    except Exception as e:
        logger.error(f"Error loading StringBundle: {e}. Using fallback for get_str.")
        mainwindow.get_str = lambda str_id: f"ERR_LOAD_BUNDLE({str_id})"

    try:
        mainwindow.settings.initialize_recent_files(mainwindow)
    except Exception as e:
        logger.error(f"Error initializing recent files: {e}")
    # self.get_str: Callable[[str], str] = lambda str_id: self.string_bundle.get_string(str_id)


# Note: setup_dock_areas seems misplaced here as it uses 'self'.
# It likely belongs in a class, possibly MainWindow or DockManager.
# Consider moving this function to the appropriate class.
def setup_dock_areas(self):
    """Configures the features of a dock widget."""
    # Assuming 'self.dock' is a QDockWidget instance available in the context where this is called.
    try:
        dock_features = QDockWidget.DockWidgetClosable | QDockWidget.DockWidgetFloatable
        # It seems 'self.dock' is intended here, ensure it exists in the calling context.
        if hasattr(self, "dock") and isinstance(self.dock, QDockWidget):
            # Toggle features: XORing allows adding features if not present, or removing if present.
            # However, usually, you just set the desired features directly.
            # Using setFeatures directly might be clearer:
            # self.dock.setFeatures(dock_features)
            # Or to add features without removing existing ones (except mutually exclusive ones):
            # self.dock.setFeatures(self.dock.features() | dock_features)
            # The original code toggles these specific features. Let's keep that logic.
            self.dock.setFeatures(self.dock.features() ^ dock_features)
            logger.debug(f"Dock features updated for {self.dock.objectName()}.")
        else:
            logger.warning("setup_dock_areas called but 'self.dock' is not a valid QDockWidget.")
    except Exception as e:
        logger.error(f"Error setting up dock areas: {e}")
