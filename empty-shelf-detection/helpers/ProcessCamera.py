import time

import cv2
from core import get_config, get_logger
from core.detection_modes import DetectionModeManager
from PyQt5.QtCore import QThread, pyqtSignal
from PyQt5.QtGui import QImage
from utilities.utility_functions import save_roi_frame


class VideoThread(QThread):

    frame_updated = pyqtSignal(QImage)
    camera_error = pyqtSignal(str)

    def __init__(self, rtsp_url, camera_name, mainwindow):
        """
        Initialize the video processing thread.
        Args:
            rtsp_url (str): URL of the RTSP stream
            camera_name (str): Name of the camera
            mainwindow: Reference to the main window object
        """
        super().__init__()
        self.rtsp_url = rtsp_url
        self.camera_name = camera_name
        self._run_flag = True
        self._pause_flag = False
        self.first_frame = True
        self.mainwindow = mainwindow

        # Set up logger
        self.logger = get_logger(f"VideoThread_{camera_name}")

        # Camera settings
        config = get_config()
        fps_limit_cfg = config.get("camera.fps", 15)
        self.frame_rate_limit = float(fps_limit_cfg) if isinstance(fps_limit_cfg, (int, float)) and fps_limit_cfg > 0 else 15.0
        self._width = config.get("camera.frame_width", 1920)
        self._height = config.get("camera.frame_height", 1080)
        self.process_interval = 1.0 / self.frame_rate_limit  # Now safe

        # Detection and tracking
        self.model_processing = False

        # Model processing rate limiting
        self.last_model_process_time = 0

        # Store last detections for continuous display
        self.last_detections = {}

        # Frame saving
        self.frame_counter = 0
        self.save_interval = 100
        self.frame_number = 0

        self.logger.info(f"Initialized video thread for camera: {camera_name}")

    def run(self):
        # Open the RTSP stream
        cap = cv2.VideoCapture(self.rtsp_url)
        # Set desired properties (Cast width/height to int, provide default if None)
        width_to_set = int(self._width if self._width is not None else 1920)
        height_to_set = int(self._height if self._height is not None else 1080)
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, width_to_set)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height_to_set)
        cap.set(cv2.CAP_PROP_FPS, self.frame_rate_limit)
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

        # Verify if properties are set correctly
        actual_width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
        actual_height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
        actual_fps = cap.get(cv2.CAP_PROP_FPS)

        self.logger.info(f"Requested Resolution: {width_to_set}x{height_to_set}, FPS: {self.frame_rate_limit}")
        self.logger.info(f"Actual Resolution: {int(actual_width)}x{int(actual_height)}, FPS: {int(actual_fps)}")

        if not cap.isOpened():
            self.logger.error(f"Error: Cannot open the camera stream for {self.camera_name}")
            self.camera_error.emit(f"Cannot open camera: {self.camera_name}")
            return

        self.frame_number = 0

        while self._run_flag:
            if self._pause_flag:
                self.msleep(100)  # Sleep for a short duration while paused
                continue

            while cap.grab():
                ret, frame = cap.retrieve()
                if not ret:
                    self.logger.warning(f"Could not retrieve frame for {self.camera_name}")
                    self.camera_error.emit(f"Could not retrieve frame for {self.camera_name}")
                    break

                # Process frame with trackers if model processing is enabled
                if self.model_processing:
                    try:
                        processed_frame = self.process_frame_with_trackers(frame)
                        if processed_frame is not None:
                            frame = processed_frame
                    except Exception as e:
                        self.logger.error(f"Error processing frame with trackers: {str(e)}")

                qt_image = self.convert_frame_to_qimage(frame)
                self.frame_updated.emit(qt_image)

                # Increment frame number
                self.frame_number += 1

                # Limit frame rate
                self.msleep(int(1000 / self.frame_rate_limit))

        cap.release()

    def set_model_processing(self, enable):
        self.model_processing = enable

    def stop(self):
        """Sets run flag to False and waits for thread to finish"""
        self._run_flag = False
        self.wait()

    def pause(self):
        """Pauses the video feed"""
        self._pause_flag = True

    def resume(self):
        """Resumes the video feed"""
        self._pause_flag = False

    def _update_processing_time(self):
        """Update the last model processing time to the current time."""
        self.last_model_process_time = time.time()
        self.logger.debug(f"Processing model. Time since last process: {time.time() - self.last_model_process_time:.2f}s")

    def _extract_roi(self, frame, shape):
        """
        Extract the region of interest from a frame based on the given shape.

        Args:
            frame: The full frame
            shape: The ROI shape object

        Returns:
            tuple: (roi_frame, (x_min, y_min, x_max, y_max))
        """
        # Extract bounding box from shape points
        points = [(p.x(), p.y()) for p in shape.points]
        x_min = int(min(p[0] for p in points))
        y_min = int(min(p[1] for p in points))
        x_max = int(max(p[0] for p in points))
        y_max = int(max(p[1] for p in points))

        # Crop ROI from frame
        roi_frame = frame[y_min:y_max, x_min:x_max].copy()
        self.logger.debug(f"Processing ROI: x_min={x_min}, y_min={y_min}, x_max={x_max}, y_max={y_max}")

        # Save ROI frame if needed
        save_roi_frame(self, roi_frame, self.mainwindow, self.save_interval)

        return roi_frame, (x_min, y_min, x_max, y_max)

    def _should_process_frame(self):
        """
        Determine if the frame should be processed based on rate limiting.

        Returns:
            bool: True if the frame should be processed, False otherwise
        """
        # Get the current time
        current_time = time.time()

        # Get the configured processing interval (default: 1 second)
        config = get_config()
        process_interval_sec = config.get("camera.process_interval", 1.0)

        # Check if enough time has passed since the last processing
        time_since_last = current_time - self.last_model_process_time
        should_process = time_since_last >= process_interval_sec

        if not should_process:
            self.logger.debug(f"Rate limiting: {time_since_last:.2f}s < {process_interval_sec:.2f}s")

        return should_process

    def process_frame_with_trackers(self, frame):
        """
        Processes the frame by delegating to trackers from the central registry.
        Each tracker handles processing for its associated ROI.
        """
        if not self._should_process_frame():
            self.logger.debug("Skipping model processing due to rate limiting.")
            return frame

        self._update_processing_time()

        # Get trackers from DetectionModeManager for this camera
        detection_mode_manager = DetectionModeManager.get_instance()
        trackers_for_camera = {}

        tracker_registry = detection_mode_manager.get_all_trackers()
        for tracker_type, trackers in tracker_registry.items():
            if self.camera_name in trackers:
                trackers_for_camera[tracker_type] = trackers[self.camera_name]

        if not trackers_for_camera:
            self.logger.warning(f"No trackers registered for camera {self.camera_name}")
            return frame

        annotated_frame = frame.copy()

        for tracker_type, tracker in trackers_for_camera.items():
            if not hasattr(tracker, "process_frame"):
                self.logger.error(f"Tracker {tracker_type} for {self.camera_name} missing 'process_frame' method.")
                continue

            try:
                # Process the frame with the tracker
                result = tracker.process_frame(annotated_frame, self.frame_number)

                # Unpack the result if it's not None
                if result is not None and len(result) == 3:
                    detections, track_ids, _ = result  # Ignore roi_coords as we don't need it here

                    # If processing was successful, we could annotate the frame here if needed
                    if detections is not None and track_ids is not None:
                        self.logger.debug(f"Successfully processed frame with {tracker_type} tracker")
                else:
                    self.logger.debug(f"No valid results from {tracker_type} tracker")
            except Exception as e:
                self.logger.error(f"Error processing frame with {tracker_type} tracker: {str(e)}")

        return annotated_frame

    def convert_frame_to_qimage(self, frame):
        """Converts an OpenCV frame to QImage."""
        rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        if len(frame.shape) == 2 or frame.shape[2] == 1:
            frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
            self.logger.warning(f"Frame is grayscale. Converting to BGR: {frame.shape}")
        h, w, ch = rgb_image.shape
        bytes_per_line = ch * w
        return QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
