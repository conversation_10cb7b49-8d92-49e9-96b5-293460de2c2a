#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Looping Video Thread module for the empty shelf detection application.

This module extends the VideoThread class to add looping functionality for local video files.
"""

import logging
import os

import cv2
from helpers.ProcessCamera import VideoThread


class LoopingVideoThread(VideoThread):
    """
    Extended VideoThread class that supports looping for local video files.
    """

    def __init__(self, rtsp_url, camera_name, mainwindow):
        """
        Initialize the looping video thread.
        Args:
            rtsp_url (str): URL of the video file (file:// protocol)
            camera_name (str): Name of the camera
            mainwindow: Reference to the main window object
        """
        super().__init__(rtsp_url, camera_name, mainwindow)
        self.is_local_file = rtsp_url.startswith("file://")
        logging.info(f"Initialized looping video thread for camera: {camera_name}, is_local_file: {self.is_local_file}")

    def run(self):
        """
        Override the run method to add looping functionality for local video files.
        """
        # Extract the actual file path from the rtsp_url if it's a local file
        video_path = self.rtsp_url
        if self.is_local_file:
            # Remove the file:// prefix
            video_path = self.rtsp_url[7:]
            logging.info(f"Using local video file: {video_path}")

            # Check if the file exists
            if not os.path.exists(video_path):
                error_msg = f"Video file not found: {video_path}"
                logging.error(error_msg)
                self.camera_error.emit(error_msg)
                return

        # Open the video file or RTSP stream
        cap = cv2.VideoCapture(video_path)

        # Set desired properties
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, self._width)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self._height)
        cap.set(cv2.CAP_PROP_FPS, self.frame_rate_limit)
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

        # Verify if properties are set correctly
        actual_width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
        actual_height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
        actual_fps = cap.get(cv2.CAP_PROP_FPS)

        logging.info(f"Requested Resolution: {self._width}x{self._height}, FPS: {self.frame_rate_limit}")
        logging.info(f"Actual Resolution: {int(actual_width)}x{int(actual_height)}, FPS: {int(actual_fps)}")

        if not cap.isOpened():
            error_msg = f"Error: Cannot open the video source for {self.camera_name}"
            logging.error(error_msg)
            self.camera_error.emit(error_msg)
            return

        self.frame_number = 0

        while self._run_flag:
            if self._pause_flag:
                self.msleep(100)  # Sleep for a short duration while paused
                continue

            # Read a frame
            ret, frame = cap.read()

            # If we reached the end of the video and it's a local file, loop back to the beginning
            if not ret and self.is_local_file:
                logging.info(f"End of video reached for {self.camera_name}, looping back to beginning")
                cap.release()
                cap = cv2.VideoCapture(video_path)
                ret, frame = cap.read()
                if not ret:
                    error_msg = f"Error: Cannot read from video file after reopening: {self.camera_name}"
                    logging.error(error_msg)
                    self.camera_error.emit(error_msg)
                    break
            elif not ret:
                error_msg = f"Warning: Could not retrieve frame for {self.camera_name}"
                logging.warning(error_msg)
                self.camera_error.emit(error_msg)
                break

            # Check if frame is grayscale and convert to BGR if necessary
            if frame is not None:
                from utilities.utility_functions import enhance_frame, enhance_grayscale_frame, is_frame_too_dark, is_grayscale

                if is_grayscale(frame):
                    logging.debug(f"{self.camera_name}: Frame is grayscale. Converting to BGR.")
                    frame = enhance_grayscale_frame(frame)

                    if is_frame_too_dark(frame):
                        logging.warning(f"{self.camera_name}: Frame is too dark. Applying enhancements.")
                        frame = enhance_frame(frame, self.camera_name)

                # If model processing is enabled, process the frame
                if self.model_processing:
                    frame = self.process_frame_with_trackers(frame)

                qt_image = self.convert_frame_to_qimage(frame)
                self.frame_updated.emit(qt_image)

                # Increment frame number
                self.frame_number += 1

                # Limit frame rate
                self.msleep(int(1000 / self.frame_rate_limit))
            else:
                error_msg = f"Warning: Received empty frame for {self.camera_name}"
                logging.warning(error_msg)

        # Clean up
        cap.release()
