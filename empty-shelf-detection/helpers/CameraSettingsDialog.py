from PyQt5.QtWidgets import  QDialog, QVBoxLayout, QLabel, QLineEdit, QPushButton, QComboBox

class CameraSettingsDialog(QDialog):

    def __init__(self, parent=None, current_settings=None):

        super().__init__(parent)

        self.setWindowTitle("Kamera Ayarlarını Değiştir")
        self.current_settings = current_settings or {}
        self.new_settings = self.current_settings.copy()
        self.setFixedSize(400, 300)

        # Resolution Settings
        self.resolution_label = QLabel("Resolution:")
        self.resolution_combo = QComboBox()
        self.resolution_combo.addItems([
            "640x480",
            "800x600",
            "1280x720",
            "1920x1080",
            "2560x1440",
            "3840x2160"
        ])

        if current_settings and 'resolution' in current_settings:
            current_res = f"{current_settings['resolution'][0]}x{current_settings['resolution'][1]}"
            index = self.resolution_combo.findText(current_res)
            if index != -1:
                self.resolution_combo.setCurrentIndex(index)

        # Layout for the dialog
        layout = QVBoxLayout()

        # Width setting
        self.width_input = QLineEdit(str(self.current_settings.get("width", "")))
        layout.addWidget(QLabel("Width:"))
        layout.addWidget(self.width_input)

        # Height setting
        self.height_input = QLineEdit(str(self.current_settings.get("height", "")))
        layout.addWidget(QLabel("Height:"))
        layout.addWidget(self.height_input)

        # Frame rate setting
        self.frame_rate_input = QLineEdit(str(self.current_settings.get("frame_rate", "")))
        layout.addWidget(QLabel("Frame Rate:"))
        layout.addWidget(self.frame_rate_input)

        # Apply button
        apply_button = QPushButton("Apply")
        apply_button.clicked.connect(self.accept)
        layout.addWidget(apply_button)

        # Cancel button
        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.reject)
        layout.addWidget(cancel_button)

        self.setLayout(layout)

    def get_settings(self):
        """Returns the new settings entered by the user."""
        self.new_settings["width"] = int(self.width_input.text())
        self.new_settings["height"] = int(self.height_input.text())
        self.new_settings["frame_rate"] = int(self.frame_rate_input.text())
        return self.new_settings
