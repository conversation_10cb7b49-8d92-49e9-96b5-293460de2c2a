# Empty Shelf Detection

This repository contains code for detecting empty shelves using fine-tuned models such as EfficientDet, YOLOv8, and Faster R-CNN. The models are trained on a dataset of shelf images and can be used to detect empty shelves in real-time.

## Features

- Real-time detection of empty shelves in retail environments
- Support for multiple camera feeds
- GUI mode for interactive annotation and detection
- Headless mode for automated processing
- API reporting of detection results
- Offline mode for local saving of results
- Configurable detection parameters

## Project Structure

The project is organized into the following main components:

- **core**: Core functionality used by both GUI and headless modes
- **gui**: Graphical user interface components
- **helpers**: Helper functions and classes
- **managers**: Management classes for cameras, files, etc.
- **utilities**: Utility functions
- **config**: Configuration files and classes
- **data**: Data files, including videos and annotations
- **models**: Detection models

## Core Module

The `core` module provides the foundational components for the application. It is designed to be used by both the GUI and headless modes, ensuring consistent behavior and reducing code duplication.

See the [Core Module README](core/README.md) for more information.

## Configuration

The application uses a centralized configuration system based on YAML files. See [README_CONFIG.md](README_CONFIG.md) for more information.

## Running the Application

### GUI Mode

To run the application in GUI mode:

```bash
python main_gui.py
```

### Headless Mode

To run the application in headless mode:

```bash
python main_seamless.py --annotation-dir ./camera
```

Optional parameters:
- `--model-path`: Path to the empty shelf detection model
- `--people-model-path`: Path to the people detection model
- `--save-dir`: Directory to save output files
- `--camera-list`: Comma-separated list of cameras to process
- `--show-image`: Show processed images
- `--workers`: Number of worker threads
- `--offline`: Run in offline mode (save reports locally)

## Building an Executable

If you want to create an executable, use PyInstaller. The important part is to make sure the resources folder is included in the executable:

```bash
# Compile resources first
pyrcc5 -o libs/resources.py resources.qrc

# Create executable
pyinstaller --add-data "data;data" --add-data "config;config" --add-data "models;models" main_gui.py
```

## Development

The project is currently undergoing refactoring to improve code organization and maintainability. See [REFACTORING_STATUS.md](core/REFACTORING_STATUS.md) for more information.
