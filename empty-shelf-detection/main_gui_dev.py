import sys

# Import development mode
import dev_mode
import libs.resources as resources  # kaldirinca resourcelar dolmuyor.

# Import from core module
from core import get_logger, setup_logging
from gui.ROIGui import MainWindow
from libs.settings import SettingsManager
from managers.file_handler import FileHandler
from utilities.utility_functions import run_main_app

# Set up logging
setup_logging()
logger = get_logger("main_gui_dev")


def create_dependencies():
    """Create and configure all dependencies"""
    file_handler = FileHandler()
    setting_handler = SettingsManager()
    setting_handler.load()  # Ensure settings are loaded
    return {"file_handler": file_handler, "setting_handler": setting_handler}


def main():
    """
    Main entry point for the GUI development mode of the application.

    This runs the application with a graphical user interface in development mode,
    which skips certain steps to speed up development and testing.
    """
    logger.info("Starting GUI development mode")
    __appname__ = "hakmarAnnotate"

    # Initialize development mode
    logger.info("Initializing development mode")
    dev_mode.apply_dev_mode(MainWindow)

    logger.info("Creating dependencies")
    dependencies = create_dependencies()
    logger.info("Running main app")
    app, _win = run_main_app(MainWindow, __appname__, dependencies, sys.argv)
    logger.info("App initialized, starting event loop")
    return app.exec_()


if __name__ == "__main__":
    sys.exit(main())
