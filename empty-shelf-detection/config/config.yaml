# Camera settings
camera:
  buffer_size: 1
  fps: 15
  frame_height: 1080
  frame_width: 1920

# Camera configuration file
camera_config:
  file: cameras_all.json

# API settings
api:
  url: http://localhost:8000

# Logging configuration
logging:
  backup_count: 5
  format: '%(asctime)s - %(levelname)s - %(name)s - %(message)s'
  level: INFO
  log_dir: logs
  max_size: 104857600

# Model paths
model:
  empty_shelf_model: detection-models/1.best.170.pt
  people_presence_model: models/person-detection8m.pt
  people_counting_model: models/person-counting8n1.pt

  # Model manager settings
  max_cached_models: 3
  memory_threshold: 0.85  # 85% memory usage threshold

  # Model optimization settings
  use_half_precision: true  # Use FP16 precision when supported
  optimize_for_inference: true  # Apply inference optimizations

# Offline mode settings
offline_mode:
  enabled: false
  log_format: json
  save_dir: offline_reports

# File paths
paths:
  annotation_dir: ./camera
  output: ./output
  save_dir: ./output

# ROI API settings
roi_api:
  max_retries: 3
  retry_delay: 5
  timeout: 10
  url: http://*********:9980/api/ROITespitler

# ROI reporter settings
roi_reporter:
  check_interval: 10
  max_retries: 3
  report_interval: 60
  retry_delay: 5
  timeout: 10
  url: http://*********:9980/api/KisiSayisiRapor

# ROI tracker settings
roi_tracker:
  check_interval: 10
  detection_threshold: 0.25
  report_interval: 60

# Runtime settings
runtime:
  num_workers: 4
  offline_mode: false

# Shelve API settings
shelve_api:
  max_retries: 3
  retry_delay: 5
  timeout: 10
  url: http://*********:9980/api/RaftakiBosKonumlar

# Offline reporting settings
offline_reporting:
  save_dir: offline_reports

# Tracker settings
tracker:
  # Individual person tracker settings
  individual_person:
    iou_threshold: 0.4
    max_missing_frames: 5
    check_interval: 10  # minutes
    report_interval: 10  # minutes

  # Presence tracker settings
  presence:
    absence_threshold: 0.9
    check_interval: 10  # minutes
    detection_threshold: 0.4
    history_size: 10
    min_state_duration: 5.0
    person_class_id: 0
    presence_threshold: 0.7
    report_interval: 1  # minutes

  # Shelve tracker settings
  shelve:
    api_report_interval: 0.5  # minutes
    api_section: shelve_api
    api_url_key: url
    default_api_url: http://*********:9980/api/RaftakiBosKonumlar
    detection_threshold: 0.25
    empty_threshold: 5
    iou_threshold: 0.3
    max_missing_frames: 3
    min_report_periods: 5
    offline_report_section: offline_reporting
    offline_save_dir_key: save_dir
    save_local_report_with_image: false
    check_interval: 1  # minutes

# Detection modes configuration
detection_modes:
  empty_shelf:
    description: Empty shelf detection
    model_key: empty_shelf_model
    roi_type: emptyshelve
    tracker_type: shelve

  presence:
    description: People presence detection
    model_key: people_presence_model
    roi_type: presence_check
    tracker_type: person

  people_counting:
    description: People counting
    model_key: people_counting_model
    roi_type: people_counting
    tracker_type: person_count
# Development mode settings
dev_mode:
  # Auto-detection settings
  auto_enable_detection: true
  auto_start_camera: true
  auto_start_delay_ms: 1000
  bypass_startup_dialog: true
  create_annotation: false
  default_camera: People Presence Demo
  detection_delay_ms: 2000
  enabled: true

  # ROI settings for development mode
  roi:
    height_percent: 0.5
    label: Auto Presence Check
    roi_type: presence_check
    width_percent: 0.5
    x_percent: 0.25
    y_percent: 0.25

  # Test camera settings
  test_camera_index: 0
  use_test_cameras: true

# Default values for config.get() calls
defaults:
  # For core/detection_modes.py
  roi_type: presence_check
  tracker_type: person

  # For core/logging.py
  logging.level: INFO
