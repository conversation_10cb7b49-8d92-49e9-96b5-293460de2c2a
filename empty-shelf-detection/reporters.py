import json
import logging
import os
import time
from datetime import datetime
from typing import Optional

import numpy as np
import requests

from core import get_config, get_logger

# Use a generic logger setup or get specific logger if utility exists
logger = get_logger(__name__)  # Basic logger setup


class ApiReporter:
    """<PERSON><PERSON> sending reports to an HTTP API with retry logic."""

    def __init__(self, config=None, api_section="api", api_url_key="url", default_url=None):
        """
        Initializes the ApiReporter.

        Args:
            config (AppConfig): AppConfig instance.
            api_section (str): Config section for API settings.
            api_url_key (str): Config key for API URL within the section.
            default_url (str, optional): Default URL if not found in config.
        """
        self.config = config if config is not None else get_config() if config is not None else get_config()

        # Use dot notation for config keys
        self.api_url = self.config.get(f"{api_section}.{api_url_key}", default_url)
        self.timeout = self.config.get(f"{api_section}.timeout", 10)  # Default timeout 10 seconds

        # Get retry settings with validation - ensure max_retries is int
        max_retries_config = self.config.get(f"{api_section}.max_retries", 3)
        if not isinstance(max_retries_config, int) or max_retries_config < 0:
            logger.warning(f"Invalid max_retries value ({max_retries_config}) in config section '{api_section}', using default 3.")
            self.max_retries = 3
        else:
            self.max_retries = max_retries_config

        retry_delay_config = self.config.get(f"{api_section}.retry_delay", 5)  # Base delay in seconds
        if not isinstance(retry_delay_config, (int, float)) or retry_delay_config <= 0:
            logger.warning(f"Invalid retry_delay value ({retry_delay_config}) in config section '{api_section}', using default 5.")
            self.retry_delay = 5.0  # Use float for potential calculations
        else:
            self.retry_delay = float(retry_delay_config)  # Ensure float

        if not self.api_url:
            logger.error(f"API URL is not configured (section: '{api_section}', key: '{api_url_key}'). API Reporter will not function.")

        logger.info(f"ApiReporter initialized. URL: {self.api_url}, Timeout: {self.timeout}, Retries: {self.max_retries}, Delay: {self.retry_delay}s")

    def send(self, payload: dict) -> bool:
        """
        Sends the payload to the configured API endpoint with retry logic.
        Checks the global offline_mode flag in config before sending.

        Args:
            payload (dict): The JSON payload to send.

        Returns:
            bool: True if the request was successful (e.g., 200 OK), False otherwise.
        """
        # Check global offline mode flag from AppConfig
        is_offline = self.config.get("runtime.offline_mode", False)
        if is_offline:
            logger.info(f"Offline mode enabled. Skipping API report send to {self.api_url}.")
            return False  # Indicate failure to send (as expected in offline mode)

        if not self.api_url:
            logger.error("Cannot send report: API URL is not configured.")
            return False

        headers = {"Content-Type": "application/json"}
        logger.debug(f"Attempting to send payload to {self.api_url}: {json.dumps(payload, indent=2)}")

        for attempt in range(self.max_retries):
            try:
                response = requests.post(self.api_url, json=payload, headers=headers, timeout=self.timeout)

                if response.status_code == 200:
                    logger.info(f"Successfully sent report to API: {self.api_url}")
                    return True
                else:
                    logger.warning(f"API request failed (attempt {attempt + 1}/{self.max_retries}): {response.status_code} - {response.text}")
                    if attempt < self.max_retries - 1:
                        current_delay = self.retry_delay * (2**attempt)  # Exponential backoff
                        logger.info(f"Retrying API call in {current_delay:.2f} seconds...")
                        time.sleep(current_delay)

            except requests.exceptions.RequestException as e:
                logger.warning(f"API request error (attempt {attempt + 1}/{self.max_retries}): {str(e)}")
                if attempt < self.max_retries - 1:
                    current_delay = self.retry_delay * (2**attempt)  # Exponential backoff
                    logger.info(f"Retrying API call in {current_delay:.2f} seconds...")
                    time.sleep(current_delay)

        logger.error(f"Failed to send report to API {self.api_url} after {self.max_retries} attempts.")
        return False


class OfflineReporter:
    """Handles saving reports and optional images locally."""

    def __init__(self, config=None, report_section="offline_reporting", save_dir_key="save_dir", default_save_dir="offline_reports"):
        """
        Initializes the OfflineReporter.

        Args:
            config (AppConfig): AppConfig instance.
            report_section (str): Config section for offline reporting settings.
            save_dir_key (str): Config key for the base save directory.
            default_save_dir (str): Default directory if not found in config.
        """
        self.config = config if config is not None else get_config() if config is not None else get_config()
        base_save_dir_config = self.config.get(f"{report_section}.{save_dir_key}", default_save_dir)

        if not isinstance(base_save_dir_config, str):
            logger.warning(f"Invalid save_dir value ({base_save_dir_config}) in config section '{report_section}', using default '{default_save_dir}'.")
            self.base_save_dir = default_save_dir
        else:
            self.base_save_dir = base_save_dir_config

        # Ensure the base directory exists
        try:
            os.makedirs(self.base_save_dir, exist_ok=True)
            logger.info(f"OfflineReporter initialized. Reports will be saved under: {self.base_save_dir}")
        except OSError as e:
            logger.error(f"Failed to create base offline reports directory {self.base_save_dir}: {e}. OfflineReporter may not function.")
            self.base_save_dir = None  # Indicate failure

    def save(self, report_data: dict, camera_name: str, report_id: str, frame: Optional[np.ndarray] = None) -> bool:
        """
        Saves the report data as JSON and optionally saves the frame as JPG.

        Args:
            report_data (dict): The report data (payload) to save.
            camera_name (str): The name of the camera for sub-directory creation.
            report_id (str): The unique identifier for this report (used for filenames).
            frame (np.ndarray, optional): The image frame to save. Defaults to None.

        Returns:
            bool: True if the report (and image, if applicable) was saved successfully, False otherwise.
        """
        if not self.base_save_dir:
            logger.error("Cannot save report: Base save directory is not configured or accessible.")
            return False

        camera_dir = None  # Initialize for use in except block
        try:
            # Create camera-specific directory
            camera_dir = os.path.join(self.base_save_dir, camera_name)
            os.makedirs(camera_dir, exist_ok=True)

            # --- Save JSON Report ---
            report_path = os.path.join(camera_dir, f"{report_id}.json")
            # Add report_id and potentially other meta-data if not already present
            report_data_to_save = report_data.copy()
            if "report_id" not in report_data_to_save:
                report_data_to_save["report_id"] = report_id
            if "save_timestamp" not in report_data_to_save:
                report_data_to_save["save_timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
            if "camera_name" not in report_data_to_save:
                report_data_to_save["camera_name"] = camera_name

            json_saved = False  # Initialize flag
            try:
                with open(report_path, "w") as f:
                    json.dump(report_data_to_save, f, indent=2)
                logger.info(f"Saved offline report JSON to {report_path}")
                json_saved = True
            except IOError as e:
                logger.error(f"Failed to save offline report JSON to {report_path}: {e}")
                # Continue to attempt image saving if requested, but overall save will fail

            # --- Save Image (Optional) ---
            image_saved = True  # Assume success if no frame provided
            image_path = None  # Initialize
            if frame is not None:
                image_path = os.path.join(camera_dir, f"{report_id}.jpg")
                try:
                    # Check if cv2 is available (optional import)
                    import cv2

                    success = cv2.imwrite(image_path, frame)
                    if success:
                        logger.info(f"Saved offline report image to {image_path}")
                        # Optionally add image_path to the saved JSON data (read-modify-write)
                        if json_saved:  # Only update JSON if it was saved successfully initially
                            try:
                                report_data_to_save["image_path"] = os.path.relpath(image_path, self.base_save_dir)  # Store relative path
                                with open(report_path, "w") as f:
                                    json.dump(report_data_to_save, f, indent=2)
                            except IOError as e_update:
                                logger.warning(f"Could not update JSON {report_path} with image path: {e_update}")
                    else:
                        logger.error(f"Failed to save offline report image to {image_path} (cv2.imwrite returned False)")
                        image_saved = False
                except ImportError:
                    logger.warning(f"OpenCV (cv2) not available, cannot save image {image_path}")
                    image_saved = False  # Cannot save image
                except Exception as e:
                    logger.error(f"Error saving offline report image to {image_path}: {str(e)}")
                    image_saved = False

            # --- Append to Log File (Optional but good practice) ---
            if json_saved:  # Only log if the core report was saved
                try:
                    log_path = os.path.join(self.base_save_dir, "_reports_log.jsonl")  # Use .jsonl for line-delimited JSON
                    log_entry = {
                        "log_timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
                        "report_id": report_id,
                        "camera_name": camera_name,
                        "report_file": os.path.relpath(report_path, self.base_save_dir),
                        "image_file": os.path.relpath(image_path, self.base_save_dir) if image_path is not None and image_saved else None,
                        "object_count": len(report_data.get("objects", [])),  # Example field
                    }
                    with open(log_path, "a") as log_f:
                        log_f.write(json.dumps(log_entry) + "\n")
                except Exception as log_e:
                    logger.warning(f"Failed to append entry to offline report log {log_path}: {log_e}")

            # Return True only if JSON saved and (image saved OR no image needed)
            return json_saved and (image_saved if frame is not None else True)

        except OSError as e:
            # Use the initialized camera_dir value here
            dir_path = camera_dir if camera_dir else self.base_save_dir
            logger.error(f"Failed to create directory structure for offline report saving ({dir_path}): {e}")
            return False
        except Exception as e:
            logger.error(f"An unexpected error occurred during offline report saving: {str(e)}")
            return False
