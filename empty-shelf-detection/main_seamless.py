import argparse
import os
import sys
import time

# Add project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core import get_logger, get_model_manager, setup_logging
from core.config import get_config
from core.detection_modes import DetectionModeManager
from core.tracker_scheduler import TrackerScheduler

setup_logging()
logger = get_logger(__name__)


def parse_arguments():
    parser = argparse.ArgumentParser(description="Empty Shelve Detection - Seamless Mode (Headless)")
    parser.add_argument("--annotation-dir", help="Override annotation directory (from core/config.yaml paths.annotation_dir)")
    parser.add_argument("--show-image", action="store_true", help="Show annotated images (for debugging)")
    parser.add_argument("--save-dir", help="Override output directory (from core/config.yaml paths.save_dir)")
    parser.add_argument("--offline", action="store_true", help="Run in offline mode (overrides core/config.yaml runtime.offline_mode)")
    parser.add_argument("--workers", type=int, help="Number of parallel workers (overrides core/config.yaml runtime.num_workers)")
    parser.add_argument("--max-fps", type=float, help="Maximum frames per second per camera", default=1.0)
    parser.add_argument("--watchdog", action="store_true", help="Enable watchdog to monitor and restart failed camera threads")
    return parser.parse_args()


# --- Main Execution ---
def run_seamless_mode(
    num_workers_override=None,
    offline_mode_override=False,
    max_fps=1.0,
):
    """
    Run the application in seamless mode using the tracker-centric approach.

    Args:
        num_workers_override: Override the number of worker threads
        offline_mode_override: Override the offline mode setting
        max_fps: Maximum frames per second per camera
    """
    config = get_config()
    annotation_dir = config.get("paths.annotation_dir")
    save_dir = config.get("paths.save_dir")
    is_offline = offline_mode_override or config.get_bool("runtime.offline_mode", False)
    num_workers = num_workers_override or config.get_int("runtime.num_workers", 4)

    logger.info(f"Starting seamless mode with tracker-centric approach... (Offline Mode: {is_offline})")
    logger.info(f"Annotation Directory: {annotation_dir}")
    logger.info(f"Output Directory: {save_dir}")
    logger.info(f"Worker Threads: {num_workers}")
    logger.info(f"Max FPS per camera: {max_fps}")

    # Get the TrackerScheduler instance
    scheduler = TrackerScheduler.get_instance()

    # Configure the scheduler
    scheduler.num_workers = num_workers

    # Initialize trackers from annotations
    if not scheduler.initialize_trackers_from_annotations():
        logger.error("Failed to initialize trackers from annotations. Exiting.")
        return

    # Verify that models are loaded

    model_manager = get_model_manager()
    empty_shelf_model = model_manager.get_model("empty_shelf")
    people_model = model_manager.get_model("people_presence_model")
    people_counting_model = model_manager.get_model("people_counting_model")

    if not empty_shelf_model or not people_model or not people_counting_model:
        logger.error("A required model failed to load during initialization. Cannot start processing.")
        return

    # Get trackers from the central registry
    all_trackers = DetectionModeManager.get_instance().get_all_trackers()

    # Count the number of cameras with trackers
    camera_count = len(set(camera_name for trackers in all_trackers.values() for camera_name in trackers.keys()))
    logger.info(f"Processing {camera_count} cameras")

    try:
        # Start the scheduler
        scheduler.start()
        logger.info("Tracker scheduler started")

        # Main loop
        while True:
            try:
                time.sleep(1)
            except KeyboardInterrupt:
                logger.info("Keyboard interrupt received, shutting down...")
                break
    except Exception as e:
        logger.error(f"Error in main loop: {str(e)}", exc_info=True)
    finally:
        logger.info("Stopping tracker scheduler...")
        scheduler.stop()
        logger.info("All processing tasks completed or failed.")


if __name__ == "__main__":
    args = parse_arguments()
    run_seamless_mode(
        num_workers_override=args.workers,
        offline_mode_override=args.offline,
        max_fps=args.max_fps,
    )
