"""
Core module for Empty Shelf Detection application.
Contains shared components used by both GUI and headless modes.
"""

# Use lazy imports to avoid circular dependencies
from .config import CoreConfig, get_config
from .logging import CoreLogger, get_logger, setup_logging

# Import model manager
from .model_manager import ModelManager, get_model_manager

# Import utility functions
from .utils.metrics import compute_iou


# These imports depend on the above, so import them after
def __getattr__(name):
    """Lazy import to avoid circular dependencies."""
    if name == "AnnotationReader":
        from .annotationReader import AnnotationReader

        return AnnotationReader
    
    elif name == "DetectionModeManager":
        from .detection_modes import DetectionModeManager

        return DetectionModeManager
    raise AttributeError(f"module {__name__!r} has no attribute {name!r}")


__all__ = [
    "AnnotationReader",
    "CameraProcessor",
    "DetectionModeManager",
    "CoreConfig",
    "get_config",
    "CoreLogger",
    "setup_logging",
    "get_logger",
    "compute_iou",
    "ModelManager",
    "get_model_manager",
]
