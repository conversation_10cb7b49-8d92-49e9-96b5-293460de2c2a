#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Core Model Manager for centralized model loading and caching.

This module provides a singleton class for managing machine learning models,
with features like:
- Centralized model loading based on configuration
- Model caching to prevent duplicate loading
- Memory usage optimization
- Thread-safe access to models
- Graceful error handling
- Model warm-up functionality
"""

import gc
import os
import threading
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import psutil
import torch
from core import get_config, get_logger
from ultralytics import YOLO


class ModelManager:
    """
    Singleton class for managing machine learning models.

    This class provides centralized model loading, caching, and memory optimization
    to prevent duplicate model loading and reduce memory usage.
    """

    _instance = None
    _lock = threading.RLock()  # Reentrant lock for thread safety

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(ModelManager, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Initialize the ModelManager if not already initialized."""
        with self._lock:
            if self._initialized:
                return

            self.logger = get_logger(self.__class__.__name__)
            self.config = get_config()

            # Model cache: maps model_id to loaded model
            self._models: Dict[str, Any] = {}

            # Model metadata: maps model_id to metadata (path, type, last_used, etc.)
            self._model_metadata: Dict[str, Dict[str, Any]] = {}

            # Default model paths from config
            self._default_model_paths = {
                "empty_shelf": self.config.get("model.empty_shelf_model", "detection-models/1.best.170.pt"),
                "people_counting_model": self.config.get("model.people_counting_model", "models/person-counting8n1.pt"),
                "people_presence_model": self.config.get("model.people_presence_model", "models/person-detection8m.pt"),
            }

            # Memory management settings
            self._max_models = self.config.get_int("model.max_cached_models", 3)
            self._memory_threshold = self.config.get_float("model.memory_threshold", 0.85)  # 85% memory usage threshold

            # Initialize memory stats
            self._update_memory_stats()

            self.logger.info(f"ModelManager initialized with {len(self._default_model_paths)} default models")
            self.logger.info(f"Memory management: max_models={self._max_models}, threshold={self._memory_threshold * 100:.1f}%")
            self.logger.info(f"Current memory usage: {self._memory_usage * 100:.1f}% ({self._used_memory_mb:.1f}MB/{self._total_memory_mb:.1f}MB)")

            self._initialized = True

    @classmethod
    def get_instance(cls) -> "ModelManager":
        """Get the singleton instance of ModelManager."""
        if cls._instance is None:
            cls._instance = ModelManager()
        return cls._instance

    def _update_memory_stats(self) -> None:
        """Update memory usage statistics."""
        memory = psutil.virtual_memory()
        self._total_memory_mb = memory.total / (1024 * 1024)  # Convert to MB
        self._used_memory_mb = memory.used / (1024 * 1024)  # Convert to MB
        self._memory_usage = memory.percent / 100.0  # Convert to ratio (0-1)

    def _is_memory_critical(self) -> bool:
        """Check if memory usage is above the threshold."""
        self._update_memory_stats()
        return self._memory_usage > self._memory_threshold

    def _get_model_memory_usage(self, model_id: str) -> float:
        """
        Estimate memory usage of a model in MB.

        This is an approximation based on PyTorch's memory tracking.
        """
        if model_id not in self._models:
            return 0.0

        model = self._models[model_id]

        # For PyTorch models
        if hasattr(model, "model") and hasattr(model.model, "parameters"):
            # YOLO models from Ultralytics have a nested model attribute
            params_mb = sum(p.numel() * p.element_size() for p in model.model.parameters()) / (1024 * 1024)
            buffers_mb = sum(b.numel() * b.element_size() for b in model.model.buffers()) / (1024 * 1024)
            return params_mb + buffers_mb

        # For other model types, provide a rough estimate
        return 500.0  # Default estimate for unknown model types (500MB)

    def _manage_memory(self) -> None:
        """
        Manage memory by unloading least recently used models if needed.

        This method is called before loading a new model to ensure
        we don't exceed memory limits.
        """
        if not self._is_memory_critical() and len(self._models) < self._max_models:
            return

        self.logger.warning(f"Memory management triggered: usage={self._memory_usage * 100:.1f}%, " f"models_loaded={len(self._models)}/{self._max_models}")

        # If we have too many models or memory usage is high, unload the least recently used model
        if len(self._models) > 0:
            # Sort models by last_used timestamp (oldest first)
            sorted_models = sorted(self._model_metadata.items(), key=lambda x: x[1].get("last_used", 0))

            # Unload the oldest model
            oldest_model_id = sorted_models[0][0]
            self.unload_model(oldest_model_id)

            # Force garbage collection
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            self._update_memory_stats()
            self.logger.info(f"After unloading model: usage={self._memory_usage * 100:.1f}%, " f"models_loaded={len(self._models)}/{self._max_models}")

    def get_model(self, model_id: str = None, model_path: str = None) -> Optional[Any]:
        """
        Get a model by ID or path, loading it if necessary.

        Args:
            model_id: Identifier for the model (e.g., "empty_shelf", "people_presence_model", "people_counting_model")
            model_path: Direct path to the model file (overrides model_id)

        Returns:
            The loaded model or None if loading failed
        """
        with self._lock:
            # Determine the model path
            if model_path is None and model_id is None:
                self.logger.error("Either model_id or model_path must be provided")
                return None

            if model_path is None:
                # Use model_id to get path from defaults
                if model_id in self._default_model_paths:
                    model_path = self._default_model_paths[model_id]
                else:
                    self.logger.error(f"Unknown model_id: {model_id}")
                    return None

            # Use the path as the ID if no ID was provided
            if model_id is None:
                model_id = model_path

            # Check if model is already loaded
            if model_id in self._models:
                # Update last_used timestamp
                self._model_metadata[model_id]["last_used"] = time.time()
                self.logger.debug(f"Using cached model: {model_id}")
                return self._models[model_id]

            # Manage memory before loading a new model
            self._manage_memory()

            # Load the model
            try:
                self.logger.info(f"Loading model: {model_id} from {model_path}")

                # Validate model path
                if not os.path.isfile(model_path):
                    self.logger.error(f"Model file not found: {model_path}")
                    return None

                # Load the model based on file extension
                if model_path.endswith((".pt", ".pth", ".weights")):
                    # Load YOLO model
                    start_time = time.time()
                    model = YOLO(model_path)
                    load_time = time.time() - start_time

                    # Store model in cache
                    self._models[model_id] = model

                    # Store metadata
                    self._model_metadata[model_id] = {
                        "path": model_path,
                        "type": "yolo",
                        "last_used": time.time(),
                        "load_time": load_time,
                    }

                    # Log memory usage after loading
                    self._update_memory_stats()
                    model_size_mb = self._get_model_memory_usage(model_id)

                    self.logger.info(
                        f"Model {model_id} loaded in {load_time:.2f}s. "
                        f"Estimated size: {model_size_mb:.1f}MB. "
                        f"Memory usage: {self._memory_usage * 100:.1f}%"
                    )

                    # Warm up the model
                    self._warm_up_model(model_id)

                    return model
                else:
                    self.logger.error(f"Unsupported model format: {model_path}")
                    return None

            except Exception as e:
                self.logger.error(f"Error loading model {model_id}: {str(e)}", exc_info=True)
                return None

    def _warm_up_model(self, model_id: str) -> None:
        """
        Warm up a model with a dummy input to initialize lazy-loaded components.

        Args:
            model_id: Identifier for the model to warm up
        """
        if model_id not in self._models:
            return

        model = self._models[model_id]
        model_type = self._model_metadata[model_id].get("type")

        try:
            if model_type == "yolo":
                # Create a dummy input (black image)
                dummy_input = np.zeros((640, 640, 3), dtype=np.uint8)

                # Run inference on dummy input
                self.logger.debug(f"Warming up model {model_id}...")
                start_time = time.time()
                _ = model.predict(dummy_input, verbose=False)
                warm_up_time = time.time() - start_time

                self.logger.debug(f"Model {model_id} warmed up in {warm_up_time:.2f}s")

                # Update metadata
                self._model_metadata[model_id]["warm_up_time"] = warm_up_time

        except Exception as e:
            self.logger.warning(f"Error warming up model {model_id}: {str(e)}")

    def unload_model(self, model_id: str) -> bool:
        """
        Unload a model from memory.

        Args:
            model_id: Identifier for the model to unload

        Returns:
            True if the model was unloaded, False otherwise
        """
        with self._lock:
            if model_id not in self._models:
                return False

            # Get model size before unloading
            model_size_mb = self._get_model_memory_usage(model_id)

            # Remove model from cache
            model = self._models.pop(model_id)

            # Clean up PyTorch model
            if hasattr(model, "model") and hasattr(model.model, "cpu"):
                model.model.cpu()

            # Remove metadata
            metadata = self._model_metadata.pop(model_id)

            # Log unloading
            self.logger.info(f"Unloaded model {model_id} ({metadata.get('path')}). " f"Estimated size: {model_size_mb:.1f}MB")

            # Force garbage collection
            del model
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            return True

    def unload_all_models(self) -> None:
        """Unload all models from memory."""
        with self._lock:
            model_ids = list(self._models.keys())
            for model_id in model_ids:
                self.unload_model(model_id)

    def get_loaded_models(self) -> List[str]:
        """Get a list of currently loaded model IDs."""
        with self._lock:
            return list(self._models.keys())

    def get_model_info(self, model_id: str = None) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """
        Get information about loaded models.

        Args:
            model_id: Optional model ID to get info for a specific model

        Returns:
            Dictionary with model information or list of dictionaries for all models
        """
        with self._lock:
            if model_id is not None:
                if model_id not in self._model_metadata:
                    return {}

                info = self._model_metadata[model_id].copy()
                info["id"] = model_id
                info["memory_mb"] = self._get_model_memory_usage(model_id)
                return info

            # Return info for all models
            result = []
            for mid in self._models:
                info = self._model_metadata[mid].copy()
                info["id"] = mid
                info["memory_mb"] = self._get_model_memory_usage(mid)
                result.append(info)

            return result

    def get_memory_stats(self) -> Dict[str, float]:
        """Get current memory usage statistics."""
        with self._lock:
            self._update_memory_stats()
            return {
                "total_mb": self._total_memory_mb,
                "used_mb": self._used_memory_mb,
                "usage_percent": self._memory_usage * 100,
                "models_loaded": len(self._models),
                "max_models": self._max_models,
            }

    def get_default_model_path(self, model_id: str) -> Optional[str]:
        """
        Get the default path for a model ID.

        Args:
            model_id: Identifier for the model

        Returns:
            Path to the model or None if the ID is unknown
        """
        with self._lock:
            return self._default_model_paths.get(model_id)

    def set_default_model_path(self, model_id: str, model_path: str) -> None:
        """
        Set the default path for a model ID.

        Args:
            model_id: Identifier for the model
            model_path: Path to the model file
        """
        with self._lock:
            self._default_model_paths[model_id] = model_path
            self.logger.info(f"Set default path for model {model_id}: {model_path}")


# Convenience function to get the ModelManager instance
def get_model_manager() -> ModelManager:
    """Get the singleton instance of ModelManager."""
    return ModelManager.get_instance()
