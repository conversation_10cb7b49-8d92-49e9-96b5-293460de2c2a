"""
Unified reporting service for all trackers.

This module provides a centralized reporting service that:
1. Handles API reporting
2. Handles offline reporting with images
3. Provides a consistent interface for all trackers
"""

import base64
import json
import os
import time
from datetime import datetime
from typing import Any, Dict, Optional

import cv2
import numpy as np
import requests
from core import get_config, get_logger


class ReportingService:
    """
    Centralized reporting service for all trackers.

    This class handles both API and offline reporting with a unified interface.
    It replaces the separate ApiReporter and OfflineReporter classes.
    """

    _instance = None

    @classmethod
    def get_instance(cls):
        """Get the singleton instance."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        """Initialize the reporting service."""
        self.logger = get_logger("ReportingService")
        self.config = get_config()

        # API reporting settings
        self.api_url = self.config.get("shelve_api.url", "http://10.50.9.3:9980/api/RaftakiBosKonumlar")
        self.timeout = self.config.get("shelve_api.timeout", 10)
        self.max_retries = self.config.get("shelve_api.max_retries", 3)
        self.retry_delay = self.config.get("shelve_api.retry_delay", 5.0)

        # Offline reporting settings
        self.offline_dir = self.config.get("offline_reporting.save_dir", "offline_reports")
        os.makedirs(self.offline_dir, exist_ok=True)

        # Runtime settings
        self.is_offline = self.config.get("runtime.offline_mode", False)

        self.logger.info(f"ReportingService initialized. API URL: {self.api_url}, Offline dir: {self.offline_dir}")

    def send_report(self, payload: Dict[str, Any], camera_name: str, frame: Optional[np.ndarray] = None, save_image: bool = False) -> bool:
        """
        Send a report via API and/or save locally.

        Args:
            payload: The report data to send
            camera_name: Name of the camera for organizing offline reports
            frame: Optional frame image to save with the report and include in API payload as base64
            save_image: Whether to save the frame image with the report

        Returns:
            bool: True if the report was sent/saved successfully
        """
        # Generate a report ID if not present
        if "report_id" not in payload:
            payload["report_id"] = f"{camera_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        report_id = payload["report_id"]
        success = False

        # Try API reporting if not in offline mode
        if not self.is_offline:
            success = self._send_api_report(payload, frame)

        # Always save locally in offline mode or if save_image is True
        if self.is_offline or save_image:
            local_success = self._save_offline_report(payload, camera_name, report_id, frame)
            # In offline mode, local success determines overall success
            if self.is_offline:
                success = local_success

        return success

    def _send_api_report(self, payload: Dict[str, Any], frame: Optional[np.ndarray] = None) -> bool:
        """
        Send a report to the API endpoint.

        Args:
            payload: The report data to send
            frame: Optional frame image to include in the report

        Returns:
            bool: True if the report was sent successfully, False otherwise
        """
        if not self.api_url:
            self.logger.error("Cannot send report: API URL is not configured.")
            return False

        try:
            headers = {"Content-Type": "application/json"}

            # If frame is provided, encode it as base64 and add to payload
            if frame is not None:
                try:
                    # Encode image as JPEG
                    _, buffer = cv2.imencode(".jpg", frame)
                    # Convert to base64 string
                    base64_image = base64.b64encode(buffer).decode("utf-8")
                    # Add to payload
                    payload["image_data"] = base64_image
                    self.logger.debug("Added base64-encoded image to API payload")
                except Exception as e:
                    self.logger.error(f"Error encoding image as base64: {str(e)}")

            for attempt in range(self.max_retries):
                try:
                    response = requests.post(self.api_url, json=payload, headers=headers, timeout=self.timeout)

                    if response.status_code == 200:
                        self.logger.info(f"Successfully sent report to API: {self.api_url}")
                        return True
                    else:
                        self.logger.warning(f"API request failed (attempt {attempt + 1}/{self.max_retries}): {response.status_code}")
                        if attempt < self.max_retries - 1:
                            delay = self.retry_delay * (2**attempt)  # Exponential backoff
                            self.logger.info(f"Retrying API call in {delay:.2f} seconds...")
                            time.sleep(delay)

                except requests.exceptions.RequestException as e:
                    self.logger.warning(f"API request error (attempt {attempt + 1}/{self.max_retries}): {str(e)}")
                    if attempt < self.max_retries - 1:
                        delay = self.retry_delay * (2**attempt)
                        self.logger.info(f"Retrying API call in {delay:.2f} seconds...")
                        time.sleep(delay)

            self.logger.error(f"Failed to send report to API after {self.max_retries} attempts.")
            return False

        except ImportError:
            self.logger.error("Cannot send API report: requests module not available.")
            return False

    def _save_offline_report(self, payload: Dict[str, Any], camera_name: str, report_id: str, frame: Optional[np.ndarray] = None) -> bool:
        """Save a report locally with optional image."""
        try:
            # Create camera-specific directory
            camera_dir = os.path.join(self.offline_dir, camera_name)
            os.makedirs(camera_dir, exist_ok=True)

            # Save JSON report
            report_path = os.path.join(camera_dir, f"{report_id}.json")
            with open(report_path, "w") as f:
                json.dump(payload, f, indent=2)

            # Save image if provided
            if frame is not None:
                image_path = os.path.join(camera_dir, f"{report_id}.jpg")
                cv2.imwrite(image_path, frame)

                # Update JSON with image path
                payload["image_path"] = os.path.relpath(image_path, self.offline_dir)
                with open(report_path, "w") as f:
                    json.dump(payload, f, indent=2)

            self.logger.info(f"Saved offline report to {report_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error saving offline report: {str(e)}")
            return False
