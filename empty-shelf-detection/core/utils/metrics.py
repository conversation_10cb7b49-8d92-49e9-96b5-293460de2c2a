"""
Metrics utility functions for the core module.

This module provides functions for calculating various metrics used in the application,
such as Intersection over Union (IoU) for bounding boxes.
"""

from typing import List, Tu<PERSON>, Union
import numpy as np


def compute_iou(boxA: Union[List[float], Tuple[float, float, float, float], np.ndarray],
                boxB: Union[List[float], Tuple[float, float, float, float], np.ndarray]) -> float:
    """
    Compute the Intersection over Union (IoU) of two bounding boxes.
    
    Args:
        boxA: First bounding box in format [x1, y1, x2, y2]
        boxB: Second bounding box in format [x1, y1, x2, y2]
        
    Returns:
        float: IoU value between 0 and 1
    """
    # Convert inputs to lists if they're numpy arrays
    if isinstance(boxA, np.ndarray):
        boxA = boxA.tolist()
    if isinstance(boxB, np.ndarray):
        boxB = boxB.tolist()
        
    # Ensure both boxes have 4 coordinates
    if len(boxA) != 4 or len(boxB) != 4:
        raise ValueError("Bounding boxes must have 4 coordinates [x1, y1, x2, y2]")
    
    # Calculate intersection coordinates
    xA = max(boxA[0], boxB[0])
    yA = max(boxA[1], boxB[1])
    xB = min(boxA[2], boxB[2])
    yB = min(boxA[3], boxB[3])
    
    # Calculate intersection area
    interArea = max(0, xB - xA) * max(0, yB - yA)
    
    # Calculate union area
    boxAArea = (boxA[2] - boxA[0]) * (boxA[3] - boxA[1])
    boxBArea = (boxB[2] - boxB[0]) * (boxB[3] - boxB[1])
    
    # Calculate IoU
    denominator = float(boxAArea + boxBArea - interArea)
    iou = interArea / denominator if denominator > 0 else 0
    
    return iou
