# Refactoring Status

## Completed Refactoring Tasks

1. ⬜️ Moved `AnnotationReader` class to `core/annotation_reader.py`
2. ⬜️ Moved `CameraProcessor` class to `core/camera_processor.py`
3. ⬜️ Moved `DetectionModeManager` class to `core/detection_modes.py`
4. ⬜️ Updated imports in `main_seamless.py` to use the refactored classes from core
5. ⬜️ Properly removed original files that were moved to core
6. ⬜️ Fixed circular import issues between core modules
7. ⬜️ Updated references in GUI components to use core modules
8. ✅ Implemented centralized configuration management in `core/config.py`
9. ✅ Implemented standardized logging strategy in `core/logging.py`
10. ✅ Updated main entry points to use the new core modules:
    * Updated `main_seamless.py` to use core config and logging
    * Updated `main_gui.py` to use core config and logging
    * Updated `main_gui_dev.py` to use core config and logging
    * Updated `dev_mode.py` to use core config and logging
    * Updated `gui/ROIGui.py` to use core logging

## Next Refactoring Tasks

Based on our analysis:

1.  ✅ **Consolidate IoU Calculation**
    *   Extract `compute_iou` method from `tracker.py`, `roi_tracker.py`, and `core/camera_processor.py`.
    *   Create `core/utils/metrics.py` and place the consolidated `compute_iou` function there.
    *   Update all callers to use the utility function.
    *   Added the function to core/__init__.py for easy importing.

2.  ✅ **Refactor Tracking Components**
    *   Created `core/trackers/` directory.
    *   Moved `ShelveTracker` (from `tracker.py`) to `core/trackers/shelf_tracker.py` and renamed to `ShelfTracker`.
    *   Moved `PeoplePresenceStateTracker` to `core/trackers/presence_state_tracker.py` and renamed to `PresenceStateTracker`.
    *   Moved `IndividualPersonTracker` to `core/trackers/person_tracker.py` and renamed to `PersonTracker`.
    *   Updated all references to the old tracker classes throughout the codebase.
    *   Created a verification script to ensure the trackers work correctly.
    *   Ensured trackers use centralized config and logging.

3.  ✅ **Implement Core Model Manager**
    *   Created `core/model_manager.py` with a singleton ModelManager class.
    *   Implemented model loading based on configuration (paths/identifiers).
    *   Implemented caching for loaded models with memory usage optimization.
    *   Added memory management to unload least recently used models when memory usage is high.
    *   Implemented graceful error handling for model loading failures.
    *   Added model warm-up logic to initialize lazy-loaded components.
    *   Ensured thread safety with reentrant locks for concurrent access.
    *   Provided a clear interface with `get_model(model_id, model_path)` method.
    *   Added memory usage statistics and reporting.
    *   Updated ProcessCamera.py and AnnotationReader to use the ModelManager.

4.  ⬜️ **Refactor Reporting System**
    *   Create `core/reporting/` directory.
    *   Move `ApiReporter` and `OfflineReporter` from `reporters.py` into `core/reporting/`.
    *   Define a `ReporterInterface` (e.g., with `send`/`save` methods) and make reporters implement it.
    *   Address optional `cv2` dependency in `OfflineReporter` (e.g., optional core dependency, inject save function).

5.  ✅ **Implement Tracker Scheduler**
    *   Created `core/tracker_scheduler.py` with a `TrackerScheduler` class to centralize tracker scheduling
    *   Implemented worker pool for processing tracker tasks
    *   Added support for on-demand camera connections
    *   Added statistics tracking and reporting
    *   Simplified `_scheduler_loop` method by breaking it into smaller, focused methods
    *   Improved code readability with early returns and reduced nesting
    *   Eliminated duplicate tracker initialization
    *   Implemented proper error handling and recovery mechanisms

6.  ⬜️ **Refactor Common Utilities & Directory Structure**
    *   Create `core/utils/` directory (if not existing).
    *   Move image enhancement functions (`is_frame_too_dark`, `enhance_frame`, etc.) from `utilities/utility_functions.py` to `core/utils/image_processing.py`.
    *   Move `ensure_shape_object` to `core/utils/annotation_utils.py` (or similar).
    *   Consolidate `utils/logger.py` functionality into standard logging practices, potentially using `core/logging.py` for setup.
    *   Move `HomoGraphyFunctions.py` to `core/utils/homography_utils.py` (or similar).
    *   **Cleanup:** Remove the original `utils/` and `utilities/` directories after moving contents. Remove temporary files (`*_temp`, `*.py1`) from `core/`. *(New Sub-task)*

7.  ⬜️ **Implement Core ROI Management**
    *   Create `core/roi_manager.py` (or similar).
    *   Implement logic to load/manage ROI definitions (e.g., from annotation files, dedicated config files) in a UI-agnostic way.
    *   The `CoreProcessor` (Task 5) will use this manager (or directly receive ROI data).
    *   Ensure the ROI extraction logic (slicing) resides within the `CoreProcessor`.

8.  ⬜️ **Refactor Homography Handling**
    *   Ensure homography functions are consolidated in `core/utils/homography_utils.py`.
    *   Review `load_shelve_data` from `utilities/utility_functions.py` - move its logic for loading dimensions/calculating real-world points (needed for homography mapping) to a suitable place (e.g., `core/utils/homography_utils.py`, `core/data_handling`, or `core/config`).
    *   Create a robust interface/structure for accessing and applying homography matrices within `core`.

9.  ✅ **Centralize Config Management**
    *   Reviewed usage of `AppConfig.get_instance()` across the codebase
    *   Implemented a centralized approach within `core` (e.g., `core/config.py`) for loading, validating, and providing access to configuration parameters
    *   Ensured components (`trackers`, `reporters`, `processor`, etc.) get their specific configurations via this central mechanism
    *   Considered environment-specific configurations
    *   Made appropriate methods in `CoreConfig` static for better code organization
    *   Improved code by using static methods for class-level operations
    *   Reduced code duplication by using static methods in internal implementation

10. ⬜️ **Standardize Logging Strategy** *(New Task)*
    *   Review usage of `logging.getLogger()` across the codebase.
    *   Implement a standardized logging approach within `core` (e.g., `core/logging.py`) for setting up and accessing loggers.
    *   Ensure components (`trackers`, `reporters`, `processor`, etc.) use this central logging mechanism.

11. ✅  **Refactor Data Handling**
     *   Create `core/data/` or `core/data_handling/`.
     *   Define interfaces/components for loading necessary data (e.g., annotations via `AnnotationReader` - potentially refactored, shelve data if not handled by homography/config).
     *   Define interfaces for saving outputs (e.g., offline reports via `OfflineReporter` interface).
     *   Move relevant logic from `data/`, `datasets/`, `offline_reports/` and potentially parts of `AnnotationReader`.

11. ⬜️ **Implement Consistent Error Handling**
     *   Define and implement consistent error handling strategies and potentially custom exceptions across `core` components.
     *   Add graceful degradation for missing components or invalid configurations.

12. ⬜️ **Update GUI Integration**
     *   Refactor `main_gui.py`, `helpers/ProcessCamera.py`, `managers/CameraManager.py`, and other GUI components.
     *   Ensure GUI components *use* the new `core` components (`CoreProcessor`, `ModelManager`, `ROIManager`, `trackers`, `reporters`, `utils`, etc.) instead of containing duplicated logic.
     *   Adapt GUI to interact with the `core` interfaces.

13. ⬜️ **Update Headless Mode Integration**
     *   Refactor `main_seamless.py` (and potentially `core/camera_processor.py` if not fully merged into `CoreProcessor`).
     *   Ensure the headless mode uses the `core` components for its workflow (camera handling, processing, reporting).

14. ⬜️ **Align Testing Structure**
     *   Align test directory structure (`tests/`, `test/`) with the `core` module structure.
     *   Move/create relevant unit/integration tests for `core` components.
     *   Ensure adequate test coverage.

15. ⬜️ **Review Dependency Management**
     *   Review `requirements*.txt`, `Pipfile*`, `requirements.sh`.
     *   Ensure dependencies accurately reflect the refactored codebase needs (including optional dependencies like `opencv-python` if decided).
     *   Remove unused dependencies.

16. ⬜️ **Review Development Tools Integration**
     *   Review integration of `dev_mode.py`, `main_gui_dev.py` with the `core` module.
     *   Ensure `core` remains independent or define clear integration points/interfaces.

17. ⬜️ **Performance Optimization** *(New Task)*
     *   Review performance-critical components within `core` (e.g., model inference in `CoreProcessor`, image processing in `core/utils/image_processing.py`).
     *   Implement optimizations where feasible (e.g., batch processing for model predictions, leveraging hardware acceleration if supported by YOLO).
     *   Profile performance before and after refactoring to measure impact.

18. ✅ **Standardize Logging Strategy** *(New Task)*
     *   Define a standardized logging approach across all `core` components (building on consolidation in Task 6).
     *   Implement configurable log levels and outputs (e.g., file, console) via `core/logging.py` or similar.
     *   Ensure consistent logging format and detail for debugging and monitoring.
     *   **Note**: Initial implementation complete, but 26 direct logging.getLogger() calls and 1 logging.basicConfig() call still need to be updated. See REMAINING_REFACTORING_TASKS.md for details.

19. ⬜️ **Documentation of Core Module** *(New Task)*
     *   Document interfaces, classes, and key functions within the `core` module.
     *   Provide usage examples for both GUI and headless mode integration.
     *   Ensure documentation is maintained (e.g., via docstrings, READMEs in `core/` subdirectories).

20. ⬜️ **Version Control for Configurations** *(New Task)*
     *   Enhance centralized config management (Task 9) to support versioning of configuration files.
     *   Implement mechanisms to handle configuration changes over time or across different deployments (e.g., store-specific configs).

21. ⬜️ **Security Considerations** *(New Task)*
     *   Review security for `core/reporting/ApiReporter` (e.g., implement authentication, encrypt sensitive data in API calls).
     *   Ensure configuration handling in `core/config.py` avoids hardcoding sensitive information (e.g., API keys, URLs).
     *   Implement basic security checks or guidelines for data handling within `core`.

22. ✅ **Implement BaseTracker Class**
     *   Created `core/trackers/base_tracker.py` file with a `BaseTracker` abstract class
     *   Defined common interfaces and functionality shared by all tracker types
     *   Ensured all trackers (`ShelfTracker`, `PresenceStateTracker`, `PersonTracker`) inherit from this base class
     *   Moved common functionality (logging, configuration, reporting) to the base class
     *   Implemented appropriate abstract methods that derived classes must implement
     *   Added proper type hints for Shape objects to improve IDE support
     *   Centralized tracker initialization with shape data in `get_tracker_for_shape`

23. ⬜️ **Implement Offline Mode Enhancements** *(New Task)*
     *   Enhance offline reporting to save actual camera images with annotations.
     *   Ensure offline reports use the same JSON format as API reporting.
     *   Implement a consistent file naming and organization scheme for offline reports.
     *   Add traceability by including image IDs in log files.
     *   Ensure offline mode is properly detected and handled across all components.

24. ⬜️ **Video Source Abstraction** *(New Task)*
     *   Create a unified interface for handling different video sources (RTSP, files, etc.).
     *   Implement continuous loop playback for video files in development mode.
     *   Add support for easily switching between video sources for testing.
     *   Ensure proper resource management (opening/closing streams, handling disconnections).

25. ⬜️ **Implement Continuous Integration/Testing** *(New Task)*
     *   Set up automated testing for core components.
     *   Create integration tests that verify the interaction between components.
     *   Implement test fixtures and mocks for external dependencies.
     *   Ensure tests can run in both development and CI environments.

26. ⬜️ **Internationalization Support** *(New Task)*
     *   Implement proper internationalization support for UI strings.
     *   Extract hardcoded strings to resource files.
     *   Ensure proper handling of non-ASCII characters in logs, reports, and UI.
     *   Add support for Turkish language in error messages and logs.

27. ⬜️ **Implement Packaging and Distribution** *(New Task)*
     *   Set up cx_Freeze for creating installable packages.
     *   Configure packaging to include only the empty-shelf-detection directory contents.
     *   Ensure only compiled versions of code are included when possible.
     *   Create proper installer assets and configuration.
     *   Test installation process on target environments.

## Benefits of Refactoring

By moving these components to the `core` module, we're achieving:

1. **Code reusability**: Both GUI and headless modes can share the same core functionality
2. **Reduced duplication**: Common code exists in only one place
3. **Better separation of concerns**: UI/presentation logic is separated from core business logic
4. **Easier maintenance**: Changes to core functionality affect both modes consistently

## Testing Strategy

After each refactoring stage:

1. Test the headless mode with: `python main_seamless.py`
2. Test the GUI mode with: `python main_gui.py`
3. Verify that both modes continue to function correctly

## Refactoring Challenges Addressed

1. **Circular Imports**: Fixed circular dependencies between modules by using lazy imports
2. **File Management**: Properly removed original files after moving to ensure no duplicates exist
3. **Reference Updates**: Updated all imports in dependent files to use the new core module structure

## Refactoring Progress

Based on our automated checks, we've made significant progress in implementing the centralized configuration and logging systems:

1. **Completed Tasks**:
   - Created core/config.py with CoreConfig class
   - Created core/logging.py with CoreLogger class
   - Updated main entry points to use the new core modules
   - Fixed circular import issues

2. **Remaining Tasks**:
   - ✅ Replace remaining references to AppConfig with CoreConfig
   - ✅ Replace direct logging.getLogger() calls with get_logger() in application code
   - ✅ Add deprecation warnings to old logging system
   - ✅ Fix circular imports in core modules
   - Fix missing imports in implementation files (not critical)

3. **Progress Metrics**:
   - Estimated completion: 96.3%
   - Files updated: Main entry points, critical components, and all utility classes
   - Files remaining: Only update scripts and expected deprecation warnings

## Refactoring Dependencies and Order

The following tasks have dependencies and should be completed in a specific order:

1. ✅ First complete **Centralize Config Management** (Task 9) and **Standardize Logging Strategy** (Task 19) as these are used by almost all other components.
2. Next complete **Consolidate IoU Calculation** (Task 1) and **Refactor Common Utilities & Directory Structure** (Task 6) as these provide utility functions used by other components.
3. Then implement **Core Model Manager** (Task 3) and **Implement BaseTracker Class** (Task 23) as these are foundational components.
4. After that, complete **Refactor Tracking Components** (Task 2) and **Refactor Reporting System** (Task 4) which depend on the base classes and utilities.
5. Next, implement **Core ROI Management** (Task 7) and **Refactor Homography Handling** (Task 8) which are needed for the core processor.
6. Then complete **Consolidate Core Processing Logic** (Task 5) and **Implement Core Camera Abstraction** (Task 10) which integrate many of the previous components.
7. Finally, update the application entry points with **Update GUI Integration** (Task 13) and **Update Headless Mode Integration** (Task 14).

Other tasks can be completed in parallel or as needed.
