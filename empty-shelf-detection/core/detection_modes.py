"""
Manages detection modes, models, and trackers for the application.
Provides a singleton class for consistent handling of detection modes.
"""

from typing import Any, Callable, Dict, List, Optional, Union

from core import get_config, get_logger, get_model_manager
from libs.shape import Shape

# Note: Tracker classes are imported dynamically in methods to avoid circular imports

# Tracker type constants
TRACKER_TYPE_SHELVE = "shelve"
TRACKER_TYPE_PERSON = "person"
TRACKER_TYPE_PRESENCE = "presence"

# ROI type constants
ROI_TYPE_EMPTYSHELVE = "emptyshelve"
ROI_TYPE_PRESENCE_CHECK = "presence_check"
ROI_TYPE_PEOPLE_COUNTING = "people_counting"


class DetectionModeManager:
    """
    Manages detection modes, models, and trackers for the application.
    Used by both GUI and headless modes to ensure consistent handling of detection modes.

    This is a singleton class - only one instance will be created.
    """

    # Class-level configuration for detection modes
    MODE_CONFIG = {
        "empty_shelf": {
            "model_id": "empty_shelf",
            "tracker_type": TRACKER_TYPE_SHELVE,
            "roi_type": ROI_TYPE_EMPTYSHELVE,
            "mode_description_key": "emptyShelfMode",  # Translation key
        },
        "presence": {
            "model_id": "people_presence_model",
            "tracker_type": TRACKER_TYPE_PRESENCE,
            "roi_type": ROI_TYPE_PRESENCE_CHECK,
            "mode_description_key": "presenceMode",  # Translation key
        },
        "person": {
            "model_id": "people_counting_model",
            "tracker_type": TRACKER_TYPE_PERSON,
            "roi_type": ROI_TYPE_PEOPLE_COUNTING,
            "mode_description_key": "peopleCountingMode",  # Translation key
        },
        # Add new modes as needed
    }

    # Singleton instance
    _instance = None

    # Class-level translator function
    _translator_func = None

    # Central tracker registry
    _tracker_registry = {
        TRACKER_TYPE_SHELVE: {},  # Maps camera_name -> tracker instance # Maps camera_name -> tracker instance
        TRACKER_TYPE_PRESENCE: {},
        TRACKER_TYPE_PERSON: {},  # Maps camera_name -> tracker instance
    }

    @classmethod
    def get_instance(cls, translator_func: Optional[Callable[[str], str]] = None):
        """
        Get the singleton instance, optionally initializing it with a translator function.

        Args:
            translator_func: Optional function that takes a key and returns a translated string.

        Returns:
            The singleton DetectionModeManager instance
        """
        if cls._instance is None:
            cls._instance = cls.__new__(cls)
        if translator_func is not None:
            cls._translator_func = translator_func
        return cls._instance

    def __new__(cls):
        """Override __new__ to ensure singleton pattern."""
        if cls._instance is None:
            # First time initialization
            cls._instance = super(DetectionModeManager, cls).__new__(cls)
            cls._instance.logger = get_logger(__name__)
        return cls._instance

    def __init__(self):
        """
        Note: Due to singleton pattern, this will only run once for the first instance.
        """
        self.logger = get_logger(__name__)
        self.model_manager = get_model_manager()
        self.logger.info("DetectionModeManager initialized with ModelManager")

    def get_mode_config(self, mode: str) -> Optional[Dict[str, Any]]:
        """
        Get configuration for a specific detection mode.
        """
        return self.MODE_CONFIG.get(mode)

    def get_tracker(self, camera_name: str, tracker_type: Any, roi_type: str, **kwargs):
        """
        Get or create a tracker of the specified type.

        Args:
            camera_name: Name of the camera
            tracker_type: Type of tracker to create ('shelve', 'person', etc.)
            roi_type: Type of ROI being tracked
            **kwargs: Additional parameters for specific tracker types

        Returns:
            Appropriate tracker instance or None if not available
        """
        # Ensure tracker_type is a string
        if tracker_type is None:
            self.logger.warning("tracker_type is None, cannot create tracker")
            return None

        tracker_type = str(tracker_type)

        # Check if tracker already exists in the central registry
        if tracker_type in self._tracker_registry and camera_name in self._tracker_registry[tracker_type]:
            tracker = self._tracker_registry[tracker_type][camera_name]

            # Special handling for shelve tracker to update shelve_name if needed
            if tracker_type == TRACKER_TYPE_SHELVE and "shelve_name" in kwargs and hasattr(tracker, "shelve_name"):
                tracker.shelve_name = kwargs["shelve_name"]

            return tracker

        # Create the tracker based on type
        tracker = None

        if tracker_type == TRACKER_TYPE_SHELVE:
            from core.trackers import ShelfTracker

            shelve_name = kwargs.get("shelve_name", camera_name)
            magaza_no = kwargs.get("magaza_no")
            tracker = ShelfTracker(camera_name, shelve_name=shelve_name, magaza_no=magaza_no)

        elif tracker_type == TRACKER_TYPE_PERSON:
            from core.trackers import PersonTracker

            tracker = PersonTracker(camera_name, roi_type, config=get_config())

        elif tracker_type == TRACKER_TYPE_PRESENCE:
            from core.trackers import PresenceStateTracker

            tracker = PresenceStateTracker(camera_name, roi_type)

        # Store the tracker in the central registry
        if tracker:
            if tracker_type not in self._tracker_registry:
                self._tracker_registry[tracker_type] = {}
            self._tracker_registry[tracker_type][camera_name] = tracker
            self.logger.info(f"Created new {tracker_type} tracker for camera {camera_name}")

        return tracker

    def get_all_trackers(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all trackers from the central registry.

        Returns:
            Dict mapping tracker_type to Dict of camera_name -> tracker instance
        """
        return self._tracker_registry

    def get_tracker_by_camera(self, tracker_type: str, camera_name: str) -> Optional[Any]:
        """
        Get a specific tracker by type and camera name.

        Args:
            tracker_type: Type of tracker to retrieve ('shelve', 'person', 'presence')
            camera_name: Name of the camera

        Returns:
            Tracker instance or None if not found
        """
        tracker_type = str(tracker_type)
        if tracker_type in self._tracker_registry and camera_name in self._tracker_registry[tracker_type]:
            return self._tracker_registry[tracker_type][camera_name]
        return None

    def get_tracker_for_shape(self, camera_name: str, shape: Shape) -> Any:
        """
        Get or create a tracker based on a shape's properties.
        Also sets shape data and initializes the tracker if it's newly created.

        Args:
            camera_name: Name of the camera
            shape: The Shape object with roi_type, homography, etc.

        Returns:
            Appropriate tracker instance or None if not suitable
        """
        # Determine the ROI type
        roi_type = getattr(shape, "roi_type", None)

        # Track if this is a new tracker that needs initialization
        is_new_tracker = False

        # Quick lookup approach instead of iterating through all modes
        for tracker_type in [TRACKER_TYPE_SHELVE, TRACKER_TYPE_PRESENCE, TRACKER_TYPE_PERSON]:
            # Get compatible ROI types for this tracker
            compatible_roi_types = self.get_roi_types_for_tracker(tracker_type)

            # Check if this shape's ROI type is compatible with this tracker
            if roi_type in compatible_roi_types:
                # Special handling for different tracker types
                kwargs = {}

                if tracker_type == TRACKER_TYPE_SHELVE:
                    # For shelve trackers, extract shelve_name and magaza_no
                    shelve_name = getattr(shape, "shelve_id", None) or getattr(shape, "label", camera_name)
                    kwargs["shelve_name"] = shelve_name

                    magaza_no = getattr(shape, "magaza_no", None)
                    if magaza_no:
                        kwargs["magaza_no"] = magaza_no

                # Check if tracker already exists
                if tracker_type in self._tracker_registry and camera_name in self._tracker_registry[tracker_type]:
                    tracker = self._tracker_registry[tracker_type][camera_name]
                else:
                    # Create a new tracker
                    tracker = self.get_tracker(camera_name, tracker_type, roi_type or ROI_TYPE_EMPTYSHELVE, **kwargs)
                    is_new_tracker = True

                # If it's a new tracker, set shape data and initialize it
                if is_new_tracker and tracker:
                    tracker.set_shape_data(shape)
                    tracker.initialize()

                return tracker

            # Special case for shelve tracker with homography
            elif tracker_type == TRACKER_TYPE_SHELVE and roi_type is None and hasattr(shape, "homography") and shape.homography is not None:
                # For shelve trackers, extract shelve_name and magaza_no
                shelve_name = getattr(shape, "shelve_id", None) or getattr(shape, "label", camera_name)
                magaza_no = getattr(shape, "magaza_no", None)

                kwargs = {"shelve_name": shelve_name, "magaza_no": magaza_no}

                # Check if tracker already exists
                if tracker_type in self._tracker_registry and camera_name in self._tracker_registry[tracker_type]:
                    tracker = self._tracker_registry[tracker_type][camera_name]
                else:
                    # Create a new tracker
                    tracker = self.get_tracker(camera_name, TRACKER_TYPE_SHELVE, ROI_TYPE_EMPTYSHELVE, **kwargs)
                    is_new_tracker = True

                # If it's a new tracker, set shape data and initialize it
                if is_new_tracker and tracker:
                    tracker.set_shape_data(shape)
                    tracker.initialize()

                return tracker

        self.logger.warning(f"No matching tracker found for shape with roi_type: {roi_type}")
        return None

    def get_roi_types_for_tracker(self, tracker_type: str) -> list:
        """
        Get the list of ROI types that a specific tracker type can handle.

        Args:
            tracker_type: The tracker type (TRACKER_TYPE_SHELVE, TRACKER_TYPE_PRESENCE, etc.)

        Returns:
            List of compatible ROI types
        """
        compatible_roi_types = []

        # Find all modes that use this tracker type and get their ROI types
        for _, config in self.MODE_CONFIG.items():
            if config.get("tracker_type") == tracker_type:
                roi_type = config.get("roi_type")
                if roi_type and roi_type not in compatible_roi_types:
                    compatible_roi_types.append(roi_type)

        # Add special case for shelve tracker handling shapes with homography
        if tracker_type == TRACKER_TYPE_SHELVE:
            compatible_roi_types.append(None)  # Can handle shapes without explicit roi_type (legacy)

        return compatible_roi_types

    def reset_tracker_registry(self):
        """Reset all registered trackers in the tracker registry."""
        total_trackers = sum(len(trackers) for trackers in self._tracker_registry.values())

        # Reset each tracker type dictionary to an empty dictionary
        for tracker_type in [TRACKER_TYPE_SHELVE, TRACKER_TYPE_PRESENCE, TRACKER_TYPE_PERSON]:
            self._tracker_registry[tracker_type] = {}

        self.logger.info(f"Reset tracker registry: cleared {total_trackers} trackers")
