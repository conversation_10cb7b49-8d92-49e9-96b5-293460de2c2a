"""
Scheduler for tracker-based processing.

This module provides a centralized scheduler that:
1. Periodically checks all registered trackers
2. Determines which trackers should run based on their should_run() method
3. Opens camera connections on-demand for trackers that need to run
4. Processes a single frame for each tracker
5. Closes the camera connection immediately after processing

This is the core component of the tracker-centric approach, where trackers
drive the process, not cameras. The scheduler is responsible for:
- Checking which trackers should run
- Opening camera connections on-demand
- Processing frames with trackers
- Handling reporting
- Managing resources
"""

import os
import queue
import threading
import time
from typing import Any, Dict, List, Optional, Tuple

import cv2
import numpy as np
from core import get_config, get_logger
from core.annotationReader import AnnotationReader
from core.detection_modes import DetectionModeManager
from libs.shape import Shape


class TrackerTask:
    """Represents a single processing task for a specific tracker."""

    def __init__(self, camera_name: str, rtsp_url: str, tracker: Any):
        self.camera_name = camera_name
        self.rtsp_url = rtsp_url
        self.tracker = tracker
        self.created_time = time.time()
        self.tracker_type = tracker.__class__.__name__

    def __str__(self):
        return f"Task[{self.camera_name}:{self.tracker_type}]"

    @property
    def task_key(self) -> Tuple[str, str]:
        """Return a unique key for this task based on camera name and tracker type."""
        return self.camera_name, self.tracker_type


class TrackerScheduler:
    """
    Centralized scheduler for tracker-based processing.

    This class manages a pool of worker threads that process trackers
    based on their should_run() method. It opens camera connections
    on-demand and closes them immediately after processing.
    """

    _instance = None

    @classmethod
    def get_instance(cls):
        """Get the singleton instance."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        """Initialize the scheduler."""
        self.logger = get_logger(self.__class__.__name__)
        self.config = get_config()
        self.detection_mode_manager = DetectionModeManager.get_instance()
        self.reader = AnnotationReader()

        # Worker thread pool
        self.num_workers = self.config.get("scheduler.num_workers", 4)
        self.task_queue = queue.Queue()
        self.workers: List[threading.Thread] = []

        # Task tracking - prevent duplicate tasks for same camera and tracker type
        self.running_tasks = {}  # Format: {(camera_name, tracker_type): timestamp}
        self.running_tasks_lock = threading.Lock()

        # Statistics
        self.stats_lock = threading.Lock()
        self.stats = {"frames_processed": 0, "errors": 0, "start_time": time.time(), "cameras": {}, "trackers": {}}

        # Control flags
        self.running = False
        self.scheduler_thread = None
        self.stats_thread = None

        self.logger.info(f"TrackerScheduler initialized with {self.num_workers} workers")

    def start(self):
        """Start the scheduler and worker threads."""
        if self.running:
            self.logger.warning("Scheduler is already running")
            return

        self.running = True

        # Start scheduler thread
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()

        # Start worker threads
        for i in range(self.num_workers):
            worker = threading.Thread(target=self._worker_loop, args=(i,), daemon=True)
            worker.start()
            self.workers.append(worker)

        # Start stats thread
        self.stats_thread = threading.Thread(target=self._stats_loop, daemon=True)
        self.stats_thread.start()

        self.logger.info(f"Started scheduler with {self.num_workers} workers")

    def stop(self):
        """Stop the scheduler and worker threads."""
        if not self.running:
            self.logger.warning("Scheduler is not running")
            return

        self.running = False

        # Wait for threads to finish
        for worker in self.workers:
            worker.join(timeout=2.0)

        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=2.0)

        if self.stats_thread:
            self.stats_thread.join(timeout=2.0)

        self.logger.info("Scheduler stopped")

    def _scheduler_loop(self):
        """Main scheduler loop that checks trackers and schedules tasks."""
        self.logger.info("Scheduler loop started")

        while self.running:
            try:
                current_time = time.time()
                all_trackers_dict = self.detection_mode_manager.get_all_trackers()
                camera_urls = self.reader.get_matched_camera_urls()

                if not camera_urls:
                    self.logger.error("No camera URLs found. Cannot schedule tasks.")
                    time.sleep(5.0)  # Wait longer before retrying
                    continue

                # Process each tracker
                for _, trackers in all_trackers_dict.items():
                    for camera_name, tracker in trackers.items():
                        self._check_and_schedule_tracker(camera_name, tracker, camera_urls, current_time)

            except Exception as e:
                self.logger.error(f"Error in scheduler loop: {str(e)}", exc_info=True)

            # Sleep to avoid CPU spinning
            time.sleep(1.0)

    def _check_and_schedule_tracker(self, camera_name, tracker, camera_urls, current_time):
        """
        Check if a tracker should run and schedule it if needed.

        Args:
            camera_name: Name of the camera
            tracker: Tracker instance to check
            camera_urls: Dictionary of camera URLs
            current_time: Current timestamp
        """
        # Skip disabled trackers
        if tracker.is_disabled():
            return

        # Check if a task for this camera and tracker type is already running
        task_key = (camera_name, tracker.__class__.__name__)

        with self.running_tasks_lock:
            # If a task is already running and recent (within last 5 seconds), skip
            if task_key in self.running_tasks:
                last_run_time = self.running_tasks[task_key]
                if current_time - last_run_time < 5.0:
                    self.logger.debug(f"Skipping task creation for {camera_name} with {tracker.__class__.__name__} - already running")
                    return

        # Check if the tracker should run
        if not tracker.should_run(current_time):
            return

        # Get the camera URL
        rtsp_url = camera_urls.get(camera_name)
        if not rtsp_url:
            self.logger.warning(f"No URL found for camera {camera_name}, skipping")
            return

        # Create and queue the task
        self._create_and_queue_task(camera_name, rtsp_url, tracker, task_key, current_time)

    def _create_and_queue_task(self, camera_name, rtsp_url, tracker, task_key, current_time):
        """
        Create a task and add it to the queue.

        Args:
            camera_name: Name of the camera
            rtsp_url: URL of the camera feed
            tracker: Tracker instance to run
            task_key: Unique key for this task (camera_name, tracker_type)
            current_time: Current timestamp
        """
        task = TrackerTask(camera_name, rtsp_url, tracker)

        # Mark this task as running
        with self.running_tasks_lock:
            self.running_tasks[task_key] = current_time

        self.task_queue.put(task)
        self.logger.debug(f"Scheduled task for {camera_name} with tracker {tracker.__class__.__name__}")

        # Initialize stats for this camera and tracker if needed
        self._initialize_stats(camera_name, tracker.__class__.__name__)

    def _initialize_stats(self, camera_name: str, tracker_type: str):
        """Initialize statistics for a camera and tracker type."""
        with self.stats_lock:
            if camera_name not in self.stats["cameras"]:
                self.stats["cameras"][camera_name] = {"frames": 0, "errors": 0, "last_processed": 0}

            tracker_key = f"{camera_name}_{tracker_type}"
            if tracker_key not in self.stats["trackers"]:
                self.stats["trackers"][tracker_key] = {"frames": 0, "errors": 0, "last_processed": 0}

    def _worker_loop(self, worker_id: int):
        """Worker thread that processes tasks from the queue."""
        self.logger.info(f"Worker {worker_id} started")

        while self.running:
            try:
                # Get a task from the queue
                try:
                    task = self.task_queue.get(timeout=1.0)
                except queue.Empty:
                    continue

                try:
                    # Process the task
                    self._process_tracker_task(task)

                    # Update stats
                    with self.stats_lock:
                        self.stats["frames_processed"] += 1
                        self.stats["cameras"][task.camera_name]["frames"] += 1
                        self.stats["cameras"][task.camera_name]["last_processed"] = time.time()

                        tracker_key = f"{task.camera_name}_{task.tracker_type}"
                        self.stats["trackers"][tracker_key]["frames"] += 1
                        self.stats["trackers"][tracker_key]["last_processed"] = time.time()
                except Exception as e:
                    self.logger.error(f"Error processing task {task}: {str(e)}", exc_info=True)

                    # Mark the error in the tracker
                    self._mark_tracker_error(task.tracker, e)

                    # Update error stats
                    with self.stats_lock:
                        self.stats["errors"] += 1
                        self.stats["cameras"][task.camera_name]["errors"] += 1

                        tracker_key = f"{task.camera_name}_{task.tracker_type}"
                        self.stats["trackers"][tracker_key]["errors"] += 1
                finally:
                    # Remove the task from running tasks
                    with self.running_tasks_lock:
                        task_key = (task.camera_name, task.tracker_type)
                        if task_key in self.running_tasks:
                            del self.running_tasks[task_key]

                    # Mark the task as done
                    self.task_queue.task_done()
            except Exception as e:
                self.logger.error(f"Unhandled error in worker {worker_id}: {str(e)}", exc_info=True)

    def _process_tracker_task(self, task: TrackerTask):
        """
        Process a single tracker task.

        This method:
        1. Opens the camera connection
        2. Reads a single frame
        3. Processes the frame with the tracker
        4. Handles reporting if needed
        5. Closes the camera connection

        Args:
            task: TrackerTask object with camera_name, rtsp_url, and tracker
        """
        camera_name = task.camera_name
        rtsp_url = task.rtsp_url
        tracker = task.tracker

        # Open the camera
        cap = cv2.VideoCapture(rtsp_url)
        if not cap.isOpened():
            self.logger.error(f"Failed to open camera: {camera_name} at {rtsp_url}")
            return

        try:
            # Read a single frame
            ret, frame = cap.read()
            if not ret:
                self.logger.error(f"Failed to read frame from camera: {camera_name}")
                return

            # Process the frame with the tracker
            roi_frame, _ = tracker.process_frame(frame)
            if roi_frame is None:
                self.logger.warning(f"Failed to process frame for {camera_name} with tracker {task.tracker_type}")
                return

            self.logger.debug(f"Processed frame for {camera_name} with tracker {task.tracker_type}")

            # Check if the tracker should report
            current_time = time.time()
            if tracker.should_report(current_time):
                try:
                    tracker.send_report()
                    self.logger.info(f"Sent report for {camera_name} with tracker {task.tracker_type}")
                except Exception as e:
                    self.logger.error(f"Error sending report for {camera_name}: {str(e)}")

            # Save the frame if needed
            self._save_frame_if_needed(camera_name, frame)

        except Exception as e:
            self.logger.error(f"Error processing task for {camera_name}: {str(e)}", exc_info=True)
            self._mark_tracker_error(tracker, e)
        finally:
            # Always release the camera
            cap.release()

    def _mark_tracker_error(self, tracker, error_obj):
        """
        Mark an error in a tracker.

        Args:
            tracker: The tracker instance
            error_obj: The error that occurred
        """
        # Log the error
        self.logger.error(f"Error in tracker {tracker.__class__.__name__} for camera {tracker.camera_name}: {str(error_obj)}")

        # Mark the error in the tracker
        is_disabled = tracker.mark_error()
        if is_disabled:
            self.logger.warning(f"Tracker {tracker.__class__.__name__} for camera {tracker.camera_name} disabled due to too many errors")

    def _save_frame_if_needed(self, camera_name: str, frame: np.ndarray) -> None:
        """
        Save a frame to disk if configured.

        Args:
            camera_name: Name of the camera
            frame: The frame to save
        """
        if not self.config.get_bool("runtime.save_frames", False):
            return

        # Get the output directory
        output_dir = self.config.get("paths.output", "./output")
        if not output_dir:
            return

        # Create the output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Save the frame
        try:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            save_path = os.path.join(output_dir, f"{camera_name}_{timestamp}.jpg")
            cv2.imwrite(save_path, frame)
            self.logger.debug(f"Saved frame to {save_path}")
        except Exception as e:
            self.logger.error(f"Error saving frame for {camera_name}: {str(e)}")

    def _stats_loop(self):
        """Thread that periodically logs statistics."""
        self.logger.info("Stats loop started")

        while self.running:
            try:
                with self.stats_lock:
                    elapsed = time.time() - self.stats["start_time"]
                    fps = self.stats["frames_processed"] / elapsed if elapsed > 0 else 0
                    self.logger.info(f"Stats: {self.stats['frames_processed']} frames, {self.stats['errors']} errors, {fps:.2f} FPS")
            except Exception as e:
                self.logger.error(f"Error in stats loop: {str(e)}")

            # Sleep for a while
            time.sleep(60.0)

    def initialize_trackers_from_annotations(self) -> bool:
        """
        Initialize trackers from annotations.

        This method:
        1. Loads annotations from files
        2. Creates trackers for each shape
        3. Sets up trackers with shape data

        Returns:
            bool: True if initialization was successful
        """
        self.logger.info("Initializing trackers from annotations...")

        try:
            # Load annotations
            loaded_cameras = self.reader.find_and_load_matched_annotations()
            if not loaded_cameras:
                raise RuntimeError("No cameras found with matching annotations")

            # Process each camera's shapes
            for camera_name, shapes in self.reader.preloaded_annotations.items():
                if not shapes:
                    self.logger.warning(f"No shapes found for camera {camera_name}")
                    continue

                for shape in shapes:
                    # Get or create a tracker for this shape
                    # The shape is guaranteed to be a Shape object from the AnnotationReader
                    tracker = self.detection_mode_manager.get_tracker_for_shape(camera_name, shape)
                    if not tracker:
                        self.logger.warning(f"Failed to create tracker for shape in camera {camera_name}")
                        continue

            # Count and log the initialized trackers
            all_trackers = self.detection_mode_manager.get_all_trackers()
            tracker_counts = {tracker_type: len(trackers) for tracker_type, trackers in all_trackers.items()}
            total_trackers = sum(tracker_counts.values())

            self.logger.info(f"Initialized {total_trackers} trackers: {tracker_counts}")
            return True

        except Exception as e:
            self.logger.error(f"Error initializing trackers: {str(e)}", exc_info=True)
            return False
