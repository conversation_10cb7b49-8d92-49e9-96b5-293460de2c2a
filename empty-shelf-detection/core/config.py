"""
Core configuration management module.

This module provides a centralized approach to configuration management for the
Empty Shelf Detection application. It replaces the legacy AppConfig class
with a more robust implementation that provides:

1. Environment-specific configurations
2. Validation of configuration values
3. Type checking and conversion
4. Default values with documentation
5. Configuration schema definition

The CoreConfig class is designed to be a drop-in replacement for AppConfig,
with backward compatibility functions provided for existing code.

Usage:
    from core import get_config

    # Get the configuration singleton
    config = get_config()

    # Get a configuration value with a default
    api_url = config.get("api.url", "http://localhost:8000")

    # Get a typed configuration value
    timeout = config.get_int("api.timeout", 30)
    enabled = config.get_bool("feature.enabled", False)

    # Get an entire section
    api_config = config.get_section("api")
"""

import json
import logging
import os
from typing import Any, Dict, List, Optional

import yaml

# Set up logger for this module
logger = logging.getLogger(__name__)


class CoreConfig:
    """
    Manages application configuration.
    - Loads general settings from config/config.yaml.
    - Loads camera details directly from data/cameras_all.json.
    """

    _instance = None
    _config_data: Dict[str, Any] = {}
    _camera_info: Dict[str, Dict[str, Any]] = {}
    _camera_names: List[str] = []
    _base_dir: str = ""
    _config_file_path: str = ""
    _camera_config_file_path: str = ""

    @classmethod
    def get_instance(cls):
        """Get the singleton instance of CoreConfig"""
        if cls._instance is None:
            cls._instance = CoreConfig()
        return cls._instance

    def __init__(self):
        """
        Initialize the CoreConfig. Loads general and camera configs.
        Prevents re-initialization for the singleton pattern.
        """
        if CoreConfig._instance is not None:
            return  # Already initialized

        CoreConfig._base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        # Path for general config (config/config.yaml)
        CoreConfig._config_file_path = os.path.join(CoreConfig._base_dir, "config", "config.yaml")

        # Path for camera config (data/cameras_all.json)
        data_dir = os.path.join(CoreConfig._base_dir, "data")
        CoreConfig._camera_config_file_path = os.path.join(data_dir, "cameras_all.json")

        # Load general config
        self._load_general_config()

        # Load camera config
        self._load_camera_details()

        # Set default log directory if not specified in config
        self.log_dir = self.get("logging.log_dir", os.path.join(CoreConfig._base_dir, "logs"))

        # Ensure log directory exists
        os.makedirs(self.log_dir, exist_ok=True)

        # Assign the instance *after* loading
        CoreConfig._instance = self
        logger.info("CoreConfig initialized.")

    def _load_general_config(self):
        """Load the general configuration from core/config.yaml."""
        if os.path.exists(CoreConfig._config_file_path):
            try:
                with open(CoreConfig._config_file_path, "r") as f:
                    CoreConfig._config_data = yaml.safe_load(f) or {}  # Ensure it's a dict even if empty
                logger.info(f"Successfully loaded general configuration from {CoreConfig._config_file_path}")
            except yaml.YAMLError as e:
                logger.error(f"Error parsing YAML file {CoreConfig._config_file_path}: {e}")
                CoreConfig._config_data = {}
            except IOError as e:
                logger.error(f"Error reading config file {CoreConfig._config_file_path}: {e}")
                CoreConfig._config_data = {}
        else:
            logger.warning(f"General configuration file not found at {CoreConfig._config_file_path}. Using empty configuration for general settings.")
            CoreConfig._config_data = {}
            # Consider saving a default minimal config here if desired
            # self._save_general_config({})

    @staticmethod
    def get_camera_map() -> Dict:
        """Load and return the camera map from the camera configuration file."""
        try:
            with open(CoreConfig._camera_config_file_path, "r") as f:
                camera_map = json.load(f)
            logger.info(f"Successfully loaded camera details from {CoreConfig._camera_config_file_path}")

            return camera_map
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from {CoreConfig._camera_config_file_path}: {e}")
            return {}
        except IOError as e:
            logger.error(f"Error reading camera file {CoreConfig._camera_config_file_path}: {e}")
            return {}

    def _load_camera_details(self):
        """Loads camera names, URLs, and magaza_no from data/cameras_all.json."""
        CoreConfig._camera_info = {}
        CoreConfig._camera_names = []

        if not os.path.exists(CoreConfig._camera_config_file_path):
            logger.error(f"Camera configuration file not found: {CoreConfig._camera_config_file_path}. No cameras will be loaded.")
            raise Exception(f"Camera configuration file not found: {CoreConfig._camera_config_file_path}")

        try:
            # Use the static method to get the camera map
            camera_map = CoreConfig.get_camera_map()

            # Extract details
            for _, nvr_data in camera_map.get("nvrs", {}).items():
                magaza_no = nvr_data.get("magaza_no")
                for camera in nvr_data.get("cameras", []):
                    cam_name = camera.get("name")
                    cam_url = camera.get("rtsp_url")
                    if cam_name and cam_url:
                        if cam_name in CoreConfig._camera_info:
                            logger.warning(f"Duplicate camera name '{cam_name}' found in {CoreConfig._camera_config_file_path}. Overwriting details.")
                        CoreConfig._camera_info[cam_name] = {"url": cam_url, "magaza_no": magaza_no}
                        # Add name only if it's not already there (handles duplicates across NVRs)
                        if cam_name not in CoreConfig._camera_names:
                            CoreConfig._camera_names.append(cam_name)

            # Sort names for consistency
            CoreConfig._camera_names.sort()
            logger.info(f"Loaded details for {len(CoreConfig._camera_names)} cameras.")

        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from {CoreConfig._camera_config_file_path}: {e}")
        except IOError as e:
            logger.error(f"Error reading camera file {CoreConfig._camera_config_file_path}: {e}")
        except Exception as e:
            logger.error(f"Unexpected error loading camera details from {CoreConfig._camera_config_file_path}: {e}", exc_info=True)

    def get(self, key_path: str, default: Any = None) -> Any:
        """
        Get a general configuration value using a dot-separated path from config/config.yaml.

        Example: config.get('tracker.shelve.iou_threshold', 0.5)

        Args:
            key_path: Dot-separated string representing the path (e.g., 'section.key').
            default: Default value if key is not found.

        Returns:
            The configuration value or the default.
        """
        keys = key_path.split(".")
        value = CoreConfig._config_data
        try:
            for key in keys:
                if isinstance(value, dict):
                    value = value[key]
                else:
                    # If trying to access a subkey of a non-dict value, return default
                    # logger.debug(f"Config path '{key_path}' leads to non-dict before end.")
                    return default
            return value
        except KeyError:
            # logger.debug(f"General configuration key '{key_path}' not found in {CoreConfig._config_file_path}.")
            return default
        except TypeError:
            # logger.debug(f"Invalid path '{key_path}'. Intermediate key not a dictionary.")
            return default

    def get_section(self, section_key: str, default: Optional[Dict] = None) -> Optional[Dict]:
        """Get an entire section from the general config (config/config.yaml)."""
        value = self.get(section_key, default if default is not None else {})
        if isinstance(value, dict):
            return value
        else:
            logger.warning(f"Config key '{section_key}' exists but is not a dictionary section.")
            return default if default is not None else {}

    # --- Camera Detail Accessors ---

    @staticmethod
    def get_camera_names() -> List[str]:
        """Get all unique camera names loaded from data/cameras_all.json."""
        return CoreConfig._camera_names

    @staticmethod
    def get_camera_urls() -> Dict[str, str]:
        """Get a dictionary mapping camera names to URLs from data/cameras_all.json."""
        return {name: info.get("url") for name, info in CoreConfig._camera_info.items() if info.get("url")}

    @staticmethod
    def get_camera_url(camera_name: str) -> Optional[str]:
        """Get the URL for a specific camera name from data/cameras_all.json."""
        camera_data = CoreConfig._camera_info.get(camera_name)
        return camera_data.get("url") if camera_data else None

    @staticmethod
    def get_camera_magaza_no(camera_name: str) -> Optional[str]:
        """Get the magaza_no for a specific camera name from data/cameras_all.json."""
        camera_data = CoreConfig._camera_info.get(camera_name)
        return camera_data.get("magaza_no") if camera_data else None

    # --- Type-specific Getters ---

    def get_int(self, key_path: str, default: int = 0) -> int:
        """
        Get an integer configuration value.

        Args:
            key_path: Dot-separated string representing the path.
            default: Default value if key is not found or not an integer.

        Returns:
            The integer value or the default.
        """
        value = self.get(key_path, default)
        try:
            return int(value)
        except (ValueError, TypeError):
            logger.warning(f"Config key '{key_path}' value '{value}' is not a valid integer. Using default: {default}")
            return default

    def get_float(self, key_path: str, default: float = 0.0) -> float:
        """
        Get a float configuration value.

        Args:
            key_path: Dot-separated string representing the path.
            default: Default value if key is not found or not a float.

        Returns:
            The float value or the default.
        """
        value = self.get(key_path, default)
        try:
            return float(value)
        except (ValueError, TypeError):
            logger.warning(f"Config key '{key_path}' value '{value}' is not a valid float. Using default: {default}")
            return default

    def get_bool(self, key_path: str, default: bool = False) -> bool:
        """
        Get a boolean configuration value.

        Args:
            key_path: Dot-separated string representing the path.
            default: Default value if key is not found or not a boolean.

        Returns:
            The boolean value or the default.
        """
        value = self.get(key_path, default)
        if isinstance(value, bool):
            return value
        elif isinstance(value, str):
            if value.lower() in ("true", "yes", "1", "on"):
                return True
            elif value.lower() in ("false", "no", "0", "off"):
                return False
        logger.warning(f"Config key '{key_path}' value '{value}' is not a valid boolean. Using default: {default}")
        return default

    def get_str(self, key_path: str, default: str = "") -> str:
        """
        Get a string configuration value.

        Args:
            key_path: Dot-separated string representing the path.
            default: Default value if key is not found.

        Returns:
            The string value or the default.
        """
        value = self.get(key_path, default)
        if value is None:
            return default
        return str(value)

    # Optional: Method to save general config (if modification is needed)
    # def _save_general_config(self, config_dict: Dict[str, Any]):
    #     try:
    #         with open(CoreConfig._config_file_path, 'w') as f:
    #             yaml.dump(config_dict, f, default_flow_style=False)
    #         logger.info(f"Saved general configuration to {CoreConfig._config_file_path}")
    #     except Exception as e:
    #         logger.error(f"Error saving general config to {CoreConfig._config_file_path}: {e}")


# --- Global Access Function ---
def get_config() -> CoreConfig:
    """
    Get the singleton instance of CoreConfig. Recommended access method.
    """
    return CoreConfig.get_instance()


# --- Example Usage Removed ---
# if __name__ == "__main__":
#     ...
