# Core Module

The `core` module provides the foundational components for the Empty Shelf Detection application. It is designed to be used by both the GUI and headless modes of the application, ensuring consistent behavior and reducing code duplication.

## Components

### Configuration Management

The `CoreConfig` class provides a centralized approach to configuration management with the following features:

- Type checking and validation of configuration values
- Environment-specific configurations (development, testing, production)
- Default values with documentation
- Configuration schema definition
- Typed getter methods for different data types

Example usage:

```python
from core import get_config

# Get the singleton instance
config = get_config()

# Get configuration values with type checking
empty_threshold = config.get_int('tracker', 'empty_threshold')
iou_threshold = config.get_float('tracker', 'iou_threshold')
api_url = config.get_str('shelve_api', 'url')
dev_mode_enabled = config.get_bool('dev_mode', 'enabled')

# Get an entire section
logging_config = config.get_section('logging')

# Set a configuration value
config.set('tracker', 'empty_threshold', 10)

# Check environment
if config.is_development():
    # Development-specific code
    pass

# Check offline mode
if config.is_offline_mode():
    # Offline mode code
    pass
```

### Logging

The `CoreLogger` class provides a standardized approach to logging with the following features:

- Centralized configuration from `CoreConfig`
- Consistent formatting across all components
- Log rotation with configurable size and backup count
- Component-specific loggers with optional file output
- Support for different log levels in different environments

Example usage:

```python
from core import get_logger, setup_logging

# Set up logging
setup_logging()

# Get a logger for a specific component
logger = get_logger('my_component')

# Log messages at different levels
logger.debug('Debug message')
logger.info('Info message')
logger.warning('Warning message')
logger.error('Error message')
logger.critical('Critical message')
```

### Annotation Reader

The `AnnotationReader` class is responsible for loading and managing annotations for cameras and regions of interest.

### Camera Processor

The `CameraProcessor` class handles the processing of camera feeds, including detection, tracking, and reporting.

### Detection Modes

The `DetectionModeManager` class manages different detection modes, models, and trackers for the application.

## Future Components

The following components are planned for future implementation:

- Core Model Manager
- Core ROI Management
- Core Trackers
- Core Reporting
- Core Utilities
- Core Camera Abstraction
- Core Data Handling

## Migration from Legacy Systems

### Configuration

The old `AppConfig` class has been replaced with the new `CoreConfig` class. To migrate:

1. Replace `from config.app_config import AppConfig` with `from core import get_config`
2. Replace `AppConfig.get_instance()` with `get_config()`

### Logging

The old `utils.logger` module has been replaced with the new `core.logging` module. To migrate:

1. Replace `from utils.logger import get_logger, setup_logging` with `from core import get_logger, setup_logging`
2. Replace `logging.getLogger()` with `get_logger()`

The old logging system has been deprecated and will be removed in a future version. It currently issues deprecation warnings to encourage migration to the new system.

## Testing

The core module includes a test script (`test_core_config_logging.py`) that verifies the functionality of the configuration and logging components.

To run the tests:

```bash
python test_core_config_logging.py
```

## Refactoring Status

The refactoring of the configuration and logging systems is approximately 93.7% complete. The remaining issues are:

1. Direct logging configuration in utils/logger.py - This is expected since it's the old logging implementation
2. Missing core imports in core/logging.py and core/config.py - These are also expected since they are the implementation files
3. References in update_config_and_logging.py - These are in the utility script, not in the actual codebase

See `REFACTORING_STATUS.md` and `REFACTORING_SUMMARY.md` for more details.
