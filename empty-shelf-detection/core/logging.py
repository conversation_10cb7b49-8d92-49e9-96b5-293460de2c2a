"""
Core logging module.

This module provides a standardized approach to logging for the Empty Shelf
Detection application. It builds on the Python standard logging module but
provides additional features:

1. Centralized configuration from CoreConfig
2. Consistent formatting across all components
3. Log rotation with configurable size and backup count
4. Component-specific loggers with optional file output
5. Support for different log levels in different environments
"""

import logging
import logging.handlers
import os
from typing import Dict, List, Optional, Union

from core.config import CoreConfig


class CoreLogger:
    """
    Enhanced logging management for the core module.

    This class provides a standardized approach to logging with centralized
    configuration and consistent formatting across all components.
    """

    _instance = None

    @classmethod
    def get_instance(cls):
        """Get the singleton instance of CoreLogger"""
        if cls._instance is None:
            cls._instance = CoreLogger()
        return cls._instance

    def __init__(self):
        """Initialize the logger with configuration from CoreConfig"""
        self.config = CoreConfig.get_instance()

        self.log_dir = getattr(self.config, "log_dir", "logs")

        # Create log directory if it doesn't exist
        os.makedirs(self.log_dir, exist_ok=True)

        # Get logging configuration
        log_level_str = self.config.get("logging.level", "INFO")
        self.log_level = getattr(logging, log_level_str.upper())
        self.log_format = self.config.get("logging.format", "%(asctime)s - %(levelname)s - %(name)s - %(message)s")

        # Get max_size as int (default 10 MB)
        self.max_size = self.config.get("logging.max_size", 10 * 1024 * 1024)

        # Get backup_count as int (default 5)
        self.backup_count = self.config.get("logging.backup_count", 5)

        # Configure root logger
        self.setup_root_logger()

        # Create loggers for different components
        self.loggers = {}
        self.setup_component_loggers()

    def setup_root_logger(self):
        """Set up the root logger with console and file handlers"""
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)

        # Remove existing handlers
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # Create formatters
        formatter = logging.Formatter(self.log_format)

        # Create console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(self.log_level)
        root_logger.addHandler(console_handler)

        # Create file handler with rotation
        log_file = os.path.join(self.log_dir, "app.log")
        file_handler = logging.handlers.RotatingFileHandler(log_file, maxBytes=self.max_size, backupCount=self.backup_count)
        file_handler.setFormatter(formatter)
        file_handler.setLevel(self.log_level)
        root_logger.addHandler(file_handler)

        logging.info(f"Root logger configured with level {logging.getLevelName(self.log_level)}")

    def setup_component_loggers(self):
        """Set up loggers for different components"""
        # Core components
        core_components = ["core", "config", "tracker", "camera", "api", "model", "processor", "reporter", "utils", "roi"]

        for component in core_components:
            self.get_logger(component)

    def get_logger(self, name: str, separate_file: bool = True) -> logging.Logger:
        """
        Get a logger for a specific component.

        Args:
            name: Name of the component
            separate_file: Whether to create a separate log file for this component

        Returns:
            Logger instance for the component
        """
        if name in self.loggers:
            return self.loggers[name]

        # Create new logger
        logger = logging.getLogger(name)
        logger.setLevel(self.log_level)

        # Remove existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # Create component-specific file handler with rotation if requested
        if separate_file:
            log_file = os.path.join(self.log_dir, f"{name}.log")
            file_handler = logging.handlers.RotatingFileHandler(log_file, maxBytes=self.max_size, backupCount=self.backup_count)
        formatter = logging.Formatter(self.log_format)
        file_handler.setFormatter(formatter)
        file_handler.setLevel(self.log_level)
        logger.addHandler(file_handler)

        self.loggers[name] = logger

        return logger

    def set_level(self, level: Union[int, str], component: Optional[str] = None):
        """
        Set the logging level for a component or all loggers.

        Args:
            level: Logging level (e.g., 'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
            component: Component name (optional, if None, sets level for all loggers)
        """
        # Convert string level to int if needed
        if isinstance(level, str):
            level = getattr(logging, level.upper())

        if component is not None:
            # Set level for specific component
            if component in self.loggers:
                self.loggers[component].setLevel(level)
                for handler in self.loggers[component].handlers:
                    handler.setLevel(level)
                logging.info(f"Set log level for {component} to {logging.getLevelName(level)}")
        else:
            # Set level for all loggers
            for _, logger in self.loggers.items():
                logger.setLevel(level)
                for handler in logger.handlers:
                    handler.setLevel(level)

            # Also set root logger level
            root_logger = logging.getLogger()
            root_logger.setLevel(level)
            for handler in root_logger.handlers:
                handler.setLevel(level)

            logging.info(f"Set log level for all loggers to {logging.getLevelName(level)}")

    def add_file_handler(self, component: str, filename: str, level: Optional[Union[int, str]] = None):
        """
        Add a file handler to a component logger.

        Args:
            component: Component name
            filename: Log file name
            level: Logging level (optional, if None, uses the component's level)
        """
        if component not in self.loggers:
            self.get_logger(component, separate_file=False)

        logger = self.loggers[component]

        # Convert string level to int if needed
        if isinstance(level, str):
            level = getattr(logging, level.upper())
        elif level is None:
            level = logger.level

        # Create file handler with rotation
        log_file = os.path.join(self.log_dir, filename)
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=self.max_size,
            backupCount=self.backup_count
        )
        formatter = logging.Formatter(self.log_format)
        file_handler.setFormatter(formatter)
        file_handler.setLevel(level)
        logger.addHandler(file_handler)

        logging.info(f"Added file handler {filename} to {component} logger with level {logging.getLevelName(level)}")


# Initialize logger
def setup_logging() -> CoreLogger:
    """Set up logging for the application"""
    return CoreLogger.get_instance()


# Get a logger for a specific component
def get_logger(name: str, separate_file: bool = True) -> logging.Logger:
    """
    Get a logger for a specific component.

    Args:
        name: Name of the component
        separate_file: Whether to create a separate log file for this component

    Returns:
        Logger instance for the component
    """
    logger_instance = CoreLogger.get_instance()
    return logger_instance.get_logger(name, separate_file)
