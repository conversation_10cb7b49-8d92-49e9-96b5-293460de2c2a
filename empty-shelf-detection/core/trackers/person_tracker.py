# Standard library imports
import time
from typing import Any, Dict

from core import compute_iou
from core.trackers.base_tracker import BaseTracker


class PersonTracker(BaseTracker):
    """
    Basic tracker for assigning IDs to detected people within an ROI.
    Uses simple IoU matching. Does not track long-term presence/absence state.
    """

    def __init__(self, camera_name: str, roi_type: str, config=None):
        # Call the parent class's __init__ method
        super().__init__(camera_name, roi_type)

        # Use provided config or get it directly (BaseTracker already does this)
        if config:
            self.config = config

        # Load parameters directly using get() with defaults
        section = "tracker.individual_person"  # Example section name
        self.iou_threshold = self.config.get(f"{section}.iou_threshold", 0.4)
        self.max_missing_frames = self.config.get(f"{section}.max_missing_frames", 5)

        # Tracking state
        self.tracked_people: Dict[int, Dict[str, Any]] = {}
        self.next_track_id = 1

        # Log initialization
        self.logger.info(f"Initialized PersonTracker for {camera_name} - {roi_type}")

    def track_detections(self, detections, frame_number: int, current_frame=None):
        """
        Assigns track IDs to person detections based on IoU matching.

        Args:
            detections: Detections object (e.g., supervision.Detections) with xyxy coordinates.
            frame_number (int): The current frame number.
            current_frame: Optional current frame to store for reporting.

        Returns:
            list: A list of track IDs corresponding to each detection.
        """
        # Update last run time
        self.last_run_time = time.time()

        # Store the current frame for reporting
        if current_frame is not None:
            self.current_frame = current_frame

        track_ids = [-1] * len(detections)
        if len(detections) == 0:
            self._handle_missed_tracks(set(), frame_number)  # Update missed counts for all active tracks
            return track_ids

        detection_bboxes = [list(bbox) for bbox in detections.xyxy]
        matched_indices: Dict[int, int] = {}  # {detection_idx: track_id}
        used_tracks = set()

        # Match detections to existing tracks
        for track_id, track_data in list(self.tracked_people.items()):  # Iterate copy
            best_iou = 0.0
            best_detection_idx = -1
            track_bbox = track_data["bounding_box"]

            for det_idx in range(len(detection_bboxes)):
                if det_idx in matched_indices:
                    continue
                iou = compute_iou(detection_bboxes[det_idx], track_bbox)
                if iou > best_iou and iou >= self.iou_threshold:
                    best_iou = iou
                    best_detection_idx = det_idx

            if best_detection_idx != -1:
                matched_indices[best_detection_idx] = track_id
                used_tracks.add(track_id)
                # Update track
                track_data["bounding_box"] = detection_bboxes[best_detection_idx]
                track_data["last_seen_frame"] = frame_number
                track_data["missed_frames"] = 0
                track_ids[best_detection_idx] = track_id

        # Create new tracks for unmatched detections
        for det_idx in range(len(detection_bboxes)):
            if det_idx not in matched_indices:
                new_id = self.next_track_id
                self.next_track_id += 1
                self.tracked_people[new_id] = {
                    "bounding_box": detection_bboxes[det_idx],
                    "first_seen_frame": frame_number,
                    "last_seen_frame": frame_number,
                    "missed_frames": 0,
                }
                track_ids[det_idx] = new_id
                self.logger.debug(f"Created new person track {new_id}")

        # Handle missed tracks
        self._handle_missed_tracks(used_tracks, frame_number)

        return track_ids

    def _handle_missed_tracks(self, matched_track_ids: set, _frame_number: int):
        """Update missed frame counts and remove old tracks."""
        active_track_ids = set(self.tracked_people.keys())
        missed_track_ids = active_track_ids - matched_track_ids

        for track_id in missed_track_ids:
            track_data = self.tracked_people[track_id]
            track_data["missed_frames"] += 1
            if track_data["missed_frames"] > self.max_missing_frames:
                self.logger.debug(f"Removing person track {track_id} (missed {track_data['missed_frames']} frames)")
                del self.tracked_people[track_id]

    def get_active_tracks(self) -> Dict[int, Dict[str, Any]]:
        """Returns the currently active tracked people."""
        return self.tracked_people

    def should_run(self, now: float) -> bool:
        """
        Determines if the tracker should run based on the configured check_interval (in minutes).
        Reads the interval from config.yaml (tracker.individual_person.check_interval).
        Args:
            now (float): Current timestamp (time.time()).
        Returns:
            bool: True if enough time has passed since the last run, False otherwise.
        """
        interval_min = self.config.get("tracker.individual_person.check_interval", 1)  # minutes
        interval_sec = interval_min * 60
        return (now - self.last_run_time) >= interval_sec

    def should_report(self, now: float) -> bool:
        """
        Determines if the tracker should report based on the configured report_interval (in minutes).
        Reads the interval from config.yaml (tracker.individual_person.report_interval).
        Args:
            now (float): Current timestamp (time.time()).
        Returns:
            bool: True if enough time has passed since the last report, False otherwise.
        """
        interval_min = self.config.get("tracker.individual_person.report_interval", 1)  # minutes
        interval_sec = interval_min * 60
        return (now - self.last_report_time) >= interval_sec

    def get_preferred_model_id(self) -> str:
        """
        Returns the preferred model ID for this tracker.

        Returns:
            str: The model ID to use for this tracker
        """
        return "people_counting_model"  # PersonTracker always uses the people_counting model

    def format_report_data(self) -> dict:
        """
        Format the tracker's state for reporting.

        Returns:
            dict: Formatted data for reporting
        """
        return {
            "camera_name": self.camera_name,
            "roi_type": self.roi_type,
            "timestamp": time.time(),
            "active_tracks": len(self.tracked_people),
            "track_data": {str(track_id): track_data for track_id, track_data in self.tracked_people.items()},
        }

    def send_report(self) -> bool:
        """
        Send a report of the current state.

        Returns:
            bool: True if the report was sent successfully
        """
        from core.reporting import ReportingService

        # Get the reporting service
        reporting_service = ReportingService.get_instance()

        # Format the report data
        payload = self.format_report_data()

        # Send the report with the current frame for base64 encoding
        success = reporting_service.send_report(
            payload=payload, camera_name=self.camera_name, frame=getattr(self, "current_frame", None)  # Pass the current frame if available
        )

        if success:
            self.last_report_time = time.time()
            self.logger.info(f"Sent person tracker report for {self.camera_name}")
        else:
            self.logger.warning(f"Failed to send person tracker report for {self.camera_name}")

        return success
