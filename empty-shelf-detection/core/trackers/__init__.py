"""
Tracker module for the Empty Shelf Detection application.

This module contains tracker implementations for different detection modes:
- ShelfTracker: Tracks empty shelf spots across frames
- PresenceStateTracker: Tracks the presence/absence of people in an area
- PersonTracker: Tracks individual people and assigns IDs

All trackers are designed to work with the DetectionModeManager.
"""

from .shelf_tracker import ShelfTracker
from .presence_state_tracker import PresenceStateTracker
from .person_tracker import PersonTracker

__all__ = [
    'ShelfTracker',
    'PresenceStateTracker',
    'PersonTracker',
]
