import time
from collections import deque
from datetime import datetime
from typing import Dict, Optional

import numpy as np
from core import get_config, get_logger
from core.reporting import ReportingService
from core.trackers.base_tracker import BaseTracker


class PresenceStateTracker(BaseTracker):
    """
    Tracks the state (presence/absence) of people within defined ROIs over time.
    Uses a sliding window and thresholds to determine stable presence or absence.
    """

    def __init__(self, camera_name: str, roi_type: str):
        """Initialize the presence tracker."""
        # Call the parent class's __init__ method
        super().__init__(camera_name, roi_type)

        # Get the reporting service
        self.reporting_service = ReportingService.get_instance()

        # Load configuration parameters
        self._load_config()

        # State tracking variables
        self.presence_history = deque(maxlen=self.history_size)
        self.current_state = "unknown"  # Can be 'unknown', 'present', 'absent'
        self.last_state_change_time = time.time()
        self.last_detection_time = 0.0
        self.current_frame = None  # Store the current frame for reporting

        self.logger.info(f"Initialized PresenceStateTracker for {camera_name} - {roi_type}")

    def _load_config(self):
        """Loads configuration settings directly using config.get with defaults."""
        section = "tracker.presence"  # Use nested key directly
        # Assume types from YAML are correct
        self.history_size = self.config.get(f"{section}.history_size", 10)
        self.presence_threshold = self.config.get(f"{section}.presence_threshold", 0.7)
        self.absence_threshold = self.config.get(f"{section}.absence_threshold", 0.9)
        self.min_state_duration = float(self.config.get(f"{section}.min_state_duration", 5.0))
        self.detection_confidence = self.config.get(f"{section}.detection_threshold", 0.4)
        self.person_class_id = self.config.get(f"{section}.person_class_id", 0)

        # Update deque maxlen if history_size changed from default during load
        if hasattr(self, "presence_history") and self.presence_history.maxlen != self.history_size:
            self.presence_history = deque(self.presence_history, maxlen=self.history_size)
        elif not hasattr(self, "presence_history"):  # Ensure it exists before state update
            self.presence_history = deque(maxlen=self.history_size)

    def check_roi(self, roi_frame: np.ndarray, model) -> bool:
        """
        Checks a specific ROI frame for people presence using the provided model.
        Updates the internal presence history and state.

        Args:
            roi_frame (np.ndarray): The image data for the Region of Interest.
            model: The detection model (e.g., YOLO) with a predict method.

        Returns:
            bool: True if people are considered currently detected in this frame, False otherwise.
        """
        if roi_frame is None or roi_frame.size == 0:
            self.logger.warning("Received empty ROI frame.")
            self._update_presence_history(False)
            return False
        try:
            results = model.predict(roi_frame, conf=self.detection_confidence, classes=[self.person_class_id], verbose=False)
            person_detected = False
            if len(results) > 0 and hasattr(results[0], "boxes") and results[0].boxes is not None:
                if results[0].boxes.cls is not None and results[0].boxes.conf is not None:
                    person_indices = np.where(
                        (results[0].boxes.cls.cpu().numpy() == self.person_class_id) & (results[0].boxes.conf.cpu().numpy() >= self.detection_confidence)
                    )[0]
                    if len(person_indices) > 0:
                        person_detected = True
                elif len(results[0].boxes) > 0:
                    person_detected = True

            if person_detected:
                self.last_detection_time = time.time()
            self._update_presence_history(person_detected)
            self._update_state()
            return person_detected
        except Exception as e:
            self.logger.error(f"ROI detection error: {e}", exc_info=False)
            self._update_presence_history(False)
            return False

    def _update_presence_history(self, detected: bool):
        self.presence_history.append(1 if detected else 0)

    def _update_state(self):
        """Updates the current state (present/absent/unknown) based on history."""
        if len(self.presence_history) < self.history_size:
            return
        if self.history_size == 0:
            return  # Avoid division by zero

        presence_ratio = sum(self.presence_history) / self.history_size
        current_time = time.time()
        time_in_current_state = current_time - self.last_state_change_time
        new_state = self.current_state

        if presence_ratio >= self.presence_threshold:
            if self.current_state != "present":
                if self.current_state == "unknown" or time_in_current_state >= self.min_state_duration:
                    new_state = "present"
                    self.logger.info(f"State -> PRESENT (Ratio: {presence_ratio:.2f})")
                # else: # Reduced logging
                # self.logger.debug(
                #     f"Presence threshold met ({presence_ratio:.2f}), "
                #     f"but prev state '{self.current_state}' duration ({time_in_current_state:.1f}s) < min ({self.min_state_duration}s). Holding."
                # )
            # else: self.logger.debug(f"State remains PRESENT (Ratio: {presence_ratio:.2f})")
        elif (1 - presence_ratio) >= self.absence_threshold:
            if self.current_state != "absent":
                if self.current_state == "unknown" or time_in_current_state >= self.min_state_duration:
                    new_state = "absent"
                    self.logger.info(f"State -> ABSENT (Absence Ratio: {1 - presence_ratio:.2f})")
                # else: # Reduced logging
                # self.logger.debug(
                #     f"Absence threshold met ({1 - presence_ratio:.2f}), "
                #     f"but prev state '{self.current_state}' duration ({time_in_current_state:.1f}s) < min ({self.min_state_duration}s). Holding."
                # )
            # else: self.logger.debug(f"State remains ABSENT (Absence Ratio: {1 - presence_ratio:.2f})")
        else:
            if self.current_state != "unknown":
                if time_in_current_state < self.min_state_duration:
                    # self.logger.debug(f"Ratio uncertain ({presence_ratio:.2f}), holding prev state '{self.current_state}' due to short duration.")
                    pass  # Keep current state
                else:
                    new_state = "unknown"
                    self.logger.info(f"Ratio uncertain ({presence_ratio:.2f}) -> UNKNOWN.")
            # else: self.logger.debug(f"Ratio uncertain ({presence_ratio:.2f}), state remains UNKNOWN.")

        if new_state != self.current_state:
            self.current_state = new_state
            self.last_state_change_time = current_time

    def get_current_state(self) -> str:
        """Returns the current determined state ('present', 'absent', 'unknown')."""
        return self.current_state

    def get_last_detection_time(self) -> float:
        """Returns the timestamp of the last positive detection."""
        return self.last_detection_time

    def should_run(self, now: float) -> bool:
        """
        Determines if the tracker should run based on the configured check_interval (in minutes).
        Reads the interval from config.yaml (tracker.presence.check_interval).
        Args:
            now (float): Current timestamp (time.time()).
        Returns:
            bool: True if enough time has passed since the last run, False otherwise.
        """
        interval_min = self.config.get("tracker.presence.check_interval", 10)  # minutes
        interval_sec = interval_min * 60
        return (now - self.last_run_time) >= interval_sec

    def should_report(self, now: float) -> bool:
        """
        Determines if the tracker should report based on the configured report_interval (in minutes).
        Reads the interval from config.yaml (tracker.presence.report_interval).
        Args:
            now (float): Current timestamp (time.time()).
        Returns:
            bool: True if enough time has passed since the last report, False otherwise.
        """
        interval_min = self.config.get("tracker.presence.report_interval", 1)  # minutes
        interval_sec = interval_min * 60
        return (now - self.last_report_time) >= interval_sec

    def get_preferred_model_id(self) -> str:
        """
        Returns the preferred model ID for this tracker.

        Returns:
            str: The model ID to use for this tracker
        """
        return "people_presence_model"  # PresenceStateTracker always uses the people_presence model

    def track_detections(self, detections, frame_number: int, current_frame=None):
        """Process detections and update presence state."""
        # Update last run time
        self.last_run_time = time.time()

        # Store the current frame for reporting
        if current_frame is not None:
            self.current_frame = current_frame

        # Check if there are any detections
        person_detected = len(detections) > 0

        # Update presence history and state
        self._update_presence_history(person_detected)
        self._update_state()

        # Return empty track IDs (this tracker doesn't track individual objects)
        return []

    def format_report_data(self) -> dict:
        """Format the tracker's state for reporting."""
        return {
            "camera_name": self.camera_name,
            "roi_type": self.roi_type,
            "state": self.current_state,
            "last_detection_time": self.last_detection_time,
            "presence_history": list(self.presence_history),
            "timestamp": time.time(),
        }

    def send_report(self) -> bool:
        """Send a report of the current state."""
        payload = self.format_report_data()

        # Send the report with the current frame for base64 encoding
        success = self.reporting_service.send_report(
            payload=payload, camera_name=self.camera_name, frame=self.current_frame  # Pass the current frame for base64 encoding
        )

        if success:
            self.last_report_time = time.time()
            self.logger.info(f"Sent presence state report for {self.camera_name}: {self.current_state}")
        else:
            self.logger.warning(f"Failed to send presence state report for {self.camera_name}")

        return success
