import time
from datetime import datetime
from typing import Any, Dict, List, Optional

import cv2
import numpy as np
from core import compute_iou, get_config, get_logger
from core.reporting import ReportingService
from core.trackers.base_tracker import BaseTracker


class ShelfTracker(BaseTracker):
    """Tracks empty shelf spots across frames and reports them."""

    def __init__(self, camera_name: str, shelve_name: Optional[str] = None, magaza_no: Optional[str] = None):
        """Initialize shelf tracker for a camera and shelf."""
        # Call the parent class's __init__ method
        super().__init__(camera_name, roi_type="emptyshelve")

        # Get the reporting service
        self.reporting_service = ReportingService.get_instance()

        # Basic properties
        self.shelve_name = shelve_name
        self.shelve_id = shelve_name  # Use name as ID for now
        self.magaza_no = magaza_no or ""

        # Tracking state
        self.tracked_spots = {}
        self.next_track_id = 1
        self.current_frame = None
        self.last_api_report_time = 0.0
        self.report_counts = {}
        self.last_successful_report = {}

        # Homography data
        self.homography_matrix = None
        self.shelve_width_cm = None
        self.shelve_height_cm = None

        # Load configuration parameters
        self._load_config_parameters()

        self.logger.info(f"Initialized ShelfTracker for camera: {camera_name}, shelve: {shelve_name or 'Unknown'}")

    def _load_config_parameters(self):
        """Loads tracker-specific parameters directly using config.get with defaults."""
        # Assume types from YAML are correct, provide defaults
        self.EMPTY_THRESHOLD = self.config.get("tracker.shelve.empty_threshold", 5)
        self.MAX_MISSING_FRAMES = self.config.get("tracker.shelve.max_missing_frames", 3)
        self.IOU_THRESHOLD = self.config.get("tracker.shelve.iou_threshold", 0.3)
        self.API_REPORT_INTERVAL = float(self.config.get("tracker.shelve.api_report_interval", 30.0))
        self.MIN_REPORT_PERIODS = self.config.get("tracker.shelve.min_report_periods", 5)
        self.SAVE_LOCAL_REPORT_WITH_IMAGE = self.config.get("tracker.shelve.save_local_report_with_image", False)

        if not all(
            isinstance(v, (int, float))
            for v in [self.EMPTY_THRESHOLD, self.MAX_MISSING_FRAMES, self.IOU_THRESHOLD, self.API_REPORT_INTERVAL, self.MIN_REPORT_PERIODS]
        ) or not isinstance(self.SAVE_LOCAL_REPORT_WITH_IMAGE, bool):
            self.logger.warning("One or more shelve tracker config values might have unexpected types.")

    def track_detections(self, detections, frame_number: int, current_frame: Optional[np.ndarray] = None):
        """
        Match new detections, update tracks, manage state, and trigger reporting.

        Args:
            detections (sv.Detections): Detections from the current frame.
            frame_number (int): Current frame number.
            current_frame (Optional[np.ndarray]): The current frame, used for reporting.

        Returns:
            list: List of track IDs assigned to each detection.
        """
        # Store the current frame for potential reporting later
        if current_frame is not None:
            self.current_frame = current_frame.copy()
            # No need to log this every frame, only when used for reporting

        # Skip processing for negative frame numbers (e.g., during initialization)
        if frame_number < 0:
            return [-1] * len(detections)

        # Update last run time
        self.last_run_time = time.time()

        matched_indices = self._match_detections_to_tracks(detections)
        track_ids = self._update_tracks(detections, matched_indices, frame_number)
        self._handle_missed_tracks(matched_indices, frame_number)

        # Apply homography mapping if available
        if self.homography_matrix is not None:
            self.process_detections_with_homography(detections, track_ids)

        self._check_and_trigger_reporting()

        return track_ids

    def _match_detections_to_tracks(self, detections) -> Dict[int, int]:
        """Matches detections to existing tracks using IoU."""
        matched_indices = {}  # Stores { detection_idx: track_id }
        used_tracks = set()

        # Avoid modifying detections.xyxy directly if it's immutable
        detection_bboxes = [list(bbox) for bbox in detections.xyxy]

        for track_id, track_data in self.tracked_spots.items():
            if track_id in used_tracks:
                continue

            best_iou = 0.0
            best_detection_idx = -1

            track_bbox = track_data["bounding_box"]

            for det_idx in range(len(detection_bboxes)):
                if det_idx in matched_indices:
                    continue

                iou_val = compute_iou(detection_bboxes[det_idx], track_bbox)
                if iou_val > best_iou and iou_val >= self.IOU_THRESHOLD:
                    best_iou = iou_val
                    best_detection_idx = det_idx

            if best_detection_idx != -1:
                matched_indices[best_detection_idx] = track_id
                used_tracks.add(track_id)

        return matched_indices

    def _update_tracks(self, detections, matched_indices: Dict[int, int], frame_number: int) -> list[int]:
        """Updates existing tracks and creates new ones for unmatched detections."""
        track_ids = [-1] * len(detections)
        # Avoid modifying detections.xyxy directly if it's immutable
        detection_bboxes = [list(bbox) for bbox in detections.xyxy]

        for det_idx in range(len(detection_bboxes)):
            bbox = detection_bboxes[det_idx]
            if det_idx in matched_indices:
                # --- Update Existing Track ---
                track_id = matched_indices[det_idx]
                track_ids[det_idx] = track_id
                self.tracked_spots[track_id]["bounding_box"] = bbox
                self.tracked_spots[track_id]["last_seen"] = frame_number
                # Increment empty count - spot is still considered empty while tracked
                self.tracked_spots[track_id]["empty_count"] += 1

                # Mark as potentially reportable if meets threshold
                if self.tracked_spots[track_id]["empty_count"] >= self.EMPTY_THRESHOLD:
                    if not self.tracked_spots[track_id]["reported"]:
                        self.tracked_spots[track_id]["reported"] = True
                        self.logger.debug(f"Marked track {track_id} as potentially reportable.")
                    # Initialize or increment report period count
                    self.report_counts[track_id] = self.report_counts.get(track_id, 0) + 1
                else:
                    # Reset reported flag if count drops below threshold (e.g., due to earlier logic)
                    self.tracked_spots[track_id]["reported"] = False
                    if track_id in self.report_counts:  # Remove from reporting consideration
                        del self.report_counts[track_id]

            else:
                # --- Create New Track ---
                new_id = self.next_track_id
                self.next_track_id += 1
                self.tracked_spots[new_id] = {
                    "bounding_box": bbox,
                    "first_seen": frame_number,
                    "last_seen": frame_number,
                    "empty_count": 1,  # Start with 1
                    "reported": False,
                    "width_cm": None,
                    "height_cm": None,
                }
                track_ids[det_idx] = new_id
                self.logger.debug(f"Created new track {new_id}")
                # Check if immediately reportable ( unlikely with EMPTY_THRESHOLD > 1 )
                if self.tracked_spots[new_id]["empty_count"] >= self.EMPTY_THRESHOLD:
                    self.tracked_spots[new_id]["reported"] = True
                    self.report_counts[new_id] = 1

        return track_ids

    def _handle_missed_tracks(self, matched_indices: Dict[int, int], frame_number: int):
        """Handles tracks that were not matched in the current frame."""
        # Find track IDs that were *not* used in the matching
        active_track_ids = set(self.tracked_spots.keys())
        matched_track_ids = set(matched_indices.values())
        missed_track_ids = active_track_ids - matched_track_ids

        for track_id in missed_track_ids:
            track_data = self.tracked_spots[track_id]
            missed_frames = frame_number - track_data["last_seen"]

            # Option 1: Decrement empty count (spot reappeared or was false positive)
            # track_data["empty_count"] -= missed_frames # This can quickly negate persistence
            # track_data["empty_count"] = max(0, track_data["empty_count"])

            # Option 2: Consider it truly missed, check for removal
            if missed_frames > self.MAX_MISSING_FRAMES:
                self.logger.debug(f"Removing track {track_id} due to {missed_frames} missed frames.")
                if track_id in self.report_counts:
                    del self.report_counts[track_id]
                if track_id in self.last_successful_report:
                    del self.last_successful_report[track_id]
                del self.tracked_spots[track_id]
            else:
                # If not removed, it's just temporarily missed. Reset its report count.
                # If it was marked as reported, it needs to meet the threshold again.
                if track_id in self.report_counts:
                    self.logger.debug(f"Resetting report count for missed track {track_id}. Will need {self.MIN_REPORT_PERIODS} more periods.")
                    del self.report_counts[track_id]
                # Keep the empty_count, but it won't increment this frame
                # Reset reported flag so it needs to build up count again if it reappears
                track_data["reported"] = False

    def _check_and_trigger_reporting(self):
        """Checks if the reporting interval has passed and triggers reporting if needed."""
        current_time = time.time()
        # Check interval
        if current_time - self.last_api_report_time >= self.API_REPORT_INTERVAL:
            self.logger.info(f"Reporting interval reached for camera {self.camera_name}. Processing report.")
            self._process_reporting(self.current_frame)
            self.last_api_report_time = current_time  # Update time regardless of report success
        else:
            time_to_next = self.API_REPORT_INTERVAL - (current_time - self.last_api_report_time)
            self.logger.debug(f"Time to next report check: {time_to_next:.2f}s")

    def _process_reporting(self, frame: Optional[np.ndarray]) -> bool:
        """Format and send report data."""
        # Check for reportable objects
        reportable_tracks = {
            track_id: count for track_id, count in self.report_counts.items() if count >= self.MIN_REPORT_PERIODS and track_id in self.tracked_spots
        }

        if not reportable_tracks:
            self.logger.info(f"No objects meet the minimum report period ({self.MIN_REPORT_PERIODS}) for reporting.")
            return False

        self.logger.info(f"Found {len(reportable_tracks)} objects meeting report criteria: {list(reportable_tracks.keys())}")

        # Format the payload
        payload = self.format_api_data(reportable_tracks)
        if not payload or not payload.get("objects"):
            self.logger.warning("Formatted payload is empty or has no objects. Skipping report.")
            return False

        # Send the report
        success = self.reporting_service.send_report(payload=payload, camera_name=self.camera_name, frame=frame, save_image=self.SAVE_LOCAL_REPORT_WITH_IMAGE)

        # Update last successful report if successful
        if success:
            self.last_successful_report = {
                track_id: self.tracked_spots[track_id]["bounding_box"] for track_id in reportable_tracks if track_id in self.tracked_spots
            }
            self.logger.info(f"Report successful. Updated last successful report for {len(self.last_successful_report)} tracks.")
        else:
            self.logger.warning("Report failed. Last successful report remains unchanged.")

        return success

    # Update format_api_data to accept the specific tracks to include
    def format_api_data(self, tracks_to_report: Dict[int, int]) -> dict:
        """
        Format the data for reportable tracks according to the API structure.

        Args:
            tracks_to_report (Dict[int, int]): Dictionary {track_id: report_count} of tracks to include.

        Returns:
            dict: Formatted data for API submission, or empty dict if no valid objects.
        """
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        objects = []

        for track_id, count in tracks_to_report.items():
            if track_id not in self.tracked_spots:
                self.logger.warning(f"Track ID {track_id} marked for reporting but not found in tracked_spots. Skipping.")
                continue

            track_data = self.tracked_spots[track_id]
            # Ensure it's still marked as reported (belt-and-suspenders check)
            if not track_data.get("reported", False):
                self.logger.warning(f"Track ID {track_id} in tracks_to_report but not marked as reported internally. Skipping.")
                continue

            bbox = track_data["bounding_box"]
            was_in_previous = False
            if track_id in self.last_successful_report:
                if isinstance(self.IOU_THRESHOLD, float):
                    try:
                        was_in_previous = compute_iou(bbox, self.last_successful_report[track_id]) > self.IOU_THRESHOLD
                    except Exception as e:
                        self.logger.error(f"Error computing IoU for track {track_id} during formatting: {e}")

            width_str = f"{track_data['width_cm']:.1f} cm" if track_data.get("width_cm") is not None else "N/A"
            height_str = f"{track_data['height_cm']:.1f} cm" if track_data.get("height_cm") is not None else "N/A"

            object_data = {
                "bosluk": track_id,
                "bir_onceki_mesajda_olma_durumu": 1 if was_in_previous else 0,
                "Bosluk genislik": width_str,
                "Bosluk yukseklik": height_str,
                "kordinat": [float(coord) for coord in bbox],  # Ensure float conversion
                "report_count": count,
            }
            objects.append(object_data)

        if not objects:
            self.logger.info("No valid objects formatted for the report.")
            return {}

        payload = {
            "shelve_id": self.shelve_id,
            "tespit_saati": current_time,
            "objects": objects,
            "magaza_no": self.magaza_no,
            # report_id will be added in _process_reporting
        }
        return payload

    def update_spot_dimensions(self, track_id, width_cm, height_cm):
        """Update the real-world dimensions of a tracked spot."""
        if track_id in self.tracked_spots:
            self.tracked_spots[track_id]["width_cm"] = width_cm
            self.tracked_spots[track_id]["height_cm"] = height_cm
            self.logger.debug(f"Updated dimensions for track {track_id}: width={width_cm}cm, height={height_cm}cm")
        else:
            self.logger.warning(f"Attempted to update dimensions for non-existent track ID: {track_id}")

    def set_homography(self, homography_matrix, shelve_width_cm, shelve_height_cm):
        """
        Set the homography matrix and shelf dimensions for this tracker.

        Args:
            homography_matrix: The homography matrix as a list or numpy array
            shelve_width_cm: Width of the shelf in centimeters
            shelve_height_cm: Height of the shelf in centimeters
        """
        if isinstance(homography_matrix, list):
            self.homography_matrix = np.array(homography_matrix, dtype=np.float32)
        else:
            self.homography_matrix = homography_matrix

        self.shelve_width_cm = shelve_width_cm
        self.shelve_height_cm = shelve_height_cm
        self.logger.info(f"Set homography matrix for {self.camera_name} with dimensions {shelve_width_cm}x{shelve_height_cm}cm")

    def map_bbox_to_real_world(self, bbox):
        """
        Map a bounding box from pixel coordinates to real-world coordinates using homography.

        Args:
            bbox: Bounding box in pixel coordinates [x1, y1, x2, y2]

        Returns:
            list: Bounding box in real-world coordinates [x1, y1, x2, y2] in centimeters
                 or None if homography is not available
        """
        if self.homography_matrix is None or self.shelve_width_cm is None or self.shelve_height_cm is None:
            self.logger.warning("Cannot map to real world: missing homography matrix or shelf dimensions")
            return None

        # Convert bbox to homogeneous coordinates
        box_pts = np.array(
            [
                [bbox[0], bbox[1]],  # Top-left
                [bbox[0], bbox[3]],  # Bottom-left
                [bbox[2], bbox[1]],  # Top-right
                [bbox[2], bbox[3]],  # Bottom-right
            ],
            dtype=np.float32,
        ).reshape(-1, 1, 2)

        try:
            # Apply homography to transform pixel coordinates to real-world coordinates
            real_pts = cv2.perspectiveTransform(box_pts, self.homography_matrix).reshape(-1, 2)
            real_x_min = np.min(real_pts[:, 0])
            real_y_min = np.min(real_pts[:, 1])
            real_x_max = np.max(real_pts[:, 0])
            real_y_max = np.max(real_pts[:, 1])

            # Convert to shelf coordinates
            real_bbox = [
                self.shelve_width_cm - real_x_max,
                self.shelve_width_cm - real_x_min,
                self.shelve_height_cm - real_y_max,
                self.shelve_height_cm - real_y_min,
            ]

            return real_bbox
        except Exception as e:
            self.logger.error(f"Error mapping bbox to real world: {str(e)}")
            return None

    def process_detections_with_homography(self, detections, track_ids):
        """
        Process detections with homography to get real-world dimensions.

        Args:
            detections: Detections object with bounding boxes
            track_ids: List of track IDs corresponding to detections
        """
        if self.homography_matrix is None:
            return

        for i, det in enumerate(detections.xyxy):
            track_id = track_ids[i]
            if track_id == -1:
                continue  # Skip invalid track IDs

            real_bbox = self.map_bbox_to_real_world(det)
            if real_bbox:
                # Calculate width and height in cm
                real_x1, real_y1, real_x2, real_y2 = real_bbox
                width_cm = abs(real_x2 - real_x1)
                height_cm = abs(real_y2 - real_y1)

                # Update tracker with real-world dimensions
                self.update_spot_dimensions(track_id, width_cm, height_cm)

    def get_tracking_state(self) -> dict:
        """Return the current tracking state."""
        return {
            "tracked_spots": self.tracked_spots,
            "next_track_id": self.next_track_id,
            "report_counts": self.report_counts,
            "last_successful_report": self.last_successful_report,
            "last_api_report_time": self.last_api_report_time,
        }

    def get_reported_spots(self) -> list:
        """Return the list of bounding boxes currently marked as reported."""
        # Return bboxes of tracks currently marked as 'reported' internally
        return [data["bounding_box"] for data in self.tracked_spots.values() if data.get("reported", False)]

    def should_run(self, now: float) -> bool:
        """
        Determines if the tracker should run based on the configured check_interval (in minutes).
        Reads the interval from config.yaml (tracker.shelve.check_interval).
        Args:
            now (float): Current timestamp (time.time()).
        Returns:
            bool: True if enough time has passed since the last run, False otherwise.
        """
        interval_min = self.config.get("tracker.shelve.check_interval", 1)  # minutes
        interval_sec = interval_min * 60
        # Use the last_api_report_time as a proxy for last run, or add a new last_run_time if needed
        last_run = getattr(self, "last_run_time", 0.0)
        return (now - last_run) >= interval_sec

    def should_report(self, now: float) -> bool:
        """
        Determines if the tracker should report based on the configured api_report_interval (in minutes).
        Reads the interval from config.yaml (tracker.shelve.api_report_interval).
        Args:
            now (float): Current timestamp (time.time()).
        Returns:
            bool: True if enough time has passed since the last report, False otherwise.
        """
        interval_min = self.API_REPORT_INTERVAL  # already loaded from config in minutes
        interval_sec = interval_min * 60
        return (now - self.last_api_report_time) >= interval_sec

    def get_preferred_model_id(self) -> str:
        """
        Returns the preferred model ID for this tracker.

        Returns:
            str: The model ID to use for this tracker
        """
        return "empty_shelf"

    def format_report_data(self):
        """
        Format the tracker's state for reporting.

        Returns:
            dict: Formatted data for reporting
        """
        # Get reportable tracks based on MIN_REPORT_PERIODS
        reportable_tracks = {
            track_id: count
            for track_id, count in self.report_counts.items()
            if isinstance(self.MIN_REPORT_PERIODS, int) and count >= self.MIN_REPORT_PERIODS and track_id in self.tracked_spots
        }

        # Use the existing format_api_data method
        return self.format_api_data(reportable_tracks)

    def send_report(self) -> bool:
        """Send a report of the current state of the tracker."""
        return self._process_reporting(self.current_frame)

    def annotate_frame(self, frame, detections, track_ids):
        """
        Annotate a frame with tracker-specific visualizations.

        Args:
            frame (numpy.ndarray): The frame to annotate
            detections: Detections object with bounding boxes
            track_ids (list): List of track IDs assigned to each detection

        Returns:
            numpy.ndarray: The annotated frame
        """
        # Create a copy of the frame to avoid modifying the original
        annotated_frame = frame.copy()

        # Draw bounding boxes and labels for each detection
        for i, det in enumerate(detections.xyxy):
            track_id = track_ids[i] if i < len(track_ids) else -1
            if track_id == -1:
                continue  # Skip invalid track IDs

            # Get the bounding box coordinates
            x1, y1, x2, y2 = map(int, det)

            # Determine color based on reporting status
            color = (0, 255, 0)  # Green for reported
            if track_id not in self.report_counts or self.report_counts[track_id] < self.MIN_REPORT_PERIODS:
                color = (0, 0, 255)  # Red for not reported

            # Draw the bounding box
            cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 2)

            # Add label with track ID and reporting status
            label = f"ID: {track_id}"
            if track_id in self.report_counts:
                label += f" ({self.report_counts[track_id]}/{self.MIN_REPORT_PERIODS})"
            cv2.putText(annotated_frame, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

        return annotated_frame
