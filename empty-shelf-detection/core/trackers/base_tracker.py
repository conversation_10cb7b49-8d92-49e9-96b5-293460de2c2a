"""
Base class for all trackers in the application.
Defines the standard interface that all trackers must implement.

This class follows the tracker-centric approach where:
1. Trackers own their state and domain logic
2. Trackers determine when they should run
3. Trackers process their own ROIs
4. Trackers handle their own reporting
"""

import time
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple

import cv2
import numpy as np
from core import get_config, get_logger
from libs.shape import Shape


class BaseTracker(ABC):
    """
    Abstract base class for all trackers.

    This class defines the standard interface that all trackers must implement.
    It provides common functionality and ensures consistent behavior across
    different tracker implementations.

    In the tracker-centric approach:
    1. Trackers own their state and domain logic
    2. Trackers determine when they should run
    3. Trackers process their own ROIs
    4. Trackers handle their own reporting
    """

    def __init__(self, camera_name: str, roi_type: Optional[str] = None):
        """
        Initialize the tracker.

        Args:
            camera_name (str): Name of the camera this tracker is associated with
            roi_type (str, optional): Type of ROI this tracker handles
        """
        self.camera_name = camera_name
        self.roi_type = roi_type
        self.logger = get_logger(f"{self.__class__.__name__}_{camera_name}")
        self.config = get_config()

        # Timing and state
        self.last_run_time = 0.0
        self.last_report_time = 0.0
        self.last_success_time = time.time()  # Time of last successful processing

        # Error handling
        self.error_count = 0
        self.max_errors = 5  # Maximum number of errors before tracker is disabled

        # Frame storage
        self.current_frame = None  # Store the current frame for reporting

        # ROI information
        self.points = []  # List of points defining the ROI
        self.label = "unknown"  # Label for the ROI
        self.roi_coordinates = None  # (x_min, x_max, y_min, y_max)

        # Run interval in seconds (default: 10 seconds)
        self.run_interval = self.config.get(f"trackers.{self.roi_type}.run_interval", 10.0)

        # Report interval in seconds (default: 60 seconds)
        self.report_interval = self.config.get(f"trackers.{self.roi_type}.report_interval", 60.0)

    @abstractmethod
    def should_run(self, now: float) -> bool:
        """
        Determine if the tracker should run based on time and state.

        Args:
            now (float): Current timestamp (time.time())

        Returns:
            bool: True if the tracker should run, False otherwise
        """
        pass

    @abstractmethod
    def track_detections(self, detections: Any, frame_number: int, current_frame: Optional[Any] = None) -> List[int]:
        """
        Process detections and update tracker state.

        Args:
            detections: Detections object with bounding boxes
            frame_number (int): Current frame number
            current_frame (numpy.ndarray, optional): The current frame

        Returns:
            list: List of track IDs assigned to each detection
        """
        pass

    @abstractmethod
    def get_preferred_model_id(self) -> str:
        """
        Get the preferred model ID for this tracker.

        Returns:
            str: The model ID to use for this tracker
        """
        pass

    @abstractmethod
    def should_report(self, now: float) -> bool:
        """
        Determine if the tracker should report based on time and state.

        Args:
            now (float): Current timestamp (time.time())

        Returns:
            bool: True if the tracker should report, False otherwise
        """
        pass

    @abstractmethod
    def format_report_data(self) -> dict:
        """
        Format the tracker's state for reporting.

        Returns:
            dict: Formatted data for reporting
        """
        pass

    @abstractmethod
    def send_report(self) -> bool:
        """
        Send the report using the appropriate reporter.

        Returns:
            bool: True if the report was sent successfully, False otherwise
        """
        pass

    def reset(self):
        """
        Reset the tracker's state.

        This method should be called when the tracker needs to be reset,
        for example after too many errors.
        """
        self.last_run_time = 0
        self.last_report_time = 0
        self.error_count = 0
        self.logger.info(f"Tracker reset for camera {self.camera_name}")

    def mark_error(self):
        """
        Mark an error in the tracker.

        Returns:
            bool: True if the tracker is now disabled due to too many errors
        """
        self.error_count += 1
        self.logger.warning(f"Tracker error count: {self.error_count}/{self.max_errors}")
        return self.error_count >= self.max_errors

    def is_disabled(self):
        """
        Check if the tracker is disabled due to errors.

        Returns:
            bool: True if the tracker is disabled
        """
        return self.error_count >= self.max_errors

    def initialize(self):
        """
        Initialize the tracker.

        This method should be called during startup to ensure the tracker
        is properly initialized.

        Returns:
            bool: True if initialization was successful
        """
        self.logger.info(f"Initializing tracker for camera {self.camera_name}")
        return True

    def annotate_frame(self, frame: Any, detections: Any, track_ids: List[int]) -> Any:
        """
        Annotate a frame with tracker-specific visualizations.

        Args:
            frame (numpy.ndarray): The frame to annotate
            detections: Detections object with bounding boxes
            track_ids (list): List of track IDs assigned to each detection

        Returns:
            numpy.ndarray: The annotated frame
        """
        # Default implementation does nothing
        return frame

    def set_shape_data(self, shape: Shape) -> None:
        """
        Set shape data for this tracker.

        This method extracts and stores relevant information from a shape object,
        such as points, label, and other properties.

        Args:
            shape: Shape object with properties like points, label, etc.
        """
        # Extract roi_type (may be None for non-ROI annotations)
        if hasattr(shape, "roi_type"):
            self.roi_type = getattr(shape, "roi_type")

        # Extract coordinates and other properties
        if hasattr(shape, "points"):
            self.points = [(p.x(), p.y()) for p in getattr(shape, "points", [])]

        if hasattr(shape, "label"):
            self.label = getattr(shape, "label", "unknown")

        # Calculate ROI coordinates
        self._calculate_roi_coordinates()

        self.logger.info(f"Set shape data for tracker: roi_type={self.roi_type}, label={self.label}")

    def _calculate_roi_coordinates(self) -> None:
        """
        Calculate ROI coordinates from points.

        This method calculates the minimum and maximum x and y coordinates
        from the points defining the ROI.
        """
        if not self.points or len(self.points) < 3:
            self.logger.warning(f"Not enough points to calculate ROI coordinates: {len(self.points) if self.points else 0}")
            return

        try:
            x_coords = [p[0] for p in self.points]
            y_coords = [p[1] for p in self.points]

            # Use floor for min and ceil for max to ensure containment for int conversion
            x_min = int(np.floor(min(x_coords)))
            x_max = int(np.ceil(max(x_coords)))
            y_min = int(np.floor(min(y_coords)))
            y_max = int(np.ceil(max(y_coords)))

            self.roi_coordinates = (x_min, x_max, y_min, y_max)
            self.logger.debug(f"Calculated ROI coordinates: {self.roi_coordinates}")
        except (IndexError, TypeError, ValueError) as e:
            self.logger.error(f"Error calculating ROI coordinates from points {self.points}: {e}")

    def extract_roi(self, frame: np.ndarray) -> Optional[np.ndarray]:
        """
        Extract ROI from a frame.

        This method extracts the region of interest from a frame based on
        the ROI coordinates.

        Args:
            frame: Full frame from the camera

        Returns:
            numpy.ndarray: Extracted ROI or None if extraction failed
        """
        if frame is None or frame.size == 0:
            self.logger.warning("Cannot extract ROI from empty frame")
            return None

        if not self.roi_coordinates:
            self.logger.warning("No ROI coordinates available")
            return None

        try:
            img_h, img_w = frame.shape[:2]
            x_min, x_max, y_min, y_max = self.roi_coordinates

            # Check if ROI is within frame bounds and clamp
            x_min_c = max(0, x_min)
            y_min_c = max(0, y_min)
            x_max_c = min(img_w, x_max)
            y_max_c = min(img_h, y_max)

            if x_max_c <= x_min_c or y_max_c <= y_min_c:
                self.logger.warning(
                    f"Invalid ROI after clamping. " f"Original:({x_min},{y_min},{x_max},{y_max}), " f"Clamped:({x_min_c},{y_min_c},{x_max_c},{y_max_c})."
                )
                return None

            # Extract ROI from the frame
            roi_frame = frame[y_min_c:y_max_c, x_min_c:x_max_c]
            if roi_frame.size == 0:
                self.logger.warning("Extracted ROI frame is empty")
                return None

            return roi_frame
        except Exception as e:
            self.logger.error(f"Error extracting ROI: {e}")
            return None

    def process_frame(self, frame: np.ndarray, frame_number: int = 0) -> Tuple[Optional[Any], Optional[List[int]], Optional[Tuple[int, int, int, int]]]:
        """
        Process a full frame.

        This method:
        1. Extracts the ROI from the frame
        2. Gets the preferred model
        3. Runs inference on the ROI
        4. Tracks detections
        5. Returns the processed ROI, track IDs, and ROI coordinates

        Args:
            frame: Full frame from the camera
            frame_number: Current frame number (optional)

        Returns:
            tuple: (detections, track_ids, roi_coordinates) or (None, None, None) if processing failed
        """
        # Store the current frame for reporting
        self.current_frame = frame

        # Extract ROI
        roi_frame = self.extract_roi(frame)
        if roi_frame is None:
            return None, None, None

        # Get the model manager
        from core import get_model_manager

        model_manager = get_model_manager()

        # Get the preferred model
        model_id = self.get_preferred_model_id()
        model = model_manager.get_model(model_id)

        if not model:
            self.logger.error(f"Failed to load model {model_id}")
            return None, None, None

        try:
            # Run inference
            results = model.predict(roi_frame, verbose=False)

            # Convert to detections
            from supervision.detection.core import Detections

            detections = Detections.from_ultralytics(results[0])

            # Track detections
            track_ids = self.track_detections(detections, frame_number, roi_frame)

            # Update last run time
            self.last_run_time = time.time()
            self.last_success_time = time.time()

            # Return the detections, track IDs, and ROI coordinates
            return detections, track_ids, self.roi_coordinates
        except Exception as e:
            self.logger.error(f"Error processing ROI: {e}")
            self.mark_error()
            return None, None, None
