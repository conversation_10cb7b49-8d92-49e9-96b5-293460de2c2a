"""
Handles loading annotations and matching them with camera configurations.
Provides methods to process cameras with loaded annotations.
"""

import os
from typing import Any, Dict, List, Optional

from core import get_config, get_logger, get_model_manager
from libs.shape import Shape
from managers.file_handler import FileHandler

from .detection_modes import DetectionModeManager


class AnnotationReader:
    """
    Handles loading annotations and matching them with camera configurations
    (match with reads data/cameras_all.json).
    """

    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.config = get_config()
        self.file_handler = FileHandler()  # Initialize the file handler for loading annotations

        # Data structures for loaded data
        self.preloaded_annotations = {}  # Stores all shapes for each camera
        self.frame_counts = {}
        self.matched_camera_urls = {}  # Stores URLs only for cameras with matched annotations

        # Get paths from CoreConfig
        self.annotation_dir = self.config.get("paths.annotation_dir", "./camera")

        # Get the ModelManager instance
        self.model_manager = get_model_manager()

        # Log memory stats
        memory_stats = self.model_manager.get_memory_stats()
        self.logger.info(f"Memory usage: {memory_stats['used_mb']:.1f}MB ({memory_stats['usage_percent']:.1f}%)")

        # Initialize components that might need config
        self.detection_mode_manager = DetectionModeManager.get_instance(lambda key: key)

    def find_and_load_matched_annotations(self) -> List[str]:
        """
        Scans annotation directory, matches found annotations with configured cameras
        (loaded from data/cameras_all.json via CoreConfig),
        and loads data only for the matched cameras.

        Returns:
            A list of camera names for which data was successfully loaded and matched.
        """
        if not self.annotation_dir or not os.path.isdir(self.annotation_dir):
            self.logger.error(f"Cannot load annotations, invalid directory: {self.annotation_dir}")
            return []

        # Get camera details directly from CoreConfig
        configured_urls = self.config.get_camera_urls()
        configured_names = self.config.get_camera_names()

        self.logger.info(f"Scanning for annotations in: {self.annotation_dir}")
        self.logger.info(f"Found {len(configured_names)} cameras in configuration.")

        # Scan annotation directory
        found_annotation_files = []
        try:
            found_annotation_files = [f for f in os.listdir(self.annotation_dir) if f.lower().endswith((".json", ".xml"))]
        except OSError as e:
            self.logger.error(f"Error listing annotation directory {self.annotation_dir}: {e}")
            return []

        annotation_camera_names = {os.path.splitext(f)[0] for f in found_annotation_files}
        self.logger.info(f"Found {len(annotation_camera_names)} potential cameras based on annotation files: {annotation_camera_names}")

        # --- Matching Logic ---
        cameras_to_attempt = [name for name in annotation_camera_names if name in configured_names]
        self.logger.info(f"Cameras with annotations matching configuration: {cameras_to_attempt}")

        if not cameras_to_attempt:
            self.logger.warning("No cameras matched between configuration and annotations.")
            return []

        # --- Load Data for Matched Cameras ---
        self.logger.info(f"Attempting to load data for {len(cameras_to_attempt)} matched cameras...")
        loaded_and_matched_cameras = []
        self.matched_camera_urls = {}  # Reset
        for camera_name in cameras_to_attempt:
            if self.load_camera_data(camera_name):
                loaded_and_matched_cameras.append(camera_name)
                # Store the URL only for cameras we successfully load data for
                url = configured_urls.get(camera_name)  # Should exist based on matching logic
                if url:
                    self.matched_camera_urls[camera_name] = url
                else:
                    # This case should be rare if matching logic is correct
                    self.logger.error(f"URL not found in config for matched camera '{camera_name}' during data loading. This shouldn't happen.")
            else:
                self.logger.warning(f"Failed to load annotation/image data for matched camera: {camera_name}")

        self.logger.info(f"Successfully loaded data for {len(loaded_and_matched_cameras)} cameras: {loaded_and_matched_cameras}")
        return loaded_and_matched_cameras

    def load_camera_data(self, camera_name: str) -> bool:
        """
        Load annotation data for a specific camera.

        Args:
            camera_name: Name of the camera to load data for

        Returns:
            bool: True if data was successfully loaded, False otherwise
        """
        annotation_path_json = os.path.join(self.annotation_dir, f"{camera_name}.json")
        annotation_path_xml = os.path.join(self.annotation_dir, f"{camera_name}.xml")
        annotation_path = None

        # Check for XML file first
        if os.path.exists(annotation_path_xml):
            annotation_path = annotation_path_xml
        # Then check for JSON file
        elif os.path.exists(annotation_path_json):
            annotation_path = annotation_path_json
        else:
            # This case should ideally not happen if called via find_and_load_matched_annotations
            self.logger.warning(f"Annotation file (.json/.xml) not found for {camera_name} in {self.annotation_dir}")
            return False

        self.logger.debug(f"Loading annotation data for {camera_name} from {annotation_path}")

        # Use the file handler to load the annotations
        annotation_data = self.file_handler.load_annotations(annotation_path)

        if not annotation_data:
            self.logger.error(f"Failed to load annotation data from {annotation_path}")
            return False

        # Convert shapes to Shape objects if they aren't already
        shapes = []
        for shape_data in annotation_data.get("shapes", []):
            try:
                # Use Shape.from_dict to convert dictionaries to Shape objects
                if isinstance(shape_data, dict):
                    shape = Shape.from_dict(shape_data)
                elif isinstance(shape_data, Shape):
                    shape = shape_data
                else:
                    self.logger.warning(f"Skipping invalid shape data type: {type(shape_data)}")
                    continue
                shapes.append(shape)
            except Exception as e:
                self.logger.warning(f"Error converting shape: {e}")
                continue

        # Store the shapes directly
        self.preloaded_annotations[camera_name] = shapes
        self.frame_counts[camera_name] = 0

        self.logger.debug(f"Successfully loaded {len(shapes)} shapes for camera: {camera_name}")
        return True

    def get_matched_camera_urls(self) -> Dict[str, str]:
        """Returns the dictionary of camera names to URLs for cameras that had matched annotations."""
        return self.matched_camera_urls
