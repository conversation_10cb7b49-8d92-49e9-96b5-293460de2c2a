---
description: 
globs: *.py,*.json,*.java
alwaysApply: false
---
- Use meaningful names for all code elements.
- Write simple, clear code; avoid complexity.
- Comment non-obvious logic, explaining "why", not just "what"
- Adhere to project style and formatting.
- Decompose large functions into smaller units.
- Design modular, reusable components,Avoid tight coupling.
- Minimize inter-module dependencies.
- Encapsulate logic to prevent side effects.
- Implement robust error handling.
- Handle edge cases and validate inputs.
- Write comprehensive, efficient unit tests.
- Use environment variables for configuration.
- Adjust logging verbosity per environment.
- Ask for clarification when unsure.