---
description: 
globs: 
alwaysApply: true
---
- Always choose the simplest solution.
- Eliminate code duplication by reusing existing functionality(check codebase).
- Write environment-aware code (dev, test, prod).
- Implement only requested or thoroughly understood changes.
- Exhaust current patterns before adopting new ones; remove obsolete code.
- Maintain a clean, organized codebase.
- Avoid one-off scripts; integrate logic into the application.
- Refactor files exceeding 400-500 lines if possible.
- Mock data only for tests.
- Do not modify configuration files without explicit instruction.

