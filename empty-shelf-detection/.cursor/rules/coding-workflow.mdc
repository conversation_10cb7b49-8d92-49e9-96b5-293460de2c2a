---
description: 
globs: 
alwaysApply: true
---
Coding Workflow Preferences:

- Focus solely on task-relevant code; avoid unrelated changes.
- Write comprehensive tests for all major features.
- Preserve existing architecture and patterns unless instructed otherwise.
- Analyze and mitigate the impact of changes on other code areas.
- Integrate new code seamlessly,Be aware of side effects.
- Communicate progress and challenges,Be responsive to feedback,Seek guidance if unsure
- Flag out-of-scope changes for review; do not implement unilaterally.
- Ensure all tests pass before submitting changes.
- Use version control with clear, logical commits.
- Propose architectural improvements for discussion