import os
import sys

# Add project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import libs.resources as resources  # kaldirinca resourcelar dolmuyor.
from gui.ROIGui import MainWindow
from libs.settings import SettingsManager
from managers.file_handler import <PERSON><PERSON>andler
from utilities.utility_functions import run_main_app

# Import from core module
from core import get_logger, setup_logging

# Set up logging
setup_logging()
logger = get_logger("main_gui")


def create_dependencies():
    """Create and configure all dependencies"""
    file_handler = FileHandler()
    setting_handler = SettingsManager()
    setting_handler.load()  # Ensure settings are loaded
    return {"file_handler": file_handler, "setting_handler": setting_handler}


def main():
    """
    Main entry point for the GUI mode of the application.

    This runs the application with a graphical user interface for interactive
    annotation and detection.
    """
    logger.info("Starting GUI mode")
    __appname__ = "hakmarAnnotate"
    logger.info("Creating dependencies")
    dependencies = create_dependencies()
    logger.info("Running main app")
    app, _win = run_main_app(MainWindow, __appname__, dependencies, sys.argv)
    logger.info("App initialized, starting event loop")
    return app.exec_()


if __name__ == "__main__":
    sys.exit(main())
