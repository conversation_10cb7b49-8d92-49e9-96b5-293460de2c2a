# GUI Mode Application Flow

This document outlines the detailed step-by-step flow of the GUI mode of the Empty Shelf Detection application, starting from `main_gui.py`. It focuses on how annotations are created and saved, and how detection models are run, covering key processes and decision points within the application flow.

## Core Principles

The GUI mode is built on these key principles:

1. Interactive annotation creation and editing
2. Real-time camera feed processing
3. Centralized tracker management through DetectionModeManager
4. Shape-based ROI definition for different detection types
5. Visual feedback of detection results

## Detailed Process Flow

### 1. Application Startup

1. The application starts in `main_gui.py`
   - Python path is set up to include the project root
   - Logging is initialized with `setup_logging()`
   - Dependencies are created with `create_dependencies()`
     - `FileHandler` for managing annotation files
     - `SettingsManager` for application settings
   - `run_main_app()` is called to initialize the Qt application
     - Creates a QApplication instance
     - Parses command line arguments
     - Creates the MainWindow instance with dependencies
     - Shows the window

2. `MainWindow.__init__()` in `ROIGui.py` is called
   - UI components are initialized
   - Settings are loaded
   - The startup dialog is shown to select between "camera mode" and "file mode"
   - `DetectionModeManager` singleton is initialized
   - `CameraManager` is created to handle camera operations

3. Mode selection in `setup_startup_dialog()`
   - If camera mode is selected:
     - `initialize_camera_mode()` is called
     - Camera save directory is set up
     - Shelve data is loaded with `load_shelve_data()`
     - Camera mode UI is set up with `setup_camera_mode_ui()`
   - If file mode is selected:
     - File mode UI is set up with `setup_file_mode_ui()`

### 2. Camera Selection and Initialization

1. User selects a camera from the camera tree
   - `on_camera_selected()` is called when a camera is double-clicked
   - Camera data is retrieved from the tree item
   - If there are unsaved changes, `may_continue()` is called to prompt the user

2. Camera initialization in `on_camera_selected()`
   - Previous camera is stopped with `stop_camera()`
   - State is reset with `reset_state()`
   - New camera is loaded with `camera_manager.load_camera()`
   - Video thread is created (either `VideoThread` or `LoopingVideoThread`)
   - Signals are connected:
     - `frame_updated` signal to `update_canvas_frame()`
     - `camera_error` signal to `handle_camera_error_signal()`

3. Camera feed processing in `VideoThread.run()`
   - Camera feed is opened with `cv2.VideoCapture()`
   - Frames are continuously captured in a loop
   - Each frame is processed:
     - If grayscale, converted to BGR with `enhance_grayscale_frame()`
     - If model processing is enabled, processed with `process_frame_with_model()`
     - Converted to QImage with `convert_frame_to_qimage()`
     - Emitted to the UI with `frame_updated.emit()`

4. Frame display in `update_canvas_frame()`
   - QImage is converted to QPixmap
   - Canvas is updated with `canvas.update_image()`

### 3. Annotation Creation

1. User creates annotations (shapes) on the canvas
   - Shapes can be created in different ways:
     - Drawing rectangles directly on the canvas
     - Using homography setup for empty shelf detection
     - Using "Create Presence Check ROI" button

2. Rectangle drawing in `Canvas.mousePressEvent()` and related methods
   - When in create mode, a new shape is started on mouse press
   - Shape points are updated as the mouse moves
   - Shape is finalized on mouse release
   - `shape_completed` signal is emitted

3. Shape completion handling in `MainWindow.shape_completed()`
   - If shape has a predefined ROI type (e.g., from homography):
     - Shape properties are set accordingly
   - Otherwise:
     - Label dialog is shown to get a label for the shape
   - Shape is added to the canvas with `add_label()`
   - Dirty flag is set to indicate unsaved changes

4. Homography setup in `set_homography_mode()`
   - User selects a shelf from predefined shelves
   - User marks four corner points on the image
   - Homography matrix is calculated with `homography_points_selected()`
   - Shape's `roi_type` is set to "emptyshelve"

5. Presence check ROI creation in `create_bounding_box_for_presence_check()`
   - A new shape is created
   - Shape's `roi_type` is set to "presence_check"
   - Shape is added to the canvas

### 4. Annotation Saving

1. User saves annotations with "Save" button or Ctrl+S
   - `save_file()` is called
   - Save directory is determined based on mode:
     - Camera mode: `camera_save_dir`
     - File mode: `default_save_dir`
   - File path is constructed based on image name
   - `_save_file()` is called with the annotation path

2. Annotation saving in `_save_file()`
   - `save_labels()` is called with the annotation file path
   - If successful, state is set to clean with `set_clean()`
   - Status message is updated

3. Label saving in `save_labels()`
   - Shapes are converted to dictionaries with `shape.to_dict()`
   - `file_handler.save_annotations()` is called with:
     - Annotation file path
     - Shape dictionaries
     - Image path and data
     - Color settings
     - Label history
   - Success or error is reported

4. File handler saving in `FileHandler.save_annotations()`
   - Appropriate writer is selected based on file format:
     - PascalVocWriter for XML format
     - YoloWriter for YOLO format
     - CreateMLWriter for JSON format
   - Writer saves the annotations to the file
   - Success or error is returned

### 5. Running Detection Models

1. User clicks "Run Model" button
   - `run_model()` is called
   - If no camera is active, an error message is shown
   - If model is already processing, it's stopped

2. Model processing setup in `run_model()`
   - Relevant shapes are identified (those with `roi_type` set)
   - If no relevant shapes, an error message is shown
   - For each shape:
     - `detection_mode_manager.get_tracker_for_shape()` is called
     - This creates and registers appropriate trackers based on shape properties
   - `camera_manager.start_model_processing()` is called
   - Status message and action states are updated

3. Model processing start in `CameraManager.start_model_processing()`
   - Camera name is retrieved from current thread
   - Tracker registry is checked for registered trackers for this camera
   - If no trackers found, a warning is logged and function returns
   - Model processing flag is set on camera thread with `set_model_processing(True)`

4. Frame processing in `VideoThread.process_frame_with_model()`
   - `DetectionModeManager` instance is retrieved
   - If rate limiting is active, processing is skipped
   - Trackers for this camera are retrieved from the registry
   - For each tracker:
     - `tracker.process_frame()` is called with the current frame
     - Results (detections, track IDs, ROI coordinates) are retrieved
     - Frame is annotated with detections using `annotate_detections()`
   - Processed frame is returned

5. Tracker processing in `BaseTracker.process_frame()`
   - ROI is extracted from the frame
   - Preferred model is determined and loaded
   - Inference is run on the ROI
   - Detections are tracked with `track_detections()`
   - Results are returned

6. Detection display
   - Annotated frame is displayed in the canvas
   - Annotations show bounding boxes and labels for detections

### 6. Stopping Model Processing

1. User clicks "Stop Detection" button (same as "Run Model" button with different text)
   - `run_model()` is called
   - Since model is already processing, it's stopped with `camera_manager.stop_model_processing()`

2. Model processing stop in `CameraManager.stop_model_processing()`
   - Model processing flag is set to False on camera thread
   - Status message and action states are updated

### 7. Camera Shutdown

1. User stops the camera with "Stop Camera" button or by closing the application
   - `stop_camera()` is called
   - Signals are disconnected
   - `camera_manager.stop_current_camera()` is called

2. Camera stopping in `CameraManager.stop_current_camera()`
   - Model processing is stopped first
   - Camera thread is stopped with `current_camera_thread.stop()`
   - Thread is waited for with `current_camera_thread.wait()`
   - Current camera thread is set to None

## Conditional Logic (if/else) Summary

### In MainWindow.run_model:
```python
if not self.camera_manager.current_camera_thread:
    self.status(self.get_str("noCameraActive"))
    return

# If already processing, stop
if self.camera_manager.is_model_processing():
    self.camera_manager.stop_model_processing()
    self.status(self.get_str("modelProcessingStopped"))
    return

relevant_shapes = [s for s in self.canvas.shapes if hasattr(s, "roi_type") and s.roi_type]

if not relevant_shapes:
    self.status(self.get_str("noROIsDefined"))
    return
```

### In CameraManager.start_model_processing:
```python
# Check if we have any registered trackers for this camera
tracker_registry = self.detection_mode_manager.get_all_trackers()
has_trackers = False

for tracker_type, trackers in tracker_registry.items():
    if camera_name in trackers:
        has_trackers = True
        break

if not has_trackers:
    self.logger.warning(f"No trackers registered for camera {camera_name}")
    return False
```

### In VideoThread.process_frame_with_model:
```python
if not self._should_process_frame():
    self.logger.debug("Skipping model processing due to rate limiting.")
    # Return the original frame as no processing or annotation happens here now
    return frame

# Process frame using each registered tracker
for tracker_type, tracker in trackers_for_camera.items():
    # Check if the tracker has the expected process_frame method
    if not hasattr(tracker, "process_frame"):
        self.logger.error(f"Tracker {tracker_type} for {self.camera_name} missing 'process_frame' method.")
        continue
```

## Key Decision Points

1. **Mode Selection**: Camera mode vs. File mode at startup
2. **Camera Selection**: Which camera to process from the camera tree
3. **ROI Type Determination**: Based on creation method (homography, presence check button, etc.)
4. **Tracker Selection**: Based on shape's `roi_type` property
5. **Model Processing**: Whether to process frames with models based on user action
6. **Frame Rate Limiting**: Whether to process a frame based on elapsed time since last processing
7. **Annotation Format**: Which format to use for saving annotations (XML, YOLO, JSON)
8. **Error Handling**: How to handle camera errors and model processing errors

## Key Components and Their Roles

### MainWindow (ROIGui.py)
- Main application window that handles UI interactions
- Manages the canvas for displaying and editing shapes
- Coordinates between UI actions and backend processing
- Handles annotation saving and loading

### CameraManager (managers/CameraManager.py)
- Manages camera threads and their settings
- Handles starting/stopping model processing
- Loads annotations for cameras
- Handles camera errors

### VideoThread (helpers/ProcessCamera.py)
- Captures frames from the camera feed
- Processes frames with models and trackers
- Annotates detections on the frame
- Emits processed frames to the UI

### DetectionModeManager (core/detection_modes.py)
- Singleton class that manages detection modes, models, and trackers
- Maps shapes to appropriate trackers
- Maintains a registry of created trackers
- Provides centralized access to trackers

### Canvas (libs/canvas.py)
- Displays the camera feed and annotations
- Handles drawing and editing shapes
- Manages homography point selection
- Emits signals for shape creation and editing

### FileHandler (managers/file_handler.py)
- Handles saving and loading annotations
- Supports multiple annotation formats
- Manages file dialogs and file operations

## Differences from Seamless Mode

The GUI mode differs from the seamless mode in several ways:

1. **User Interaction**: GUI mode allows interactive creation and editing of annotations, while seamless mode loads annotations from files
2. **Camera Management**: GUI mode processes one camera at a time, while seamless mode can process multiple cameras concurrently
3. **Tracker Creation**: In GUI mode, trackers are created when the user starts model processing, while in seamless mode, trackers are created when annotations are loaded
4. **Visualization**: GUI mode displays processed frames in real-time, while seamless mode typically runs headless
5. **Error Handling**: GUI mode shows error messages in dialogs, while seamless mode logs errors and continues processing
6. **Scheduling**: GUI mode processes frames as they arrive, while seamless mode uses a scheduler to coordinate tracker execution
7. **Reporting**: GUI mode focuses on visualization, while seamless mode focuses on reporting to APIs or saving results locally
