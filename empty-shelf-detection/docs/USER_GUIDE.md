# Empty Shelve Detection - User Guide

## Overview

The Empty Shelve Detection system is designed to automatically detect and report empty spots on retail shelves using computer vision. The system processes video feeds from cameras, identifies empty spots, and reports them to an API for further action.

## Features

- Real-time detection of empty spots on retail shelves
- Tracking of empty spots across video frames
- Reporting of empty spots to an API
- Support for multiple cameras
- Configuration via YAML files
- Detailed logging with log rotation

## Installation

### Prerequisites

- Python 3.8 or higher
- OpenCV
- CUDA-compatible GPU (recommended for faster processing)

### Installation Steps

1. Clone the repository:


2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Download the YOLO model:
   ```bash
   mkdir -p detection-models
   # Download the model file to detection-models/1.best.170.pt
   ```

## Configuration

### Main Configuration

The main configuration file is located at `config/config.yaml`. If it doesn't exist, it will be created with default values when the application is first run.

Example configuration:

```yaml
tracker:
  empty_threshold: 5
  max_missing_frames: 3
  iou_threshold: 0.3
  api_report_interval: 30
  min_report_periods: 5

api:
  url: 'http://*********:9980/api/RaftakiBosKonumlar'
  timeout: 10
  max_retries: 3
  retry_delay: 5

camera:
  frame_width: 1920
  frame_height: 1080
  fps: 15
  buffer_size: 1

model:
  default_path: 'detection-models/1.best.170.pt'

logging:
  level: 'INFO'
  format: '%(asctime)s - %(levelname)s - %(name)s - %(message)s'
  max_size: 10485760
  backup_count: 5
```

### Camera Configuration

Camera configuration is stored in `cameras_all.json`. This file contains information about the cameras, including their RTSP URLs.

Example:

```json
{
  "nvrs": {
    "*********": {
      "cameras": [
        {
          "name": "camera1",
          "rtsp_url": "rtsp://username:password@*********:554/stream1"
        },
        {
          "name": "camera2",
          "rtsp_url": "rtsp://username:password@*********:554/stream2"
        }
      ]
    }
  }
}
```

### Shelve Dimensions

Shelve dimensions are stored in `data/predefined_shelves.txt`. This file contains information about the dimensions of different shelves.

Example:

```
magaza_no,REYON_NO,REYON_ADI,RAF KATSAYISI (Sistem),"REYON UZUNLUĞU (Fiili)","RAF KAT SAYISI (Fiili)"
4,37,A.Bebe Bisküvi 1.25 mt,6,1,25,6
4,282,A.Cips & Pringles 0.50 mt,6,1,2,7
```

## Usage

### Running in Seamless Mode

To run the application in seamless mode (without GUI):

```bash
python main.py --annotation-dir /path/to/annotations --model-path /path/to/model.pt --camera-list camera1,camera2 --show-image --save-dir /path/to/save
```

Arguments:
- `--annotation-dir`: Directory containing annotation files
- `--model-path`: Path to the YOLO model file
- `--camera-list`: Comma-separated list of camera names to process
- `--show-image`: Show annotated images
- `--save-dir`: Directory to save processed results
- `--config`: Path to configuration file

### Annotations

Annotations are stored in the annotation directory specified by `--annotation-dir`. Each camera should have an annotation file with the same name as the camera (e.g., `camera1.json`).

The annotation file should contain information about the regions of interest (ROIs) and homography matrices for mapping to real-world coordinates.

## Logging

Logs are stored in the `logs` directory. The following log files are created:

- `app.log`: Main application log
- `tracker.log`: Tracker-specific log
- `camera.log`: Camera-specific log
- `api.log`: API-specific log
- `model.log`: Model-specific log

Logs are rotated when they reach 10 MB, and up to 5 backup files are kept.

## Troubleshooting

### Common Issues

1. **No cameras with valid annotations found**
   - Make sure the annotation directory contains valid annotation files
   - Check that the camera names in the annotation files match the camera names in the configuration

2. **No RTSP URL found for camera**
   - Make sure the camera is configured in `cameras_all.json`
   - Check that the RTSP URL is correct

3. **Error loading YOLO model**
   - Make sure the model file exists at the specified path
   - Check that the model file is a valid YOLO model

### Logs

Check the log files in the `logs` directory for more detailed error messages.

## Development

### Running Tests

To run the unit tests:

```bash
python -m unittest discover tests
```

### Adding a New Camera

1. Create an annotation file for the camera
2. Add the camera to `cameras_all.json`
3. Run the application with the new camera

### Modifying the Tracker

The tracker is implemented in `tracker.py`. To modify the tracker:

1. Edit the `SeamlessTracker` class in `tracker.py`
2. Update the configuration in `config/config.yaml` if necessary
3. Run the tests to make sure everything works

## License

This project is licensed under the MIT License - see the LICENSE file for details.
