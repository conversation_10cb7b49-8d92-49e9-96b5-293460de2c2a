# Model Memory Optimization Guide

This document provides information about model memory consumption and optimization techniques used in the Empty Shelf Detection application.

## Memory Consumption Statistics

Based on our testing, here are the approximate memory requirements for the models used in the application:

| Model | Purpose | Size on Disk | Memory Usage | GPU Memory (if available) |
|-------|---------|--------------|--------------|---------------------------|
| empty_shelf_model | Empty shelf detection | ~14 MB | ~11.5 MB | ~200-300 MB |
| people_counting_model | People presence detection | ~15 MB | ~12.1 MB | ~200-300 MB |
| people_presence_model | Alternative people detection | ~100 MB | ~98.9 MB | ~500-600 MB |

**Note**: Memory usage can vary depending on the hardware, operating system, and PyTorch version.

## Memory Optimization Techniques

The application uses several techniques to optimize memory usage:

### 1. Model Caching

The `ModelManager` implements a caching mechanism that prevents loading the same model multiple times. This is particularly useful when:

- Multiple cameras use the same model
- Switching between detection modes that use the same model
- Restarting processing with the same model

```python
# Example of model caching in action
model1 = model_manager.get_model("empty_shelf")
model2 = model_manager.get_model("empty_shelf")  # Returns the same model instance
```

### 2. Automatic Memory Management

When memory usage approaches a configurable threshold (default: 85%), the `ModelManager` automatically unloads the least recently used models to free up memory:

```python
# Configuration in config.yaml
model:
  max_cached_models: 3  # Maximum number of models to keep in memory
  memory_threshold: 0.85  # 85% memory usage threshold
```

### 3. Half-Precision (FP16)

For compatible hardware (NVIDIA GPUs with Tensor Cores), the application can use half-precision (FP16) to reduce memory usage:

```python
# Configuration in config.yaml
model:
  use_half_precision: true  # Use FP16 precision when supported
```

This can reduce memory usage by up to 50% with minimal impact on accuracy.

### 4. Model Optimization for Inference

The application applies inference-specific optimizations to the models:

```python
# Configuration in config.yaml
model:
  optimize_for_inference: true  # Apply inference optimizations
```

These optimizations include:
- Fusion of operations
- Removal of training-specific layers
- Quantization of weights

### 5. On-Demand Loading

Models are loaded only when needed, based on the current detection mode:

```python
# Example from CameraManager.py
model_id = model_id_mapping.get(detection_mode, "empty_shelf")
self.current_model_id = model_id
```

### 6. Explicit Garbage Collection

After unloading a model, the application forces garbage collection to ensure memory is freed:

```python
# From ModelManager.py
def unload_model(self, model_id):
    # ... unload model ...
    del model
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
```

## Best Practices for Managing Model Memory

1. **Use the ModelManager**: Always use the `ModelManager` to load models instead of loading them directly.

2. **Unload Unused Models**: Call `model_manager.unload_model(model_id)` when a model is no longer needed.

3. **Monitor Memory Usage**: Use `model_manager.get_memory_stats()` to monitor memory usage.

4. **Adjust Configuration**: Tune the `max_cached_models` and `memory_threshold` parameters based on your hardware.

5. **Use Smaller Models**: Consider using smaller models for less critical tasks.

6. **Batch Processing**: Process multiple frames in a batch when possible to improve efficiency.

## Troubleshooting Memory Issues

If you encounter memory issues:

1. **Check Memory Usage**: Use `model_manager.get_memory_stats()` to check current memory usage.

2. **Reduce Max Cached Models**: Lower the `max_cached_models` value in the configuration.

3. **Increase Memory Threshold**: Lower the `memory_threshold` value to trigger earlier unloading.

4. **Enable Half-Precision**: Set `use_half_precision: true` if using compatible hardware.

5. **Use Smaller Models**: Consider using smaller models or quantized versions.

6. **Process Fewer Cameras**: Reduce the number of simultaneous camera streams.

## Example: Memory Usage Monitoring

```python
# Get memory statistics
memory_stats = model_manager.get_memory_stats()

# Log memory usage
logger.info(f"Memory usage: {memory_stats['used_mb']:.1f}MB ({memory_stats['usage_percent']:.1f}%)")
logger.info(f"Models loaded: {memory_stats['models_loaded']}/{memory_stats['max_models']}")

# Get information about loaded models
model_info = model_manager.get_model_info()
for info in model_info:
    logger.info(f"Model: {info['id']}, Size: {info['memory_mb']:.1f}MB, Last used: {info['last_used']}")
```

## Conclusion

By implementing these memory optimization techniques, the application can efficiently manage multiple models while minimizing memory usage. This is particularly important when running on resource-constrained devices or when processing multiple camera streams simultaneously.
