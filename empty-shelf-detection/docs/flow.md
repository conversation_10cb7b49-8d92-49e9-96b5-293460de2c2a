# Detailed Step-by-Step Guide: Tracker-Centric Architecture

This document provides a detailed, step-by-step breakdown of the tracker-centric approach implemented in the Empty Shelf Detection application. It includes all conditional logic and decision points to help you understand the complete flow.

## Core Principles

The tracker-centric approach is built on these key principles:

1. Trackers own their state and domain logic
2. Trackers determine when they should run
3. Camera connections are opened on-demand
4. A central scheduler coordinates tracker execution
5. The DetectionModeManager maintains a central registry of all trackers

## Detailed Process Flow

### 1. Application Startup

1. The application starts in `main_seamless.py`
2. Configuration is loaded from `config.yaml`
3. Logging is set up
4. The `AnnotationReader` is initialized
5. The `TrackerScheduler` is initialized

### 2. Annotation Loading

1. `AnnotationReader.find_and_load_matched_annotations()` is called
2. The method scans the annotation directory for XML and JSON files
3. It loads camera configurations from `cameras_all.json`
4. For each annotation file:
   - If the file is XML, it's parsed using `pascal_voc_io.py`
   - If the file is JSON, it's parsed directly
   - The parsed data is converted to `Shape` objects
   - Each `Shape` object contains properties like `roi_type`, `points`, `label`, etc.
5. Camera names from annotations are matched with camera configurations
6. For each matched camera:
   - Annotations are stored in `preloaded_annotations` dictionary
   - Camera URLs are stored in `matched_camera_urls` dictionary
7. During annotation loading, trackers are created for each shape:
   - `DetectionModeManager.get_tracker_for_shape(camera_name, shape)` is called
   - This creates and registers the appropriate tracker based on the shape's `roi_type`
   - If the tracker is newly created, it's also initialized with shape data by calling `set_shape_data` and `initialize`

### 3. Tracker Initialization

1. `TrackerScheduler.initialize_trackers_from_annotations()` is called during application startup
   - This is the main entry point for tracker initialization in the application
   - It calls `AnnotationReader.find_and_load_matched_annotations()` to load annotations
   - It ensures all trackers are properly created and registered
2. For each camera and its shapes:
   - If no shapes are found for a camera, a warning is logged and the camera is skipped
   - For each shape:
     - `DetectionModeManager.get_tracker_for_shape(camera_name, shape)` is called
     - If no tracker is created, a warning is logged and the shape is skipped
     - Note: The tracker is already initialized with shape data in `get_tracker_for_shape` if it's newly created
3. All trackers are now registered in the central registry in `DetectionModeManager`
4. The method returns `True` if initialization was successful, `False` otherwise

> **Note:** There is some duplication between the annotation loading process and the tracker initialization process.
> Both call `get_tracker_for_shape`, but this is intentional to ensure all trackers are properly created and registered.
> The `initialize_trackers_from_annotations` method is the recommended way to initialize trackers in the application.

### 4. Scheduler Startup

1. `TrackerScheduler.start()` is called
2. The scheduler starts three types of threads:
   - A scheduler thread that periodically checks which trackers should run
   - Worker threads (default: 4) that process tracker tasks
   - A stats thread that logs statistics periodically
3. The scheduler thread runs the `_scheduler_loop` method
4. Worker threads run the `_worker_loop` method
5. The stats thread runs the `_stats_loop` method

### 5. Scheduler Loop

1. The scheduler loop runs continuously while `self.running` is True
2. In each iteration:
   - Get the current time
   - Get all trackers from the central registry
   - Get camera URLs from the `AnnotationReader`
   - If no camera URLs are found, log an error and wait before retrying
   - For each tracker:
     - If `tracker.is_disabled()` returns True, skip this tracker
     - Check if a task for this camera and tracker type is already running:
       - Create a task key from the camera name and tracker type
       - If a task with this key is already running and was started within the last 5 seconds:
         - Skip creating a new task for this tracker
     - If `tracker.should_run(current_time)` returns True:
       - Get the camera URL for this tracker's camera
       - If no URL is found, log a warning and skip this tracker
       - Create a `TrackerTask` with the camera name, URL, and tracker
       - Mark this task as running in the running_tasks dictionary
       - Add the task to the task queue
       - Initialize statistics for this camera and tracker if needed
   - Sleep for 1 second to avoid CPU spinning

### 6. Worker Loop

1. Each worker thread runs continuously while `self.running` is True
2. In each iteration:
   - Try to get a task from the queue with a timeout of 1 second
   - If no task is available (queue.Empty exception), continue to the next iteration
   - Process the task using `_process_tracker_task`
   - Update statistics for this camera and tracker
   - If an error occurs during processing:
     - Log the error
     - Mark the error in the tracker using `_mark_tracker_error`
     - Update error statistics
   - Remove the task from the running_tasks dictionary
   - Mark the task as done in the queue

### 7. Processing a Tracker Task

1. `_process_tracker_task` is called with a `TrackerTask`
2. Open the camera connection using `cv2.VideoCapture`
3. If the camera cannot be opened, log an error and return
4. Read a single frame from the camera
5. If frame reading fails, log an error and return
6. Process the frame with the tracker:
   - Call `tracker.process_frame(frame)`
   - If processing fails (roi_frame is None), log a warning and return
   - If processing succeeds, log a debug message
7. Check if the tracker should report:
   - If `tracker.should_report(current_time)` returns True:
     - Call `tracker.send_report()`
     - If reporting succeeds, log an info message
     - If reporting fails, log an error
8. Save the frame if needed using `_save_frame_if_needed`
9. Always release the camera connection in the finally block

### 8. Tracker Process Frame

1. `BaseTracker.process_frame` is called with a frame
2. Store the current frame for potential reporting later
3. Extract the ROI from the frame using `extract_roi`
4. If ROI extraction fails, return (None, None)
5. Get the model manager
6. Get the preferred model ID using `get_preferred_model_id`
7. Load the model using `model_manager.get_model(model_id)`
8. If model loading fails, log an error and return (None, None)
9. Run inference on the ROI
10. Convert the results to a Detections object
11. Track detections using `track_detections`
12. Update the last run time and last success time
13. Return the processed ROI and track IDs
14. If an error occurs during processing:
    - Log the error
    - Mark the error using `mark_error`
    - Return (None, None)

### 9. Tracker ROI Extraction

1. `BaseTracker.extract_roi` is called with a frame
2. If the frame is None or empty, log a warning and return None
3. If no ROI coordinates are available, log a warning and return None
4. Get the frame dimensions
5. Get the ROI coordinates
6. Check if the ROI is within frame bounds and clamp if necessary:
   - x_min_c = max(0, x_min)
   - y_min_c = max(0, y_min)
   - x_max_c = min(img_w, x_max)
   - y_max_c = min(img_h, y_max)
7. If the clamped ROI is invalid (x_max_c <= x_min_c or y_max_c <= y_min_c):
   - Log a warning and return None
8. Extract the ROI from the frame: `frame[y_min_c:y_max_c, x_min_c:x_max_c]`
9. If the extracted ROI is empty, log a warning and return None
10. Return the extracted ROI
11. If an error occurs during extraction:
    - Log the error and return None

### 10. Tracker Detection Tracking

1. `ShelfTracker.track_detections` is called with detections, frame number, and current frame
2. If current_frame is not None, store a copy for potential reporting
3. If frame_number is negative, return [-1] * len(detections)
4. Update the last run time
5. Match detections to existing tracks using `_match_detections_to_tracks`
6. Update tracks with matched detections using `_update_tracks`
7. Handle missed tracks using `_handle_missed_tracks`
8. If homography matrix is available, process detections with homography
9. Check and trigger reporting if needed
10. Return the list of track IDs

### 11. Tracker Reporting

1. `ShelfTracker._check_and_trigger_reporting` is called
2. Get the current time
3. If the time since the last report is greater than or equal to the reporting interval:
   - Log an info message
   - Call `_process_reporting` with the current frame
   - Update the last report time regardless of report success
4. Otherwise:
   - Calculate the time to the next report
   - Log a debug message

### 12. Processing a Report

1. `ShelfTracker._process_reporting` is called with a frame
2. Check for reportable tracks:
   - A track is reportable if its report count is greater than or equal to MIN_REPORT_PERIODS
   - If no reportable tracks are found, log an info message and return False
3. Format the payload using `format_api_data`
4. If the payload is empty or has no objects, log a warning and return False
5. Send the report using `reporting_service.send_report`
6. If the report is successful:
   - Update the last successful report
   - Log an info message
7. Otherwise:
   - Log a warning
8. Return the success status

### 13. Formatting API Data

1. `ShelfTracker.format_api_data` is called with tracks to report
2. Get the current time
3. Initialize an empty list for objects
4. For each track ID and count:
   - If the track ID is not in tracked_spots, log a warning and skip
   - Get the track data
   - If the track is not marked as reported, log a warning and skip
   - Get the bounding box
   - Check if the track was in the previous report:
     - If IOU_THRESHOLD is a float, compute IoU with the previous report
     - If IoU is greater than IOU_THRESHOLD, mark as in previous report
   - Format width and height strings
   - Create an object data dictionary
   - Add the object data to the objects list
5. If no valid objects are found, log an info message and return an empty dict
6. Create a payload dictionary with:
   - shelve_id
   - tespit_saati (detection time)
   - objects
   - magaza_no (store number)
7. Return the payload

### 14. Homography Processing

1. `ShelfTracker.process_detections_with_homography` is called with detections and track IDs
2. If homography matrix is None, return immediately
3. For each detection and its track ID:
   - If the track ID is -1, skip this detection
   - Map the bounding box to real-world coordinates using `map_bbox_to_real_world`
   - If mapping succeeds:
     - Calculate width and height in centimeters
     - Update the tracker with real-world dimensions using `update_spot_dimensions`

### 15. Mapping to Real World

1. `ShelfTracker.map_bbox_to_real_world` is called with a bounding box
2. If homography matrix or shelf dimensions are missing, log a warning and return None
3. Convert the bounding box to homogeneous coordinates
4. Apply the homography transformation using `cv2.perspectiveTransform`
5. Calculate the minimum and maximum x and y coordinates
6. Convert to shelf coordinates
7. Return the real-world bounding box
8. If an error occurs during mapping:
   - Log the error and return None

### 16. Error Handling

1. `BaseTracker.mark_error` is called when an error occurs
2. Increment the error count
3. Log a warning with the current error count
4. Return True if the error count is greater than or equal to max_errors, False otherwise
5. If a tracker is disabled due to too many errors:
   - It will be skipped in the scheduler loop
   - It can be reset using `reset` method

### 17. Application Shutdown

1. When the application receives a keyboard interrupt or encounters a fatal error:
   - `TrackerScheduler.stop()` is called
   - The running flag is set to False
   - All threads are joined with a timeout
   - The scheduler is stopped
   - The application exits

## Conditional Logic (if/else) Summary

### In TrackerScheduler._scheduler_loop:
```python
if not camera_urls:
    self.logger.error("No camera URLs found. Cannot schedule tasks.")
    time.sleep(5.0)  # Wait longer before retrying
    continue

if tracker.is_disabled():
    continue

# Check if a task for this camera and tracker type is already running
task_key = (camera_name, tracker.__class__.__name__)

with self.running_tasks_lock:
    # If a task is already running and it's recent (within last 5 seconds)
    if task_key in self.running_tasks:
        last_run_time = self.running_tasks[task_key]
        if current_time - last_run_time < 5.0:  # 5 second threshold
            self.logger.debug(f"Skipping task creation for {camera_name} with {tracker.__class__.__name__} - already running")
            continue

if tracker.should_run(current_time):
    # Get the camera URL
    rtsp_url = camera_urls.get(camera_name)
    if not rtsp_url:
        self.logger.warning(f"No URL found for camera {camera_name}, skipping")
        continue

    # Create and queue the task
    # Mark this task as running
    # Initialize stats
```

### In TrackerScheduler._process_tracker_task:
```python
# Open the camera
cap = cv2.VideoCapture(rtsp_url)
if not cap.isOpened():
    self.logger.error(f"Failed to open camera: {camera_name} at {rtsp_url}")
    return

# Read a single frame
ret, frame = cap.read()
if not ret:
    self.logger.error(f"Failed to read frame from camera: {camera_name}")
    return

# Process the frame with the tracker
roi_frame, _ = tracker.process_frame(frame)
if roi_frame is None:
    self.logger.warning(f"Failed to process frame for {camera_name} with tracker {task.tracker_type}")
    return

self.logger.debug(f"Processed frame for {camera_name} with tracker {task.tracker_type}")

# Check if the tracker should report
if tracker.should_report(current_time):
    try:
        tracker.send_report()
        self.logger.info(f"Sent report for {camera_name} with tracker {task.tracker_type}")
    except Exception as e:
        self.logger.error(f"Error sending report for {camera_name}: {str(e)}")
```

### In TrackerScheduler._mark_tracker_error:
```python
is_disabled = tracker.mark_error()
if is_disabled:
    self.logger.warning(f"Tracker {tracker.__class__.__name__} for camera {tracker.camera_name} disabled due to too many errors")
```

### In TrackerScheduler._save_frame_if_needed:
```python
if not self.config.get_bool("runtime.save_frames", False):
    return

# Get the output directory
output_dir = self.config.get("paths.output", "./output")
if not output_dir:
    return
```

### In BaseTracker.extract_roi:
```python
if frame is None or frame.size == 0:
    self.logger.warning("Cannot extract ROI from empty frame")
    return None

if not self.roi_coordinates:
    self.logger.warning("No ROI coordinates available")
    return None

# Check if ROI is within frame bounds and clamp
if x_max_c <= x_min_c or y_max_c <= y_min_c:
    self.logger.warning(
        f"Invalid ROI after clamping. "
        f"Original:({x_min},{y_min},{x_max},{y_max}), "
        f"Clamped:({x_min_c},{y_min_c},{x_max_c},{y_max_c})."
    )
    return None

# Extract ROI from the frame
roi_frame = frame[y_min_c:y_max_c, x_min_c:x_max_c]
if roi_frame.size == 0:
    self.logger.warning("Extracted ROI frame is empty")
    return None
```

### In BaseTracker.process_frame:
```python
# Get the preferred model
model_id = self.get_preferred_model_id()
model = model_manager.get_model(model_id)

if not model:
    self.logger.error(f"Failed to load model {model_id}")
    return None, None
```

### In ShelfTracker.track_detections:
```python
# Skip processing for negative frame numbers
if frame_number < 0:
    return [-1] * len(detections)

# Apply homography mapping if available
if self.homography_matrix is not None:
    self.process_detections_with_homography(detections, track_ids)
```

### In ShelfTracker._update_tracks:
```python
for det_idx in range(len(detection_bboxes)):
    bbox = detection_bboxes[det_idx]
    if det_idx in matched_indices:
        # --- Update Existing Track ---
        # Increment empty count - spot is still considered empty while tracked
        if self.tracked_spots[track_id]["empty_count"] >= self.EMPTY_THRESHOLD:
            if not self.tracked_spots[track_id]["reported"]:
                self.tracked_spots[track_id]["reported"] = True
                self.logger.debug(f"Marked track {track_id} as potentially reportable.")
            # Initialize or increment report period count
            self.report_counts[track_id] = self.report_counts.get(track_id, 0) + 1
        else:
            # Reset reported flag if count drops below threshold
            self.tracked_spots[track_id]["reported"] = False
            if track_id in self.report_counts:  # Remove from reporting consideration
                del self.report_counts[track_id]
    else:
        # --- Create New Track ---
        # Check if immediately reportable
        if self.tracked_spots[new_id]["empty_count"] >= self.EMPTY_THRESHOLD:
            self.tracked_spots[new_id]["reported"] = True
            self.report_counts[new_id] = 1
```

### In ShelfTracker._handle_missed_tracks:
```python
for track_id in missed_track_ids:
    track_data = self.tracked_spots[track_id]
    missed_frames = frame_number - track_data["last_seen"]

    # Consider it truly missed, check for removal
    if missed_frames > self.MAX_MISSING_FRAMES:
        self.logger.debug(f"Removing track {track_id} due to {missed_frames} missed frames.")
        if track_id in self.report_counts:
            del self.report_counts[track_id]
        if track_id in self.last_successful_report:
            del self.last_successful_report[track_id]
        del self.tracked_spots[track_id]
    else:
        # If not removed, it's just temporarily missed. Reset its report count.
        if track_id in self.report_counts:
            self.logger.debug(f"Resetting report count for missed track {track_id}. Will need {self.MIN_REPORT_PERIODS} more periods.")
            del self.report_counts[track_id]
        # Reset reported flag so it needs to build up count again if it reappears
        track_data["reported"] = False
```

### In ShelfTracker._check_and_trigger_reporting:
```python
if current_time - self.last_api_report_time >= self.API_REPORT_INTERVAL:
    self.logger.info(f"Reporting interval reached for camera {self.camera_name}. Processing report.")
    self._process_reporting(self.current_frame)
    self.last_api_report_time = current_time  # Update time regardless of report success
else:
    time_to_next = self.API_REPORT_INTERVAL - (current_time - self.last_api_report_time)
    self.logger.debug(f"Time to next report check: {time_to_next:.2f}s")
```

### In ShelfTracker._process_reporting:
```python
# Check for reportable objects
reportable_tracks = {
    track_id: count for track_id, count in self.report_counts.items()
    if count >= self.MIN_REPORT_PERIODS and track_id in self.tracked_spots
}

if not reportable_tracks:
    self.logger.info(f"No objects meet the minimum report period ({self.MIN_REPORT_PERIODS}) for reporting.")
    return False

# Format the payload
payload = self.format_api_data(reportable_tracks)
if not payload or not payload.get("objects"):
    self.logger.warning("Formatted payload is empty or has no objects. Skipping report.")
    return False

# Update last successful report if successful
if success:
    self.last_successful_report = {
        track_id: self.tracked_spots[track_id]["bounding_box"]
        for track_id in reportable_tracks if track_id in self.tracked_spots
    }
    self.logger.info(f"Report successful. Updated last successful report for {len(self.last_successful_report)} tracks.")
else:
    self.logger.warning("Report failed. Last successful report remains unchanged.")
```

### In ShelfTracker.format_api_data:
```python
for track_id, count in tracks_to_report.items():
    if track_id not in self.tracked_spots:
        self.logger.warning(f"Track ID {track_id} marked for reporting but not found in tracked_spots. Skipping.")
        continue

    track_data = self.tracked_spots[track_id]
    # Ensure it's still marked as reported (belt-and-suspenders check)
    if not track_data.get("reported", False):
        self.logger.warning(f"Track ID {track_id} in tracks_to_report but not marked as reported internally. Skipping.")
        continue

    was_in_previous = False
    if track_id in self.last_successful_report:
        if isinstance(self.IOU_THRESHOLD, float):
            try:
                was_in_previous = compute_iou(bbox, self.last_successful_report[track_id]) > self.IOU_THRESHOLD
            except Exception as e:
                self.logger.error(f"Error computing IoU for track {track_id} during formatting: {e}")

if not objects:
    self.logger.info("No valid objects formatted for the report.")
    return {}
```

### In ShelfTracker.process_detections_with_homography:
```python
if self.homography_matrix is None:
    return

for i, det in enumerate(detections.xyxy):
    track_id = track_ids[i]
    if track_id == -1:
        continue  # Skip invalid track IDs

    real_bbox = self.map_bbox_to_real_world(det)
    if real_bbox:
        # Calculate width and height in cm
        real_x1, real_y1, real_x2, real_y2 = real_bbox
        width_cm = abs(real_x2 - real_x1)
        height_cm = abs(real_y2 - real_y1)

        # Update tracker with real-world dimensions
        self.update_spot_dimensions(track_id, width_cm, height_cm)
```

### In ShelfTracker.map_bbox_to_real_world:
```python
if self.homography_matrix is None or self.shelve_width_cm is None or self.shelve_height_cm is None:
    self.logger.warning("Cannot map to real world: missing homography matrix or shelf dimensions")
    return None
```

### In TrackerScheduler.initialize_trackers_from_annotations:
```python
# Load annotations
loaded_cameras = self.reader.find_and_load_matched_annotations()
if not loaded_cameras:
    raise RuntimeError("No cameras found with matching annotations")

# Process each camera's shapes
for camera_name, shapes in self.reader.preloaded_annotations.items():
    if not shapes:
        self.logger.warning(f"No shapes found for camera {camera_name}")
        continue

    # Create trackers for each shape
    for shape in  shapes:
        # Get or create a tracker for this shape
        tracker = self.detection_mode_manager.get_tracker_for_shape(camera_name, shape)
        if not tracker:
            self.logger.warning(f"Failed to create tracker for shape in camera {camera_name}")
            continue

# Count and log the initialized trackers
all_trackers = self.detection_mode_manager.get_all_trackers()
tracker_counts = {tracker_type: len(trackers) for tracker_type, trackers in all_trackers.items()}
total_trackers = sum(tracker_counts.values())

if total_trackers == 0:
    raise RuntimeError("No trackers were created during initialization")
```

## Key Decision Points

1. **Tracker Selection**: Based on shape's `roi_type` property
2. **Tracker Scheduling**: Based on `should_run()` method
3. **Task Deduplication**: Based on camera name and tracker type
4. **Reporting Timing**: Based on `should_report()` method
5. **Error Handling**: Based on error count and `mark_error()` method
6. **Track Management**: Based on IoU matching and missing frame count
7. **Report Criteria**: Based on empty count and report period count
8. **Homography Application**: Based on availability of homography matrix

This detailed breakdown covers all the conditional logic and decision points in the tracker-centric approach. You can use this to identify parts you might want to remove or modify.
