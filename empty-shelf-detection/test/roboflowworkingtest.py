import inference
import os
import cv2
from dotenv import load_dotenv
from roboflow import <PERSON><PERSON><PERSON>
from inference import get_model
import supervision as sv
from ultralytics import Y<PERSON><PERSON>



def apply_transparent_red(frame, xmin, ymin, xmax, ymax):
    overlay = frame.copy()
    cv2.rectangle(overlay, (xmin, ymin), (xmax, ymax), (0, 0, 255), -1)  # Draw the transparent red area
    cv2.rectangle(overlay, (xmin, ymin), (xmax, ymax), (255, 255, 255), 1)  # Draw the white rectangle border
    alpha = 0.5  # Transparency factor (0.0 to 1.0)
    cv2.addWeighted(overlay, alpha, frame, 1 - alpha, 0, frame)
    return frame


dotenv_path = os.path.join(os.path.dirname(__file__), '.env.example') 
load_dotenv(dotenv_path)

os.makedirs("datasets", exist_ok=True)
print(os.environ.get("ROBOFLOW_API_KEY"));

from roboflow import Roboflow
rf = Roboflow(api_key=os.environ.get("ROBOFLOW_API_KEY"))

project = rf.workspace("thesis-dpgss").project("out-of-stock-tnjj1")

model = YOLO('empty-shelf-detection/detection-models/2class_best.pt')


image_file = "empty-shelf-detection/images/tumEkranYag2.png"
#image_file = "C:/developer/shelf/ShelfSight/Empty-Spaces-Detection-in-Shelf-Data-5/train/images/0a434e0d-02f7-4d1e-b679-767df40fe8cf_jpg.rf.ac238b311af20a0236ad76410bf82344.jpg"

image = cv2.imread(image_file)




# run inference on our chosen image, image can be a url, a numpy array, a PIL image, etc.
result = model.predict(image)



detections = sv.Detections.from_ultralytics(result[0])

label_annotator = sv.LabelAnnotator()
bounding_box_annotator = sv.BoxAnnotator()


annotated_image = bounding_box_annotator.annotate(scene=image, detections=detections)
annotated_image = label_annotator.annotate(scene=annotated_image, detections=detections)
save_path = "C:/developer/shelf/ShelfSight/Empty-Spaces-Detection-in-Shelf-Data-5/train/images/0a434e0d-02f7-4d1e-b679-767df40fe8cf_jpg.rf.ac238b311af20a0236ad76410bf82344.jpg"
sv.plot_image(image=annotated_image, size=(16, 16))
cv2.imwrite(save_path, annotated_image)


#model.download("pt","empty-shelf-detection/detection-models/thesis-dpgss.pt")