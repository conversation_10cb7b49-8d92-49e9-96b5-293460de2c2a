import os
import cv2
from inference import get_model
import supervision as sv
from ultralytics import YOL<PERSON>


from PIL import Image
import torch
import torchvision
from ultralytics import YOLO
import random 
import xml.etree.ElementTree as ET

def load_pascal_voc_annotations(annotation_path):
    """
    Load annotations from a Pascal VOC XML file.
    """
    tree = ET.parse(annotation_path)
    root = tree.getroot()
    boxes = []
    for obj in root.findall('object'):
        # You can filter by class name if needed
        # name = obj.find('name').text
        bbox = obj.find('bndbox')
        x_min = int(bbox.find('xmin').text)
        y_min = int(bbox.find('ymin').text)
        x_max = int(bbox.find('xmax').text)
        y_max = int(bbox.find('ymax').text)
        boxes.append([x_min, y_min, x_max, y_max])
    return boxes

# Load the model
model = YOLO('empty-shelf-detection/detection-models/best2class.pt')

def perform_inference_on_rois(image_path, annotation_path):
    """
    Performs inference only on the ROIs specified in the annotations.
    """
    global model

    img = cv2.imread(image_path)
    height, width, _ = img.shape

    # Load annotations
    boxes = load_pascal_voc_annotations(annotation_path)
    # If using YOLO annotations:
    # boxes = load_yolo_annotations(annotation_path, width, height)

    all_results = []

    for box in boxes:
        x_min, y_min, x_max, y_max = box
        # Crop the ROI
        roi = img[y_min:y_max, x_min:x_max]

        # Convert ROI to PIL Image
        roi_pil = Image.fromarray(cv2.cvtColor(roi, cv2.COLOR_BGR2RGB))

        # Run inference on the ROI
        results = model(roi_pil)

        # Adjust bounding boxes to the original image coordinates
        for result in results:
            for detection in result.boxes:
                # Adjust the coordinates
                detection.xyxy[:, [0, 2]] += x_min  # x coordinates
                detection.xyxy[:, [1, 3]] += y_min  # y coordinates

        all_results.extend(results)

    # Combine results from all ROIs
    combined_boxes = []
    combined_scores = []
    combined_cls = []
    for result in all_results:
        combined_boxes.extend(result.boxes.xyxy.cpu().tolist())
        combined_scores.extend(result.boxes.conf.cpu().tolist())
        combined_cls.extend(result.boxes.cls.cpu().tolist())

    if combined_boxes:
        # Apply NMS
        combined_boxes = torch.tensor(combined_boxes)
        combined_scores = torch.tensor(combined_scores)
        combined_cls = torch.tensor(combined_cls)
        keep_indices = torchvision.ops.nms(combined_boxes, combined_scores, iou_threshold=0.5)
        return combined_boxes[keep_indices], combined_scores[keep_indices], combined_cls[keep_indices]
    else:
        # Handle the case where there are no detections
        return [], [], []

# Define paths
data_dir = "empty-shelf-detection/images"  # Your dataset image directory
annotation_dir = "empty-shelf-detection/annotations"  # Your annotations directory
save_dir = "empty-shelf-detection/saved_results/run4"  # Save directory

# Get a list of all image files in the directory
image_files = [f for f in os.listdir(data_dir) if f.endswith(('.jpg', '.png', '.JPG'))]

# Process and save the images
for filename in image_files:
    print(filename)
    image_path = os.path.join(data_dir, filename)
    annotation_path = os.path.join(annotation_dir, os.path.splitext(filename)[0] + '.xml')  # Adjust extension if needed

    if not os.path.exists(annotation_path):
        print(f"No annotation for {filename}, skipping.")
        continue

    # Perform inference on ROIs
    combined_boxes, combined_scores, combined_cls = perform_inference_on_rois(image_path, annotation_path)

    # Load the original image using OpenCV
    img = cv2.imread(image_path)

    # Draw detections on the original image
    for box, score, cls in zip(combined_boxes, combined_scores, combined_cls):
        x1, y1, x2, y2 = map(int, box)
        cv2.rectangle(img, (x1, y1), (x2, y2), (0, 255, 0), 2)  # Green bounding box
        label = f"{model.names[int(cls)]}: {score:.2f}"
        cv2.putText(img, label, (x1, max(y1 - 10, 0)), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

    # Save the annotated image
    os.makedirs(save_dir, exist_ok=True)
    cv2.imwrite(os.path.join(save_dir, filename), img)


