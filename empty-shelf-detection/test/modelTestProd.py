import os
import cv2
from inference import get_model
import supervision as sv
from ultralytics import YOLO

import random
from PIL import Image


model = YOLO('empty-shelf-detection/detection-models/1.best.170.pt')

image_file = "empty-shelf-detection/images/640x640roi.JPG"
image = cv2.imread(image_file)

# run inference on our chosen image, image can be a url, a numpy array, a PIL image, etc.
result = model.predict(image)
detections = sv.Detections.from_ultralytics(result[0])

label_annotator = sv.LabelAnnotator()
bounding_box_annotator = sv.BoxAnnotator()


annotated_image = bounding_box_annotator.annotate(scene=image, detections=detections)
annotated_image = label_annotator.annotate(scene=annotated_image, detections=detections)
save_path = "C:/developer/shelf/empty-shelf-detection/saved_results/1.jpg"
#sv.plot_image(image=annotated_image, size=(16, 16))
#cv2.imwrite(save_path, annotated_image)





# Define paths
data_dir = "empty-shelf-detection/images"  # Your dataset image directory
save_dir = "empty-shelf-detection/saved_results/run2"  # Save directory



# Get a list of all image files in the directory
image_files = [f for f in os.listdir(data_dir) if f.endswith(('.jpg', '.png','.JPG'))]

# Randomly select 10 image files (or fewer if there are fewer than 10 images)
selected_files = random.sample(image_files, min(100, len(image_files)))

# Process and save the selected images
for filename in selected_files:
    image_path = os.path.join(data_dir, filename)

    # Run YOLOv8 inference on the image
    results = model(image_path)

    # Get the image with detections plotted
    img_with_detections = results[0].plot()

    # Save the processed image to the desired directory
    img_with_detections = cv2.cvtColor(img_with_detections, cv2.COLOR_BGR2RGB)
    Image.fromarray(img_with_detections).save(os.path.join(save_dir, filename))
    print(f"Processed and saved: {filename}")

