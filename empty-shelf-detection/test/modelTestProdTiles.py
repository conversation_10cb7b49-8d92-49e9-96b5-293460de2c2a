import os
import cv2
from inference import get_model
import supervision as sv
from ultralytics import YOL<PERSON>


from PIL import Image
import torch
import torchvision
from ultralytics import YOLO
import random 

# Load the model
model = YOLO('empty-shelf-detection/detection-models/best2class.pt')

def perform_tiled_inference(image_path, tile_size=640, overlap=0.2):
    """
    Performs tiled inference on a large image using the provided YOLOv8 model.

    Args:
        image_path: Path to the image file.
        tile_size: Size of each tile (square).
        overlap: Overlap ratio between adjacent tiles (0 to 1).

    Returns:
        combined_results: Combined results from all tiles after Non-Maximum Suppression (NMS).
    """
    global model

    img = Image.open(image_path)
    width, height = img.size
    stride = int(tile_size * (1 - overlap))

    all_results = []

    # Iterate through the image with a sliding window approach
    for y in range(0, height, stride):
        for x in range(0, width, stride):
            x2 = min(x + tile_size, width)
            y2 = min(y + tile_size, height)
            tile = img.crop((x, y, x2, y2))
           
            if (x2 - x) < 140 or (y2 - y) < 140:
                print('shortcut')
                continue 

            # Run inference on the tile

            results = model(tile)

    
            for result in results:
                for box in result.boxes:
                    box.xyxy.data += torch.tensor([x, y, x, y], device=box.xyxy.device)

            all_results.append(results)



   # Combine results from all tiles
    combined_boxes = []
    combined_scores = []
    combined_cls = []
    for results in all_results:
        for result in results:
            combined_boxes.extend(result.boxes.xyxy.cpu().tolist())
            combined_scores.extend(result.boxes.conf.cpu().tolist())
            combined_cls.extend(result.boxes.cls.cpu().tolist())
    
    if combined_boxes:
        # Apply NMS
        combined_boxes = torch.tensor(combined_boxes)
        combined_scores = torch.tensor(combined_scores)
        combined_cls = torch.tensor(combined_cls)
        keep_indices = torchvision.ops.nms(combined_boxes, combined_scores, iou_threshold=0.5)
        return combined_boxes[keep_indices], combined_scores[keep_indices], combined_cls[keep_indices]

    else:
        # Handle the case where there are no detections across all tiles
        combined_results = results[0]  # Take the first result object as a template
         

        return  combined_results.boxes[:0], torch.tensor([]), torch.tensor([])







# Define paths
data_dir = "empty-shelf-detection/images"  # Your dataset image directory
save_dir = "empty-shelf-detection/saved_results/run4"  # Save directory

# Get a list of all image files in the directory
image_files = [f for f in os.listdir(data_dir) if f.endswith(('.jpg', '.png','.JPG'))]

# Randomly select 10 image files (or fewer if there are fewer than 10 images)
selected_files = random.sample(image_files, min(100, len(image_files)))

# Process and save the selected images
for filename in selected_files:
    print(filename)
    image_path = os.path.join(data_dir, filename)
    # Perform tiled inference
    combined_boxes, combined_scores, combined_cls = perform_tiled_inference(image_path)

    # Load the original image using OpenCV
    img = cv2.imread(image_path)

    # Draw detections on the original image
    for box, score, cls in zip(combined_boxes, combined_scores, combined_cls):
        x1, y1, x2, y2 = box.int().tolist()
        cv2.rectangle(img, (x1, y1), (x2, y2), (0, 255, 0), 2)  # Green bounding box
        label = f"{model.names[int(cls)]}: {score:.2f}"
        cv2.putText(img, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

    # Save the annotated image
    cv2.imwrite(os.path.join(save_dir,filename), img)


