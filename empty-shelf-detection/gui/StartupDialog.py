import os
from PyQt5.QtWidgets import QDialog, QVBoxLayout, QPushButton

class StartupDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Uygulama Anotasyon Tipini Seçiniz.")
        layout = QVBoxLayout()
        
        self.camera_button = QPushButton("Camera Anotasyon")
        self.file_button = QPushButton("Foto Annotasyon")
        
        layout.addWidget(self.camera_button)
        layout.addWidget(self.file_button)
        
        self.setLayout(layout)
        
        self.camera_button.clicked.connect(self.accept)
        self.file_button.clicked.connect(self.reject)