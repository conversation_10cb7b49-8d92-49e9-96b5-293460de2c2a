#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os.path
import platform
from cProfile import label
from functools import partial

from controller.state_manager import AppState
from core import DetectionModeManager, get_config, get_logger, setup_logging
from gui.ShelveSelectorDialog import ShelveSelectorDialog
from gui.StartupDialog import StartupDialog
from helpers.CameraSettingsDialog import CameraSettingsDialog
from helpers.gui_setup_helpers import load_init_settings
from helpers.WindowMixin import WindowMixin
from libs.canvas import Canvas
from libs.colorDialog import ColorDialog
from libs.constants import *
from libs.create_ml_io import JSON_EXT
from libs.default_label_combobox import Default<PERSON>abelComboBox
from libs.hashableQListWidgetItem import HashableQListWidgetItem
from libs.labelDialog import LabelDialog
from libs.lightWidget import LightWidget
from libs.pascal_voc_io import XML_EXT
from libs.SettingsInterface import SettingsInterface
from libs.shape import DEFAULT_FILL_COLOR, DEFAULT_LINE_COLOR, Shape
from libs.utils import *
from libs.yolo_io import TXT_EXT
from libs.zoomWidget import ZoomWidget
from managers.CameraManager import CameraManager
from managers.dock_manager import DockManager
from managers.FileHandlerInterface import FileHandlerInterface
from utilities.gui_utilities import *
from utilities.HomoGraphyFunctions import homography_points_selected
from utilities.utility_functions import get_format_meta, load_predefined_classes, load_shelve_data, scan_all_images

# Set up logging
setup_logging()
logger = get_logger("MainWindow")


class MainWindow(QMainWindow, WindowMixin):
    FIT_WINDOW, FIT_WIDTH, MANUAL_ZOOM = list(range(3))

    def _setup_ui(self):
        # UI elements common to both modes
        self.setWindowTitle(self.get_str(self.__appname__))
        self.use_default_label_checkbox = QCheckBox(self.get_str("useDefaultLabel"))
        self.use_default_label_checkbox.setChecked(False)
        self.default_label_combo_box = DefaultLabelComboBox(self, items=self.label_hist)
        self.use_difficult = False
        self.diffc_button = QCheckBox(self.get_str("useDifficult"))
        self.diffc_button.setChecked(False)
        self.diffc_button.stateChanged.connect(self.button_state)
        self.edit_button = QToolButton(self)
        self.edit_button.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextBesideIcon)

        self.zoom_widget = ZoomWidget()
        self.zoom_widget.setWhatsThis(
            "Zoom in or out of the image. Also accessible with %s and %s from the canvas." % (format_shortcut("Ctrl+[-+]"), format_shortcut("Ctrl+Wheel"))
        )
        self.zoom_widget.setEnabled(False)
        self.zoom_widget.valueChanged.connect(self.paint_canvas)

        self.statusBar().addPermanentWidget(self.mode_label)

    def setup_canvas(self):
        self.canvas = Canvas(parent=self)
        self.canvas.zoomRequest.connect(self.zoom_request)
        self.canvas.lightRequest.connect(self.light_request)
        self.canvas.set_drawing_shape_to_square(self.settings.get(SETTING_DRAW_SQUARE, False))
        self.scroll_area = QScrollArea(self)
        self.scroll_area.setWidget(self.canvas)
        self.scroll_area.setWidgetResizable(True)
        self.scroll_bars = {Qt.Orientation.Vertical: self.scroll_area.verticalScrollBar(), Qt.Orientation.Horizontal: self.scroll_area.horizontalScrollBar()}
        self.canvas.scrollRequest.connect(self.scroll_request)
        self.canvas.newShape.connect(self.new_shape)
        self.canvas.shapeMoved.connect(self.set_dirty)
        self.canvas.selectionChanged.connect(self.shape_selection_changed)
        self.canvas.drawingPolygon.connect(self.toggle_drawing_sensitive)
        self.setCentralWidget(self.scroll_area)

    def setup_common_actions(self):
        action = partial(new_action, self)
        self.action = action
        tr = self.get_str

        # Define actions - initial enabled state will be set by update_action_states()
        self.actions.save = action(tr("save"), self.save_file, "Ctrl+S", "save", tr("saveDetail"))
        format_meta = get_format_meta(self.label_file_format)
        self.actions.save_format = self.action(
            format_meta[0],
            self.change_format,
            "Ctrl+Y",
            format_meta[1],
            tr("changeSaveFormat"),
            enabled=True,
        )  # Format toggle always enabled

        self.actions.hide_all = action(tr("hideAllBox"), partial(self.toggle_polygons, False), "Ctrl+H", "hide", tr("hideAllBoxDetail"))
        self.actions.show_all = action(tr("showAllBox"), partial(self.toggle_polygons, True), "Ctrl+A", "hide", tr("showAllBoxDetail"))
        self.actions.open_annotation = action(tr("openAnnotation"), self.open_annotation_dialog, "Ctrl+Shift+O", "open", tr("openAnnotationDetail"))
        self.actions.copy_prev_bounding = action(tr("copyPrevBounding"), self.copy_previous_bounding_boxes, "Ctrl+V", "copy", tr("copyPrevBounding"))
        self.actions.delete = action(tr("delBox"), self.delete_selected_shape, "Delete", "delete", tr("delBoxDetail"))
        self.actions.copy = action(tr("dupBox"), self.copy_selected_shape, "Ctrl+D", "copy", tr("dupBoxDetail"))

        self.actions.editMode = action(tr("editBox"), self.set_edit_mode, "Ctrl+J", "edit", tr("editBoxDetail"), checkable=True)  # Make checkable
        self.actions.createMode = action(tr("crtBox"), self.set_create_mode, "Ctrl+w", "new", tr("crtBoxDetail"), checkable=True)  # Make checkable
        self.actions.create = action(tr("crtBox"), self.create_shape, "w", "new", tr("crtBoxDetail"))

        self.actions.edit = action(tr("editLabel"), self.edit_label, "Ctrl+E", "edit", tr("editLabelDetail"))

        self.edit_button.setDefaultAction(self.actions.edit)
        # Ensure only one of createMode or editMode can be active at a time
        self.create_edit_mode_group = QActionGroup(self)
        self.create_edit_mode_group.addAction(self.actions.createMode)
        self.create_edit_mode_group.addAction(self.actions.editMode)
        self.create_edit_mode_group.setExclusive(True)

        self.actions.editMode.setChecked(False)
        self.actions.createMode.setChecked(False)

        # Common actions
        self.quit_action = action(tr("quit"), self.close, "Ctrl+Q", "quit", tr("quitApp"))
        self.show_info_action = action(tr("info"), self.show_info_dialog, None, "help", tr("info"))

        # Zoom actions
        self.zoom = QWidgetAction(self)
        self.zoom.setDefaultWidget(self.zoom_widget)  # type: ignore
        self.actions.zoom_in_action = action(tr("zoomin"), partial(self.add_zoom, 10), "Ctrl++", "zoom-in", tr("zoominDetail"))
        self.actions.zoom_out_action = action(tr("zoomout"), partial(self.add_zoom, -10), "Ctrl+-", "zoom-out", tr("zoomoutDetail"))
        self.actions.fit_window_action = action(tr("fitWin"), self.set_fit_window, "Ctrl+F", "fit-window", tr("fitWinDetail"), checkable=True)
        self.actions.fit_width_action = action(tr("fitWidth"), self.set_fit_width, "Ctrl+Shift+F", "fit-width", tr("fitWidthDetail"), checkable=True)
        self.actions.zoom_org_action = action(tr("originalsize"), partial(self.set_zoom, 100), "Ctrl+=", "zoom", tr("originalsizeDetail"))

        # Group zoom controls into a list for easier toggling.
        self.zoom_actions = (
            self.zoom_widget,
            self.actions.zoom_in_action,
            self.actions.zoom_out_action,
            self.actions.zoom_org_action,
            self.actions.fit_window_action,
            self.actions.fit_width_action,
        )
        self.zoom_mode = self.MANUAL_ZOOM
        self.scalers = {
            self.FIT_WINDOW: self.scale_fit_window,
            self.FIT_WIDTH: self.scale_fit_width,
            self.MANUAL_ZOOM: lambda: 1,
        }

        # Light actions
        self.light_widget = LightWidget(tr("lightWidgetTitle"))
        self.light_widget.setWhatsThis(
            "Brighten or darken current image. Also accessible with %s and %s from the canvas."
            % (format_shortcut("Ctrl+Shift+[-+]"), format_shortcut("Ctrl+Shift+Wheel"))
        )
        self.light_widget.valueChanged.connect(self.paint_canvas)

        self.light = QWidgetAction(self)
        self.light.setDefaultWidget(self.light_widget)
        self.light_brighten_action = action(tr("lightbrighten"), partial(self.add_light, 10), "Ctrl+Shift++", "light_lighten", tr("lightbrightenDetail"))
        self.light_darken_action = action(tr("lightdarken"), partial(self.add_light, -10), "Ctrl+Shift+-", "light_darken", tr("lightdarkenDetail"))
        self.light_org_action = action(tr("lightreset"), partial(self.set_light, 50), "Ctrl+Shift+=", "light_reset", tr("lightresetDetail"), checkable=True)
        self.light_org_action.setChecked(True)  # Initial check state

        self.actions.light_brighten_action = self.light_brighten_action
        self.actions.light_darken_action = self.light_darken_action
        self.actions.light_org_action = self.light_org_action

        # Group light controls into a list for easier toggling.
        self.light_actions = (self.light_widget, self.light_brighten_action, self.light_darken_action, self.light_org_action)
        # Advanced Mode
        self.advanced_mode_action = action(tr("advancedMode"), self.toggle_advanced_mode, "Ctrl+Shift+A", "expert", tr("advancedModeDetail"), checkable=True)

        self.menus.view = self.menu(self.get_str("menu_view"))  # type: ignore
        add_actions(self.menus.view, (self.advanced_mode_action,))
        self.actions.advancedMode = self.advanced_mode_action
        self.actions.advancedMode.setChecked(not self._beginner)

        # Auto Saving Mode
        self.auto_saving_action = action(tr("autoSaveMode"), self.toggle_auto_saving, "Ctrl+Alt+S", "save", tr("autoSaveModeDetail"), checkable=True)
        add_actions(self.menus.view, (self.auto_saving_action,))
        self.actions.auto_saving = self.auto_saving_action
        self.actions.auto_saving.setChecked(self.settings.get(SETTING_AUTO_SAVE, False))

        # Single Class Mode
        self.single_class_mode_action = action(
            tr("singleClsMode"), self.toggle_single_class_mode, "Ctrl+Shift+S", "single_class", tr("singleClsMode"), checkable=True
        )
        add_actions(self.menus.view, (self.single_class_mode_action,))
        self.actions.single_class_mode = self.single_class_mode_action
        self.actions.single_class_mode.setChecked(self.settings.get(SETTING_SINGLE_CLASS, False))
        self.lastLabel = None

        # Display Label Option
        self.actions.display_label_option = action(
            tr("displayLabel"), self.toggle_paint_labels_option, "Ctrl+Shift+P", "label", tr("displayLabelDetail"), checkable=True
        )

        add_actions(self.menus.view, (self.actions.display_label_option,))

        self.actions.display_label_option.setChecked(self.settings.get(SETTING_PAINT_LABEL, True))

        # Draw Squares Option
        self.draw_squares_option_action = action(
            tr("drawSquares"), self.toggle_draw_square, "Ctrl+Shift+R", "draw_square", tr("drawSquaresDetail"), checkable=True  # Assuming this key exists
        )
        add_actions(self.menus.view, (self.draw_squares_option_action,))
        self.actions.draw_squares_option = self.draw_squares_option_action
        self.actions.draw_squares_option.setChecked(self.settings.get(SETTING_DRAW_SQUARE, False))

        # Define shape color actions
        self.actions.shapeLineColor = action(tr("shapeLineColor"), self.choose_shape_line_color, icon="color_line", tip=tr("shapeLineColorDetail"))
        self.actions.shapeFillColor = action(tr("shapeFillColor"), self.choose_shape_fill_color, icon="color", tip=tr("shapeFillColorDetail"))

        self.actions.run_model = action(self.get_str("runModel"), self.run_model, "Ctrl+M", "run_model", self.get_str("runModel"))

        # Actions that should be enabled when shapes are present
        self.actions.onShapesPresent = [self.actions.delete, self.actions.copy, self.actions.edit, self.actions.shapeLineColor, self.actions.shapeFillColor]

        self.actions.onLoadActive = [
            self.actions.save,
            self.actions.save_as,
            self.actions.close,
            self.actions.create,
            self.actions.createMode,
            self.actions.editMode,
            self.actions.create_bounding_box_for_presence_check,
            self.actions.zoom_in_action,
            self.actions.zoom_out_action,
            self.actions.fit_window_action,
            self.actions.fit_width_action,
            self.actions.zoom_org_action,
            self.actions.light_brighten_action,
            self.actions.light_darken_action,
            self.actions.light_org_action,
            self.actions.hide_all,
            self.actions.show_all,
            self.actions.editMode,
            self.actions.display_label_option,
            self.actions.single_class_mode,
            self.actions.auto_saving,
            self.actions.draw_squares_option,
            self.actions.advancedMode,
        ]

        # Actions specific to File Mode
        self.actions.fileModeOnly = [
            self.actions.open,
            self.actions.open_dir,
            self.actions.save_as,
            self.actions.close,
            self.actions.delete_image,
            self.actions.reset_all,
        ]

        # Actions specific to Camera Mode
        self.actions.cameraModeOnly = [
            self.actions.start_camera,
            self.actions.stop_camera,
            self.actions.pause_camera,
            self.actions.resume_camera,
            self.actions.set_homography,
            self.actions.adjust_camera_settings,
            self.actions.create_bounding_box_for_presence_check,  # Specific creation type for camera
        ]

        self.actions.beginnerContext = (
            self.actions.create,  # Keep create for beginner
            self.actions.create_bounding_box_for_presence_check,
            self.actions.edit,
            self.actions.copy,
            self.actions.delete,
        )

        self.actions.advancedContext = (
            self.actions.createMode,
            self.actions.editMode,
            self.actions.edit,
            self.actions.copy,
            self.actions.delete,
            self.actions.shapeLineColor,
            self.actions.shapeFillColor,
        )

    def setup_file_mode_actions(self):
        action = partial(new_action, self)
        tr = self.get_str

        # Actions specific to file mode
        self.actions.open = action(tr("openFile"), self.open_file, "Ctrl+O", "open", tr("openFileDetail"))
        self.actions.open_dir = action(tr("openDir"), self.open_dir_dialog, "Ctrl+U", "open", tr("openDir"))

        self.actions.save_as = action(tr("saveAs"), self.save_file_as, "Ctrl+Shift+S", "save-as", tr("saveAsDetail"))

        self.actions.close = action(tr("closeCur"), self.close_file, "Ctrl+W", "close", tr("closeCurDetail"))
        self.actions.delete_image = action(tr("deleteImg"), self.delete_image, "Ctrl+Shift+D", "close", tr("deleteImgDetail"))
        self.actions.reset_all = action(tr("resetAll"), self.reset_all, None, "resetall", tr("resetAllDetail"))

    def setup_camera_mode_actions(self):
        action = partial(new_action, self)
        tr = self.get_str

        # Actions specific to camera mode
        self.actions.start_camera = action(tr("startCamera"), self.start_camera, "Ctrl+Shift+C", "camera_start", tr("startCamera"))
        self.actions.stop_camera = action(tr("stopCamera"), self.stop_camera, "Ctrl+Shift+X", "camera_stop", tr("stopCamera"))

        self.actions.pause_camera = action(tr("pauseCamera"), self.pause_camera, "Ctrl+Shift+P", "camera_pause", tr("pauseCamera"))
        self.actions.resume_camera = action(tr("resumeCamera"), self.resume_camera, "Ctrl+Shift+R", "camera_resume", tr("resumeCamera"))
        self.actions.set_homography = action(tr("setHomography"), self.set_homography_mode, "Ctrl+Shift+H", "homography_icon", tr("setHomography"))
        self.actions.create_bounding_box_for_presence_check = action(
            self.get_str("crtCoveragePoint"), self.create_bounding_box_for_presence_check, "Ctrl+4", "eye", self.get_str("crtCoveragePointDetail")
        )
        self.actions.adjust_camera_settings = action(
            tr("adjustCameraSettings"), self.adjust_camera_settings, "Ctrl+Shift+A", "camera_settings", tr("adjustCameraSettingsDetail")
        )

    def setup_common_menus(self):
        tr = self.get_str

        # Initialize all menus
        self.menus.file = self.menu(tr("menu_file"))  # type: ignore
        self.menus.edit = self.menu(tr("menu_edit"))  # type: ignore
        self.menus.view = self.menu(tr("menu_view"))  # type: ignore
        self.menus.help = self.menu(tr("menu_help"))  # type: ignore

        # Define common File menu actions, excluding 'Quit'
        common_file_actions = [
            self.actions.save,
            self.actions.hide_all,
            self.actions.show_all,
            self.actions.open_annotation,
            self.actions.copy_prev_bounding,
            self.actions.save_format,
            # Add other common File menu actions here
        ]

        # Add common File menu actions
        add_actions(self.menus.file, common_file_actions)

        # Add a separator before 'Quit'
        self.menus.file.addSeparator()

        # Add 'Quit' action last
        self.menus.file.addAction(self.quit_action)

        # Populate the View menu with common actions
        add_actions(
            self.menus.view,
            (
                self.actions.zoom_in_action,
                self.actions.zoom_out_action,
                self.actions.fit_window_action,
                self.actions.fit_width_action,
                self.actions.light_brighten_action,
                self.actions.light_darken_action,
                self.actions.light_org_action,
                self.actions.display_label_option,
                self.actions.single_class_mode,
                self.actions.auto_saving,
                self.actions.draw_squares_option,
                self.actions.advancedMode,
            ),
        )

        self.menus.labelList = QMenu(self)  # type: ignore
        self.menus.labelList.addAction(self.actions.edit)
        self.menus.labelList.addAction(self.actions.delete)

        # Connect the context menu to the label_list widget
        self.label_list.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.label_list.customContextMenuRequested.connect(self.pop_label_list_menu)

    def setup_camera_mode_menus(self):

        camera_file_actions = [
            self.actions.start_camera,
            self.actions.stop_camera,
            self.actions.pause_camera,
            self.actions.resume_camera,
            self.actions.set_homography,
            self.actions.adjust_camera_settings,
        ]

        # Insert camera-specific actions into the File menu before 'Quit'
        for action in camera_file_actions:
            self.menus.file.insertAction(self.quit_action, action)  # type: ignore

    def setup_file_mode_menus(self):
        add_actions(self.menus.edit, (self.actions.edit, self.actions.copy, self.actions.delete, self.actions.shapeLineColor, self.actions.shapeFillColor))

        file_mode_file_actions = [
            self.actions.open,
            self.actions.open_dir,
            self.actions.save_as,
            self.actions.close,
            self.actions.reset_all,
            # Add other file-specific File menu actions here if any
        ]

        # Insert file mode specific actions into the File menu before 'Quit'
        for action in file_mode_file_actions:
            self.menus.file.insertAction(self.quit_action, action)  # type: ignore

    def setup_camera_ui(self):

        # Setup camera tree in dock manager
        self.dock_manager.setup_camera_tree()

    def setup_file_list(self):
        """Set up the file list widget through the dock manager"""
        # Setup file list in dock manager
        self.dock_manager.setup_file_list()

        # Get the file list widget from dock manager for reference
        self.list_widget = self.dock_manager.get_file_list()

    def _setup_label_list(self):
        """Set up the label list widget through the dock manager"""

        # Setup label list in dock manager
        self.dock_manager.setup_label_list()

        # Get references to dock manager widgets
        self.label_list = self.dock_manager.label_list
        self.combo_box = self.dock_manager.combo_box
        self.edit_button = self.dock_manager.edit_button
        self.diffc_button = self.dock_manager.diffc_button
        self.use_default_label_checkbox = self.dock_manager.use_default_label_checkbox
        self.default_label_combo_box = self.dock_manager.default_label_combo_box

    def setup_light_widget(self):
        self.light_widget.valueChanged.connect(self.paint_canvas)

    def _setup_label_dialog(self):
        self.label_dialog = LabelDialog(parent=self, list_item=self.label_hist)

    def _setup_shortcuts(self):
        # Add shortcuts for labels
        self.add_label_shortcuts()

        # Add shortcuts for zooming
        self.zoom_in_shortcut = QShortcut(QKeySequence("Ctrl++"), self)
        self.zoom_in_shortcut.activated.connect(lambda: self.add_zoom(10))

        self.zoom_out_shortcut = QShortcut(QKeySequence("Ctrl+-"), self)
        self.zoom_out_shortcut.activated.connect(lambda: self.add_zoom(-10))

        self.zoom_org_shortcut = QShortcut(QKeySequence("Ctrl+="), self)
        self.zoom_org_shortcut.activated.connect(lambda: self.set_zoom(100))

    def _initialize_flags(self):
        self._no_selection_slot = False
        self._beginner = True
        # Start in EDIT mode (view/select) instead of CREATE mode initially
        self.canvas.mode = self.canvas.EDIT
        self.dirty = False

        self.label_file = None

    def initialize_settings(self):
        settings = self.settings
        self.restoreGeometry(settings.get(SETTING_WIN_GEOMETRY, QByteArray()))
        if xbool(settings.get("advanced_mode", False)):
            self.actions.advancedMode.setChecked(True)
            self.toggle_advanced_mode()

    def setup_context_menu(self):
        # Custom context menu for the canvas widget:
        add_actions(self.canvas.menus[0], self.actions.beginnerContext)
        add_actions(self.canvas.menus[1], (self.action("&Copy here", self.copy_shape), self.action("&Move here", self.move_shape)))

    def setup_camera_mode_toolbar(self):
        # Add camera mode specific tools
        self.tools = self.toolbar("Tools")
        add_actions(
            self.tools,
            (
                self.actions.start_camera,
                self.actions.stop_camera,
                self.actions.pause_camera,
                self.actions.resume_camera,
                self.actions.set_homography,
                self.actions.run_model,
                self.actions.adjust_camera_settings,
                self.actions.save,
                self.actions.create,
                self.actions.create_bounding_box_for_presence_check,
            ),
        )  # Removed duplicate self.actions.set_homography

    def setup_file_mode_toolbar(self):
        # Add file mode specific tools
        add_actions(self.tools, (self.actions.open, self.actions.open_dir, self.actions.save, self.actions.create, self.actions.run_model, self.actions.edit))

    def setup_status_bar(self, msg):
        self.statusBar().showMessage(msg)
        self.statusBar().show()
        # Add a permanent widget to show current mode
        self.mode_label = QLabel("")
        self.statusBar().addPermanentWidget(self.mode_label)

    def add_label_shortcuts(self):
        """Add keyboard shortcuts for label selection (1-9)."""
        for i in range(10):
            shortcut = QShortcut(QKeySequence(str(i)), self)
            shortcut.activated.connect(partial(self.quick_label, i))

    def quick_label(self, index):
        """Quickly select a label based on the shortcut pressed."""
        if index < len(self.label_hist):
            self.current_label = self.label_hist[index]
            print("Set label to:", self.current_label)

    def load_settings(self):

        # Load window size and position
        size, position, state = self.settings.get_window_settings()

        # Fix the multiple monitors issue
        for i in range(QApplication.desktop().screenCount()):
            if QApplication.desktop().availableGeometry(i).contains(position):
                break  # Found a suitable screen, use the stored position
        else:
            # If the position is not on any screen, reset it
            position = QPoint(0, 0)

        self.resize(size)
        self.move(position)
        self.restoreState(state)

        line_color, fill_color, difficult = self.settings.get_labeling_settings()

        self.line_color = QColor(line_color)
        self.fill_color = QColor(fill_color)
        self.difficult = difficult
        self.canvas.set_drawing_color(self.line_color)

    def setup_application_state(self):

        save_dir = self.settings.get(SETTING_SAVE_DIR, None)
        self.last_open_dir = self.settings.get(SETTING_LAST_OPEN_DIR, None)

        if self.default_save_dir is None and save_dir and os.path.exists(save_dir):
            self.default_save_dir = save_dir
            self.setup_status_bar(f"{self.__appname__} started. Annotation will be saved to {self.default_save_dir}")

        self.color_dialog = ColorDialog(parent=self)

        if self.annotation_mode == "file" and self.file_path:
            if os.path.isdir(self.file_path):
                self.import_dir_images(self.file_path)
            else:
                self.load_file(self.file_path)

    def initialize_camera_mode(self, default_save_dir):
        """Initialize settings and UI for camera mode."""
        self.camera_save_dir = self.settings.get(SETTING_CAMERA_SAVE_DIR, None)

        if self.camera_save_dir is None:
            if default_save_dir:
                self.camera_save_dir = os.path.join(default_save_dir, "camera_annotations")

        if self.camera_save_dir is None:
            options = QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
            self.camera_save_dir = str(QFileDialog.getExistingDirectory(self, "Select Camera Save Directory", "", options))

        # Ensure the directory exists
        if self.camera_save_dir:
            os.makedirs(self.camera_save_dir, exist_ok=True)
        self.shelve_dimensions = {}
        self.current_shelve_index = None
        self.homography_point_labels = ["Sol Üst", "Sol Alt", "Sağ Üst", "Sağ Alt"]
        self.canvas.homography_completed.connect(self.on_homography_points_selected)

    def __init__(
        self,
        app_name,
        file_handler: FileHandlerInterface,
        setting_handler: SettingsInterface,
        default_filename=None,
        default_prefdef_class_file=None,
        default_prefdef_shelves_file=None,
        default_save_dir=None,
    ):
        super(MainWindow, self).__init__()
        # defaults for warnings.
        self.camera_save_dir = None

        self.__appname__ = app_name
        self.file_handler = file_handler
        self.state = AppState()

        self.settings = setting_handler

        load_init_settings(self)

        ##init variables:
        self.os_name = platform.system()
        self.mode_label = QLabel()
        self.tools = self.toolbar("Tools")

        self.items_to_shapes = {}
        self.shapes_to_items = {}
        self.prev_label_text = ""
        self.camera_save_dir = ""

        # Make sure we're using the singleton instance
        self.detection_mode_manager = DetectionModeManager.get_instance(self.get_str)  # type: ignore

        self.default_save_dir = default_save_dir
        self.label_file_format = self.settings.get(SETTING_LABEL_FILE_FORMAT, self.file_handler.label_file_format)
        self.m_img_list = []
        self.dir_name = None
        self.label_hist = []
        self.cur_img_idx = 0
        self.img_count = len(self.m_img_list)
        self.file_path = default_filename or ""
        self.recent_files = []
        self.max_recent = 7
        self.line_color = None
        self.fill_color = None
        self.zoom_level = 100
        self.fit_window = False
        self.difficult = False
        self.hide_all = False
        self.label_coordinates = QLabel("")
        self._beginner = True
        self.screencast = "https://youtu.be/p0nR2YsCY_U"
        self.image = QImage()
        self.dock_features = QDockWidget.DockWidgetMovable | QDockWidget.DockWidgetFloatable
        self.is_awaiting_first_frame = False  # Add flag for first frame detection

        self.actions = Struct()  # type: ignore
        self.menus = Struct()
        self.annotation_mode = None  # Will be set during mode selection
        self.setup_application_state()
        # Set up common UI components
        self._setup_ui()

        self.setup_canvas()

        # Initialize CameraManager - WITHOUT canvas reference
        self.camera_manager = CameraManager(main_window=self)

        # Initialize the dock manager
        self.dock_manager = DockManager(self)

        self._setup_label_list()
        self.setup_common_actions()
        self.initialize_settings()

        self.setup_common_menus()
        # TODO: dock manager is being used kaldir
        self.setup_light_widget()
        self._setup_label_dialog()
        self._setup_shortcuts()
        self._initialize_flags()
        self.setup_context_menu()
        self.setup_status_bar("%s started." % self.__appname__)

        # Setup startup dialog
        self.setup_startup_dialog(default_save_dir, default_prefdef_shelves_file)
        self.update_mode_indicator()

        # Load settings and predefined classes
        self.load_settings()
        load_predefined_classes(self, default_prefdef_class_file)

        # Since loading the file may take some time, make sure it runs in the background.
        if self.file_path and os.path.isdir(self.file_path):
            queue_event(partial(self.import_dir_images, self.file_path or ""))
        elif self.file_path:
            queue_event(partial(self.load_file, self.file_path or ""))

        self.populate_advance_or_beginer_mode_actions()

        # Display cursor coordinates at the right of status bar
        self.statusBar().addPermanentWidget(self.label_coordinates)

        # Open Dir if default file
        if self.file_path and os.path.isdir(self.file_path):
            self.open_dir_dialog(dir_path=self.file_path, silent=True)

        self.update_action_states()  # Initial state update

    def setup_startup_dialog(self, default_save_dir, default_prefdef_shelves_file):
        """Show startup dialog to select mode."""
        startup_dialog = StartupDialog(self)

        if startup_dialog.exec_():
            self.annotation_mode = "camera"
            self.initialize_camera_mode(default_save_dir)
            load_shelve_data(self, default_prefdef_shelves_file)
            self.setup_camera_mode_ui()
        else:
            self.annotation_mode = "file"
            self.setup_file_mode_ui()

    def keyReleaseEvent(self, event: QKeyEvent):
        if event.key() == Qt.Key.Key_Control:
            self.canvas.set_drawing_shape_to_square(False)

    def keyPressEvent(self, event: QKeyEvent):
        if event.key() == Qt.Key.Key_Control:
            # Draw rectangle if Ctrl is pressed
            self.canvas.set_drawing_shape_to_square(True)

    # Support Functions #
    def set_format(self, save_format):
        result = self.file_handler.set_format(save_format)
        if result:
            self.actions.save_format.setText(result["format"])
            self.actions.save_format.setIcon(new_icon(result["icon"]))
            self.label_file_format = self.file_handler.label_file_format

    def change_format(self):
        result = self.file_handler.change_format()
        if result:
            self.set_dirty()  # Mark as dirty since format changed

    def no_shapes(self):
        return not self.items_to_shapes

    def toggle_advanced_mode(self, value=True):
        self._beginner = not value
        self.canvas.set_editing(True)

        self.populate_advance_or_beginer_mode_actions()
        self.edit_button.setVisible(not value)  # Keep this specific visibility toggle

        # Use dock manager to set dock features (if needed, separate from action states)
        if self.dock_manager:
            if value:
                self.dock_manager.set_dock_features()
        else:
            # Use dock manager to set dock features
            if self.dock_manager:
                self.dock_manager.set_dock_features()

        self.update_action_states()  # Update states after mode toggle

    def populate_advance_or_beginer_mode_actions(self):
        """Populate toolbar based on the current mode and user settings."""

        if not self.beginner():  # Advanced mode
            add_actions(self.tools, (self.actions.createMode, self.actions.editMode))

        # Add common toolbar actions
        add_actions(
            self.tools,
            (
                self.actions.zoom_in_action,
                self.actions.zoom_out_action,
                self.actions.fit_window_action,
                self.actions.fit_width_action,
                self.actions.light_brighten_action,
                self.actions.light_darken_action,
            ),
        )

    def set_dirty(self):
        """Mark the state as dirty (unsaved changes present)."""
        self.dirty = True
        self.update_action_states()

    def set_clean(self):
        """Mark the state as clean (no unsaved changes)."""
        self.dirty = False
        self.update_action_states()  # Update states when clean

    def status(self, message, delay=5000):
        self.statusBar().showMessage(message, delay)

    def setup_camera_mode_ui(self):
        # Setup actions specific to camera mode
        self.setup_camera_mode_actions()
        # Setup menus specific to camera mode
        self.setup_camera_mode_menus()
        # Setup toolbar specific to camera mode
        self.setup_camera_mode_toolbar()

        # Initialize dock manager with camera mode
        if self.dock_manager:
            self.dock_manager.setup_dock_areas(mode="camera")
        self.setup_camera_ui()

    def setup_file_mode_ui(self):
        # Setup actions specific to file mode
        self.setup_file_mode_actions()
        # Setup menus specific to file mode
        self.setup_file_mode_menus()
        # Setup toolbar specific to file mode
        self.setup_file_mode_toolbar()

        # Initialize dock manager with file mode
        if self.dock_manager:
            self.dock_manager.setup_dock_areas(mode="file")
        self.setup_file_list()
        self.update_action_states()  # Update states after setting mode

    def start_camera(self):
        # Start the camera and begin capturing frames
        camera_tree = self.dock_manager.get_camera_tree()
        selected_items = camera_tree.selectedItems()
        if not selected_items:
            # No camera selected, select the first one
            if camera_tree.topLevelItemCount() > 0:
                first_item = camera_tree.topLevelItem(0)
                if first_item.childCount() > 0:
                    first_camera = first_item.child(0)
                    camera_tree.setCurrentItem(first_camera)
                    self.on_camera_selected(first_camera)  # type: ignore
                else:
                    self.error_message("Camera Selection", "No cameras available.")
            else:
                self.error_message("Camera Selection", "No cameras available.")
        else:
            # Camera selected, call on_camera_selected
            self.on_camera_selected(selected_items[0])

    def stop_camera(self):
        """Stop the camera and release resources."""
        if self.camera_manager.current_camera_thread:
            # Disconnect signals to prevent any further updates
            try:
                self.camera_manager.current_camera_thread.frame_updated.disconnect(self.update_canvas_frame)
                self.camera_manager.current_camera_thread.camera_error.disconnect(self.handle_camera_error_signal)
            except TypeError:
                # Signal was not connected
                pass
            self.camera_manager.stop_current_camera()
            self.status("Camera stopped.")
        else:
            self.status("No camera is running.")

    def pause_camera(self):
        if self.camera_manager.is_running():
            self.camera_manager.pause()
            self.status("Camera feed paused.")
            self.update_action_states()  # Update states after pausing

    def resume_camera(self):
        if self.camera_manager.is_paused():
            self.camera_manager.resume()
            self.status("Camera feed resumed.")
            self.update_action_states()  # Update states after resuming

    def adjust_camera_settings(self):

        dialog = CameraSettingsDialog(self, current_settings=self.camera_manager.get_settings())
        if dialog.exec_():
            new_settings = dialog.get_settings()
            self.camera_manager.apply_settings(new_settings)
            self.status("Camera settings updated.")

    def run_model(self):
        """Run model processing on the current camera feed."""
        if not self.camera_manager.current_camera_thread:
            self.status(self.get_str("noCameraActive"))
            return

        # If already processing, stop
        if self.camera_manager.is_model_processing():
            self.camera_manager.stop_model_processing()
            self.status(self.get_str("modelProcessingStopped"))
            return

        relevant_shapes = [s for s in self.canvas.shapes if hasattr(s, "roi_type") and s.roi_type]

        if not relevant_shapes:
            self.status(self.get_str("noROIsDefined"))
            return

        camera_name = self.camera_manager.current_camera_thread.camera_name

        self.detection_mode_manager.reset_tracker_registry()
        # Register shapes with the detection mode manager
        for shape in relevant_shapes:
            # This will create and register appropriate trackers based on shape properties
            tracker = self.detection_mode_manager.get_tracker_for_shape(camera_name, shape)
            if tracker:
                # The shape is now registered with a tracker in the central registry
                logger.debug(f"Registered shape with tracker: {tracker.__class__.__name__}")

        # Start model processing using the registered trackers
        self.camera_manager.start_model_processing()
        self.status(self.get_str("modelProcessingStarted"))
        self.update_action_states()

    def reset_state(self):
        self.items_to_shapes.clear()
        self.shapes_to_items.clear()
        self.label_list.clear()
        self.file_path = None
        self.image = None
        self.label_file = None
        self.is_awaiting_first_frame = False  # Reset flag
        self.canvas.reset_state()
        self.label_coordinates.clear()
        # self.combo_box.cb.clear() # Clearing combobox might not be desired on reset
        self.set_clean()  # This calls update_action_states

    def current_item(self):
        items = self.label_list.selectedItems()
        if items:
            return items[0]
        return None

    def add_recent_file(self, file_path):
        if file_path in self.recent_files:
            self.recent_files.remove(file_path)
        elif len(self.recent_files) >= self.max_recent:
            self.recent_files.pop()
        self.recent_files.insert(0, file_path)
        self.update_file_menu()

    def beginner(self):
        return self._beginner

    def advanced(self):
        return not self.beginner()

    def show_info_dialog(self):
        from libs.__init__ import __version__

        msg = "Name:{0} \nApp Version:{1} \n{2} ".format(self.__appname__, __version__, sys.version_info)
        QMessageBox.information(self, "Information", msg)

    def create_shape(self):
        assert self.beginner()
        self.canvas.set_editing(False)
        self.canvas.set_shape_type("rectangle")
        self.canvas.set_next_shape_roi_type("rectangle")  # Ensure generic shapes have no predefined ROI type
        self.update_action_states()  # Reflect immediate state change

    def create_bounding_box_for_presence_check(self):
        """Prepare the canvas to draw a bounding box for people presence detection."""
        assert self.beginner()
        self.canvas.set_editing(False)
        self.canvas.set_shape_type("rectangle")
        self.canvas.set_next_shape_roi_type("presence_check")  # Set ROI type for the next shape
        self.update_action_states()

        self.status("Drawing presence check rectangle. Press ESC to cancel.")

    def set_homography_mode(self):
        """Enable homography setup mode."""
        if not self.may_continue():
            return

        # Ensure a camera or image is loaded
        if not self.canvas.pixmap or self.canvas.pixmap.isNull():
            self.error_message("No Image", "Please load an image before setting homography.")
            return

        # Check if we have Shelves data available
        if not self.shelve_dimensions:
            self.error_message(self.get_str("shelfError"), self.get_str("invalidShelfSelection"))
            return

        self.current_shelve_index = ShelveSelectorDialog.get_selected_shelve(self, self.shelve_dimensions)

        # If user canceled selection, return
        if self.current_shelve_index is None:
            self.status("Homography setup canceled.")
            return

        self.canvas.set_mode(self.canvas.SET_HOMOGRAPHY)
        self.status(f"{self.get_str('homographyModeTitle')} {self.current_shelve_index})")
        self.status(self.get_str("selectCornersMessage"))
        self.update_action_states()  # Update states for homography mode

    def toggle_drawing_sensitive(self, drawing=True):
        """Update action states when drawing starts or stops."""
        if not drawing and self.beginner():
            print("Cancel creation.")
            self.canvas.set_editing(True)
            self.canvas.restore_cursor()
        self.update_action_states()

    def set_create_mode(self):
        """Switch to create mode (Advanced)."""
        assert self.advanced()
        self.canvas.set_editing(False)
        # Ensure the action reflects the state if triggered programmatically
        self.actions.createMode.setChecked(True)
        self.update_action_states()

    def set_edit_mode(self):
        """Switch to edit mode (Advanced)."""
        assert self.advanced()
        self.canvas.set_editing(True)
        # Ensure the action reflects the state if triggered programmatically
        self.actions.editMode.setChecked(True)
        self.label_selection_changed()  # This calls update_action_states

    def update_file_menu(self):
        """Update the recent files submenu."""
        menu = self.menus.file
        recent_files_menu = self.menu(self.get_str("menu_openRecent"))
        recent_files_menu.clear()

        files = [f for f in self.recent_files if os.path.exists(f)]

        for i, f in enumerate(files[: self.max_recent]):
            icon = QIcon.fromTheme(self, name="document-open")  # type: ignore
            action = QAction(icon, f"&{i + 1} {os.path.basename(f)}", self)
            action.triggered.connect(partial(self.load_recent, f))
            recent_files_menu.addAction(action)

        if not recent_files_menu.actions():
            recent_files_menu.addAction(self.get_str("noRecentFiles")).setEnabled(False)

        # Add the recent files menu to the main file menu
        if not any(action.text() == self.get_str("menu_openRecent") for action in menu.actions()):  # type: ignore
            menu.addMenu(recent_files_menu)  # type: ignore

    def pop_label_list_menu(self, point):
        item = self.label_list.itemAt(point)
        if item:
            self.menus.labelList.exec_(self.label_list.mapToGlobal(point))  # type: ignore

    def edit_label(self):
        if not self.canvas.editing():
            return
        item = self.current_item()
        if not item:
            return
        text = self.label_dialog.pop_up(item.text())
        if text is not None:
            item.setText(text)
            item.setBackground(generate_color_by_text(text))
            self.set_dirty()
            self.update_combo_box()

    # Tzutalin 20160906 : Add file list and dock to move faster
    def file_item_double_clicked(self, item=None):
        # Handle double-click event on file list
        self.cur_img_idx = self.m_img_list.index(item.text())
        filename = self.m_img_list[self.cur_img_idx]
        if filename:
            self.load_file(filename)

    def button_state(self) -> None:
        """Function to handle difficult examples
        Update on each object"""
        if not self.canvas.editing():
            return

        selected_item = self.current_item()
        if not selected_item:  # If not selected Item, take the first one
            selected_item = self.label_list.item(self.label_list.count() - 1)

        difficult_button_checked = self.diffc_button.isChecked()

        try:
            shape = self.items_to_shapes[selected_item]
            shape_changed = False
            if difficult_button_checked != shape.difficult:
                shape.difficult = difficult_button_checked
                shape_changed = True

            # Also handle visibility change here
            is_visible = selected_item.checkState() == Qt.CheckState.Checked
            if self.canvas.set_shape_visible(shape, is_visible):  # Check if visibility actually changed
                shape_changed = True  # Consider visibility change as needing redraw, maybe not dirty

            if shape_changed:
                self.set_dirty()  # Mark as dirty if difficult changed
                self.canvas.update()  # Redraw canvas if visibility changed

        except KeyError:
            logger.error(f"button_state: Item {selected_item} not found in items_to_shapes.")
        except Exception as e:
            logger.error(f"button_state: Unexpected error getting shape for item {selected_item}: {e}")

    # React to canvas signals.
    def shape_selection_changed(self, selected=False):
        if self._no_selection_slot:
            self._no_selection_slot = False
        else:
            shape = self.canvas.selected_shape
            if shape:
                self.shapes_to_items[shape].setSelected(True)
            else:
                self.label_list.clearSelection()
        self.update_action_states()  # Update states based on selection

    def add_label(self, shape):
        """Add a label to the label list and associate it with a shape."""
        shape.paint_label = self.actions.display_label_option.isChecked()
        item = HashableQListWidgetItem(shape.label)
        item.setFlags(item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
        item.setCheckState(Qt.CheckState.Checked)
        item.setBackground(generate_color_by_text(shape.label))
        self.items_to_shapes[item] = shape
        self.shapes_to_items[shape] = item
        self.label_list.addItem(item)

        # Mark as dirty when a new label is added
        self.dirty = True

        self.update_combo_box()
        self.update_action_states()  # Update states after adding label

    def remove_label(self, shape):
        """Remove a label and its associated shape."""
        if shape is None:
            return
        item = self.shapes_to_items.get(shape)
        if item:
            self.label_list.takeItem(self.label_list.row(item))
            del self.shapes_to_items[shape]
            del self.items_to_shapes[item]
            self.update_combo_box()
            self.update_action_states()  # Update states after removing label

    def get_shelve_display_name(self, shelve_id):
        """Convert a shelve_id to a human-readable display name.
        Displays magaza_no reyon_no and reyon_adi for clarity."""
        if not shelve_id:
            return shelve_id

        # First, try directly with the provided shelve_id
        if shelve_id in self.shelve_dimensions:
            shelve_data = self.shelve_dimensions[shelve_id]
            return f"{shelve_data.get('magaza_no', '')}_{shelve_data.get('id', '')}_{shelve_data.get('name', '')}"
        # If not found, just return the ID as is
        return shelve_id

    def load_labels(self, shapes_data):
        """Load labels from shapes data."""
        shapes = []
        for shape_data in shapes_data:
            shape = Shape.from_dict(shape_data)
            # Ensure shape is properly closed
            shape.close()
            shapes.append(shape)
            self.add_label(shape)

        # Finally load shapes into the canvas
        self.canvas.load_shapes(shapes)
        self.update_combo_box()
        self.set_clean()

    def update_combo_box(self):
        """Update the combo box with unique labels."""
        if self.dock_manager:
            self.dock_manager.update_combo_box()
        else:
            items_text_list = [str(self.label_list.item(i).text()) for i in range(self.label_list.count())]
            unique_text_list = list(set(items_text_list))
            unique_text_list.append("")
            unique_text_list.sort()
            self.combo_box.update_items(unique_text_list)

    def save_labels(self, annotation_file_path):
        """Save labels to the specified annotation file."""
        shapes = [shape.to_dict() for shape in self.canvas.shapes]

        success = self.file_handler.save_annotations(
            annotation_file_path,
            shapes,
            self.file_path,
            self.image,
            self.line_color.getRgb(),
            self.fill_color.getRgb(),
            self.label_hist,
            verified=self.canvas.verified,
        )
        if success:
            print(f"Image: {self.file_path} -> Annotation: {annotation_file_path}")
            return True
        else:
            self.error_message("Error saving label data", "See console for details.")
            return False

    def copy_selected_shape(self):
        self.add_label(self.canvas.copy_selected_shape())
        # fix copy and delete
        self.shape_selection_changed(True)

    def combo_selection_changed(self, index):
        text = self.combo_box.cb.itemText(index)
        for i in range(self.label_list.count()):
            if text == "":
                self.label_list.item(i).setCheckState(2)
            elif text != self.label_list.item(i).text():
                self.label_list.item(i).setCheckState(0)
            else:
                self.label_list.item(i).setCheckState(2)

    def default_label_combo_selection_changed(self, index):
        self.default_label = self.label_hist[index]

    def label_selection_changed(self):
        item = self.current_item()
        if item and self.canvas.editing():
            self._no_selection_slot = True
            self.canvas.select_shape(self.items_to_shapes[item])
            shape = self.items_to_shapes[item]
            # Add Chris
            self.diffc_button.setChecked(shape.difficult)

    def label_item_changed(self, item):
        shape = self.items_to_shapes[item]
        if label != shape.label:
            shape.label = item.text()
            shape.line_color = generate_color_by_text(shape.label)
            self.set_dirty()
        else:  # User probably changed item visibility
            if self.canvas.set_shape_visible(shape, item.checkState() == Qt.CheckState.Checked):
                self.canvas.update()  # Redraw if visibility changed
                self.update_action_states()  # Update states if visibility changed

    # Callback functions:
    def new_shape(self):
        """
        Slot for new shape signal.
        Handles shape labeling based on shape.roi_type.
        """
        if not self.canvas.shapes:
            logger.warning("new_shape called but no shapes exist on canvas.")
            return

        shape = self.canvas.shapes[-1]  # Get the newest shape

        # Set magaza_no for the new shape
        if self.camera_manager and self.camera_manager.current_camera_thread.camera_name:
            current_camera_name = self.camera_manager.current_camera_thread.camera_name
            config = get_config()
            magaza_no = config.get_camera_magaza_no(current_camera_name)
            if magaza_no:
                shape.magaza_no = magaza_no
                logger.info(f"Set magaza_no '{magaza_no}' for new shape from camera '{current_camera_name}'.")
            else:
                logger.warning(f"Could not retrieve magaza_no for camera '{current_camera_name}'.")
        else:
            logger.warning("Cannot set magaza_no: No current camera selected or camera_manager not available.")

        # Logic based on shape.roi_type (which should now be set by Canvas if applicable)
        if shape.roi_type == "presence_check":
            # This ROI type has predefined behavior: auto-label, no dialog
            shape.label = self.camera_manager.current_camera_thread.camera_name if self.camera_manager.current_camera_thread else "presence_check_roi"
            shape.paint_label = True
            self.add_label(shape)
            self.set_dirty()
            if self.beginner():
                self.canvas.set_editing(True)
        elif shape.roi_type is None:
            # No predefined ROI type (generic shape, or initial rect for homography): Show the label dialog.
            text = self.label_dialog.pop_up(text=shape.label if shape.label else "") if self.label_dialog else None
            if text:
                shape.label = text
                shape.line_color = generate_color_by_text(text)
                self.add_label(shape)
                self.set_dirty()
                if self.beginner():
                    self.canvas.set_editing(True)
                if text not in self.label_hist:
                    self.label_hist.append(text)
            else:  # Dialog cancelled
                if self.canvas.shapes and shape == self.canvas.shapes[-1]:  # Ensure it's the one we are processing
                    self.canvas.shapes.pop()
                self.canvas.update()
        else:
            if shape.roi_type == "emptyshelve":
                if not shape.label and hasattr(shape, "shelve_id") and shape.shelve_id:
                    shape.label = f"Shelf {shape.shelve_id}"  # Default label for emptyshelve if not set
                elif not shape.label:
                    shape.label = "emptyshelve_roi"
            elif not shape.label:  # Default label for any other unknown pre-set ROI type
                shape.label = f"{shape.roi_type}_roi"

            shape.paint_label = True  # Default to painting label for these ROIs
            if shape not in self.shapes_to_items:
                self.add_label(shape)
            self.set_dirty()
            if self.beginner():
                self.canvas.set_editing(True)

        self.update_action_states()

    def scroll_request(self, delta, orientation):
        units = -delta / (8 * 15)
        bar = self.scroll_bars[orientation]
        bar.setValue(int(bar.value() + bar.singleStep() * units))

    def set_zoom(self, value):
        self.actions.fit_width_action.setChecked(False)
        self.actions.fit_window_action.setChecked(False)
        self.zoom_mode = self.MANUAL_ZOOM
        # Convert to int to avoid type errors
        self.zoom_widget.setValue(int(value))

    def add_zoom(self, increment=10):
        self.set_zoom(self.zoom_widget.value() + increment)

    def zoom_request(self, delta):
        # get the current scrollbar positions
        # calculate the percentages ~ coordinates
        h_bar = self.scroll_bars[Qt.Orientation.Horizontal]
        v_bar = self.scroll_bars[Qt.Orientation.Vertical]

        # get the current maximum, to know the difference after zooming
        h_bar_max = h_bar.maximum()
        v_bar_max = v_bar.maximum()

        # get the cursor position and canvas size
        # calculate the desired movement from 0 to 1
        # where 0 = move left
        #       1 = move right
        # up and down analogous
        cursor = QCursor()
        pos = cursor.pos()
        relative_pos = QWidget.mapFromGlobal(self, pos)

        cursor_x = relative_pos.x()
        cursor_y = relative_pos.y()

        w = self.scroll_area.width()
        h = self.scroll_area.height()

        # the scaling from 0 to 1 has some padding
        # you don't have to hit the very leftmost pixel for a maximum-left movement
        margin = 0.1
        move_x = (cursor_x - margin * w) / (w - 2 * margin * w)
        move_y = (cursor_y - margin * h) / (h - 2 * margin * h)

        # clamp the values from 0 to 1
        move_x = min(max(move_x, 0), 1)
        move_y = min(max(move_y, 0), 1)

        # zoom in
        units = delta // (8 * 15)
        scale = 10
        self.add_zoom(scale * units)

        # get the difference in scrollbar values
        # this is how far we can move
        d_h_bar_max = h_bar.maximum() - h_bar_max
        d_v_bar_max = v_bar.maximum() - v_bar_max

        # get the new scrollbar values
        new_h_bar_value = int(h_bar.value() + move_x * d_h_bar_max)
        new_v_bar_value = int(v_bar.value() + move_y * d_v_bar_max)

        h_bar.setValue(new_h_bar_value)
        v_bar.setValue(new_v_bar_value)

    def light_request(self, delta):
        self.add_light(5 * delta // (8 * 15))

    def set_fit_window(self, value=True):
        if value:
            self.actions.fit_width_action.setChecked(False)
        self.zoom_mode = self.FIT_WINDOW if value else self.MANUAL_ZOOM
        self.adjust_scale()

    def set_fit_width(self, value=True):
        if value:
            self.actions.fit_window_action.setChecked(False)
        self.zoom_mode = self.FIT_WIDTH if value else self.MANUAL_ZOOM
        self.adjust_scale()

    def set_light(self, value):
        self.actions.light_org_action.setChecked(int(value) == 50)
        # Arithmetic on scaling factor often results in float
        # Convert to int to avoid type errors
        self.light_widget.setValue(int(value))

    def add_light(self, increment=10):
        self.set_light(self.light_widget.value() + increment)

    def toggle_polygons(self, value):
        for item, shape in self.items_to_shapes.items():
            item.setCheckState(Qt.CheckState.Checked if value else Qt.CheckState.Unchecked)

    def load_file(self, file_path=None):
        """Load the specified file, or the last opened file if None."""
        self.stop_camera()
        self.reset_state()

        if file_path is None:
            file_path = self.settings.get(SETTING_FILENAME)

        unicode_file_path = os.path.abspath(file_path) if file_path else None

        # Highlight the file item
        if unicode_file_path and self.list_widget.count() > 0:
            if unicode_file_path in self.m_img_list:
                index = self.m_img_list.index(unicode_file_path)
                file_widget_item = self.list_widget.item(index)
                file_widget_item.setSelected(True)
            else:
                self.list_widget.clear()
                self.m_img_list.clear()

        if unicode_file_path and os.path.exists(unicode_file_path):
            # Use file_handler to load file data
            result = self.file_handler.load_file_data(unicode_file_path)

            if not result["success"]:
                self.error_message("Error opening file", result["error_msg"])
                self.status(f"Error reading {unicode_file_path}")
                self.update_action_states()
                return False

            # Extract data from result
            self.image = result["image"]
            self.label_file = result["label_file"]
            self.canvas.verified = result["is_verified"]

            if self.label_file:
                self.line_color = QColor(*self.label_file.lineColor)
                self.fill_color = QColor(*self.label_file.fillColor)

            self.status(f"Loaded {os.path.basename(unicode_file_path)}")
            self.file_path = unicode_file_path
            self.canvas.load_pixmap(QPixmap.fromImage(self.image))

            if self.label_file:
                self.load_labels(self.label_file.shapes)
            self.set_clean()
            self.canvas.setEnabled(True)
            self.adjust_scale(initial=True)
            self.paint_canvas()
            self.add_recent_file(self.file_path)
            self.show_bounding_box_from_annotation_file(self.file_path)

            counter = counter_str(self.cur_img_idx, self.img_count)
            self.setWindowTitle(f"{self.__appname__} {file_path} {counter}")

            if self.label_list.count():
                self.label_list.setCurrentItem(self.label_list.item(self.label_list.count() - 1))
                self.label_list.item(self.label_list.count() - 1).setSelected(True)

            self.canvas.setFocus()
            self.update_action_states()
            return True
        self.update_action_states()
        return False

    def update_action_states(self):
        """Central function to update the enabled state of all actions based on current context."""
        logger.debug("Updating action states...")

        # --- Determine Current State ---
        # Application Mode
        is_file_mode = self.annotation_mode == "file"
        is_camera_mode = self.annotation_mode == "camera"

        # Resource State
        is_camera_running = is_camera_mode and self.camera_manager.is_running()
        is_camera_paused = is_camera_mode and self.camera_manager.is_paused()
        is_image_loaded = self.image is not None and not self.image.isNull()
        # Resource active means image loaded (in file mode) OR camera running/fallback image (in camera mode)
        is_resource_active = (is_file_mode and is_image_loaded) or (is_camera_mode and (is_camera_running or is_image_loaded))
        is_model_processing = self.camera_manager.is_model_processing()

        # Shape State
        shapes_exist = bool(self.canvas.shapes)
        selected_shape = self.canvas.selected_shape
        is_shape_selected = selected_shape is not None

        # UI State / User Interaction
        is_drawing = self.canvas.drawing()
        is_homography_mode = self.canvas.mode == self.canvas.SET_HOMOGRAPHY
        is_busy_drawing = is_drawing or is_homography_mode  # General flag for drawing/homography

        # Other States
        is_dirty = self.dirty
        is_advanced = self.advanced()
        is_beginner = self.beginner()

        # ROI State
        has_presence_roi = any(s.roi_type == "presence_check" for s in self.canvas.shapes)
        has_emptyshelve_roi = any(
            s.roi_type == "emptyshelve" or (hasattr(s, "homography") and s.homography is not None) for s in self.canvas.shapes
        )  # Include homography shapes
        has_valid_roi_for_model = has_presence_roi or has_emptyshelve_roi

        # Directory Navigation State
        is_in_dir_list = is_file_mode and self.dir_name is not None and self.file_path in self.m_img_list
        current_img_index = -1
        if is_in_dir_list:
            try:
                current_img_index = self.m_img_list.index(self.file_path)
            except ValueError:
                logger.warning(f"File path {self.file_path} not found in m_img_list during state update.")

        # --- Update Action States ---

        # Helper to safely enable/disable actions
        def set_enabled(action_name, enabled):
            action = getattr(self.actions, action_name, None)
            if action:
                action.setEnabled(enabled)
            else:
                # Only log warning if we're in the right mode for this action
                if (
                    (is_file_mode and action_name in ["save_as", "close", "delete_image", "copy_prev_bounding"])
                    or (
                        is_camera_mode
                        and action_name in ["start_camera", "stop_camera", "pause_camera", "resume_camera", "set_homography", "adjust_camera_settings"]
                    )
                    or (action_name in ["save", "create", "createMode", "editMode", "edit", "copy", "delete", "shapeLineColor", "shapeFillColor", "run_model"])
                ):
                    logger.warning(f"Action '{action_name}' not found during state update.")

        # A. Based on Application Mode (File vs. Camera)
        # File Mode Only Actions
        if is_file_mode:
            set_enabled("open", True)  # Always enabled in File Mode
            set_enabled("open_dir", True)  # Always enabled in File Mode
            set_enabled("save_as", is_image_loaded)  # File loaded/present
            set_enabled("close", is_image_loaded)  # File loaded/present
            set_enabled("delete_image", is_image_loaded and is_in_dir_list)  # File loaded/present AND part of directory list
            set_enabled("reset_all", True)  # Always enabled in File Mode
            set_enabled("copy_prev_bounding", is_image_loaded and is_in_dir_list and current_img_index > 0)  # Not first image

        # Camera Mode Only Actions
        if is_camera_mode:
            set_enabled("start_camera", not is_camera_running)  # No camera currently running
            set_enabled("stop_camera", is_camera_running)  # Camera is currently running
            set_enabled("pause_camera", is_camera_running and not is_camera_paused)  # Camera running AND not paused
            set_enabled("resume_camera", is_camera_running and is_camera_paused)  # Camera running AND paused
            set_enabled("set_homography", (is_camera_running or is_image_loaded) and not is_busy_drawing)  # Camera running OR fallback image loaded
            set_enabled("adjust_camera_settings", True)  # Always enabled in Camera Mode
            set_enabled(
                "create_bounding_box_for_presence_check", (is_camera_running or is_image_loaded) and not is_busy_drawing and is_beginner
            )  # Camera running OR fallback image loaded

        # B. Based on Resource State (Image Loaded or Camera Running)
        if is_resource_active:
            # Save action depends on dirty state
            set_enabled("save", is_dirty and not is_busy_drawing)

            # Create/Edit actions depend on drawing state and mode
            if not is_busy_drawing:
                if is_beginner:
                    set_enabled("create", True)
                else:  # Advanced mode
                    set_enabled("createMode", True)
                    set_enabled("editMode", True)
            else:  # Disable when drawing/setting homography
                set_enabled("create", False)
                set_enabled("createMode", False)
                set_enabled("editMode", False)

            # Enable zoom and light widgets/actions
            for widget_or_action in self.zoom_actions:
                widget_or_action.setEnabled(True)
            for widget_or_action in self.light_actions:
                widget_or_action.setEnabled(True)

            # Show/Hide actions depend on shapes existing
            set_enabled("hide_all", shapes_exist)
            set_enabled("show_all", shapes_exist)

            # Open annotation always enabled when resource active
            set_enabled("open_annotation", True)

            # Run model depends on ROIs and drawing state
            set_enabled("run_model", has_valid_roi_for_model and not is_busy_drawing)
        else:  # No resource active
            # Disable resource-dependent actions
            set_enabled("save", False)
            set_enabled("create", False)
            set_enabled("createMode", False)
            set_enabled("editMode", False)
            set_enabled("hide_all", False)
            set_enabled("show_all", False)
            set_enabled("open_annotation", False)
            set_enabled("run_model", False)

            # Disable zoom and light widgets/actions
            for widget_or_action in self.zoom_actions:
                widget_or_action.setEnabled(False)
            for widget_or_action in self.light_actions:
                widget_or_action.setEnabled(False)

        # C. Based on Shape State
        if is_shape_selected and not is_busy_drawing:
            set_enabled("delete", True)
            set_enabled("copy", True)
            set_enabled("edit", True)

            # Shape color actions only in advanced mode
            if is_advanced:
                set_enabled("shapeLineColor", True)
                set_enabled("shapeFillColor", True)
            else:
                set_enabled("shapeLineColor", False)
                set_enabled("shapeFillColor", False)
        else:  # No shape selected or busy drawing
            set_enabled("delete", False)
            set_enabled("copy", False)
            set_enabled("edit", False)
            set_enabled("shapeLineColor", False)
            set_enabled("shapeFillColor", False)

        # D. Based on UI State / User Interaction
        # Drawing Mode Active - most actions already handled above

        # Beginner vs Advanced Mode
        if is_beginner:
            # These are already handled above, but explicitly set here for clarity
            set_enabled("createMode", False)
            set_enabled("editMode", False)
            set_enabled("shapeLineColor", False)
            set_enabled("shapeFillColor", False)

        # Update run_model text based on processing state
        run_model_action = getattr(self.actions, "run_model", None)
        if run_model_action:
            if is_model_processing:
                run_model_action.setText(self.get_str("stopDetection"))
            else:
                run_model_action.setText(self.get_str("runModel"))

        # Ensure canvas enabled state matches resource activity
        self.canvas.setEnabled(is_resource_active)

        # Stop model processing if conditions are not met
        if not (has_valid_roi_for_model and is_resource_active):
            if is_model_processing:
                self.camera_manager.stop_model_processing()

        logger.debug("Action states updated.")

    def show_bounding_box_from_annotation_file(self, file_path):
        """Show bounding boxes by loading annotations from a file."""
        if self.default_save_dir:
            basename = os.path.basename(os.path.splitext(file_path)[0])
            for ext in [XML_EXT, TXT_EXT, JSON_EXT]:
                annotation_path = os.path.join(self.default_save_dir, basename + ext)
                if os.path.isfile(annotation_path):
                    self.load_annotations(annotation_path, file_path)
                    return
        else:
            for ext in [XML_EXT, TXT_EXT, JSON_EXT]:
                annotation_path = os.path.splitext(file_path)[0] + ext
                if os.path.isfile(annotation_path):
                    self.load_annotations(annotation_path, file_path)
                    return

        # If we get here, no annotation file was found
        self.status("No annotation file found for this image.")

    def paint_canvas(self):
        if self.image is None or self.image.isNull():
            return
        self.canvas.scale = 0.01 * self.zoom_widget.value()
        self.canvas.overlay_color = self.light_widget.color()
        self.canvas.label_font_size = int(0.02 * max(self.image.width(), self.image.height()))
        self.canvas.adjustSize()
        self.canvas.update()

    def adjust_scale(self, initial=False):
        value = self.scalers[self.FIT_WINDOW if initial else self.zoom_mode]()
        self.zoom_widget.setValue(int(100 * value))

    def scale_fit_window(self):
        """Figure out the size of the pixmap in order to fit the main widget."""

        if not self.canvas.pixmap or self.canvas.pixmap.isNull():
            return 1

        e = 2.0  # So that no scrollbars are generated.
        w1 = self.centralWidget().width() - e
        h1 = self.centralWidget().height() - e
        a1 = w1 / h1
        # Calculate a new scale value based on the pixmap's aspect ratio.
        w2 = self.canvas.pixmap.width() - 0.0
        h2 = self.canvas.pixmap.height() - 0.0
        a2 = w2 / h2
        return w1 / w2 if a2 >= a1 else h1 / h2

    def scale_fit_width(self):
        # The epsilon does not seem to work too well here.
        w = self.centralWidget().width() - 2.0
        return w / self.canvas.pixmap.width()

    def closeEvent(self, event):
        """
        Handles the close event of the MainWindow.
        Saves current settings and ensures that resources are properly released.
        """
        # Check if it's safe to continue closing
        if not self.may_continue():
            event.ignore()
            return

        # Stop the camera if it's running in annotation mode
        if self.annotation_mode == "camera" and self.camera_manager.is_running():
            self.camera_manager.stop_current_camera()

        # Gather all necessary data
        filename = self.file_path if self.dir_name is None and self.file_path else ""

        size = self.size()
        pos = self.pos()
        state = self.saveState()

        line_color = self.line_color
        fill_color = self.fill_color

        recent_files = self.recent_files

        is_advanced = not self._beginner

        camera_settings = self.camera_manager.get_settings()

        default_save_dir = self.default_save_dir if self.default_save_dir and os.path.exists(self.default_save_dir) else "./"
        # tukkan adi kullan sonra.
        camera_save_dir = self.camera_save_dir if self.camera_save_dir and os.path.exists(self.camera_save_dir) else "./camera"
        last_open_dir = self.last_open_dir if self.last_open_dir and os.path.exists(self.last_open_dir) else ""

        auto_save = self.actions.auto_saving.isChecked()
        single_class = self.actions.single_class_mode.isChecked()
        paint_label = self.actions.display_label_option.isChecked()
        draw_square = self.actions.draw_squares_option.isChecked()

        # Save all settings at once
        self.settings.save_all_settings(
            filename,
            size,
            pos,
            state,
            line_color,
            fill_color,
            recent_files,
            is_advanced,
            camera_settings,
            default_save_dir,
            camera_save_dir,
            last_open_dir,
            auto_save,
            single_class,
            paint_label,
            draw_square,
            self.label_file_format,
        )

        # Attempt to save settings and handle potential failures
        if not self.settings.save():
            QMessageBox.warning(self, "Save Error", "Failed to save application settings.")

        # Proceed with the default close event handling
        super(MainWindow, self).closeEvent(event)

    def load_recent(self, filename):
        if self.may_continue():
            self.load_file(filename)

    def change_save_dir_dialog(self, _value=False):
        if self.default_save_dir is not None:
            path = self.default_save_dir
        else:
            path = "."

        dir_path = QFileDialog.getExistingDirectory(
            self, "%s - Save annotations to the directory" % self.__appname__, path, QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
        )

        if dir_path is not None and len(dir_path) > 1:
            self.default_save_dir = dir_path

        self.show_bounding_box_from_annotation_file(self.file_path)

        self.status("%s . Annotation will be saved to %s" % ("Change saved folder", self.default_save_dir))

    def open_annotation_dialog(self, _value=False):
        if self.file_path is None:
            self.setup_status_bar("Please select an image first")
            return

        path = os.path.dirname(self.file_path) if self.file_path else "."
        filters = "Annotation files (*%s *%s *%s)" % (XML_EXT, TXT_EXT, JSON_EXT)
        filename, _ = QFileDialog.getOpenFileName(self, f"{self.__appname__} - Choose an annotation file", path, filters)
        if filename:
            self.load_annotations(filename, self.file_path)

    def open_dir_dialog(self, _value=False, dir_path=None, silent=False):
        if not self.may_continue():
            return

        default_open_dir_path = dir_path if dir_path else "."
        if self.last_open_dir and os.path.exists(self.last_open_dir):
            default_open_dir_path = self.last_open_dir
        else:
            default_open_dir_path = os.path.dirname(self.file_path) if self.file_path else "."
        if silent is not True:
            target_dir_path = QFileDialog.getExistingDirectory(
                self, f"{self.__appname__} - Open Directory", default_open_dir_path, QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
            )
        else:
            target_dir_path = default_open_dir_path
        self.last_open_dir = target_dir_path
        self.import_dir_images(target_dir_path)
        self.default_save_dir = target_dir_path
        if self.file_path:
            self.show_bounding_box_from_annotation_file(file_path=self.file_path)

    def import_dir_images(self, dir_path):
        if not self.may_continue() or not dir_path:
            return

        self.last_open_dir = dir_path
        self.dir_name = dir_path
        self.file_path = None

        # Use dock manager to update file list
        self.m_img_list = scan_all_images(dir_path)
        if self.dock_manager:
            self.dock_manager.update_file_list(self.m_img_list)
        else:
            self.list_widget.clear()
            for img_path in self.m_img_list:
                item = QListWidgetItem(img_path)
                self.list_widget.addItem(item)

        # Update image count and open the first image
        self.img_count = len(self.m_img_list)
        if self.img_count > 0:
            self.open_next_image()

    def verify_image(self, _value=False):
        # Proceeding next image without dialog if having any label
        if self.file_path is not None:
            try:
                self.label_file.toggle_verify()
            except AttributeError:
                # If the labelling file does not exist yet, create if and
                # re-save it with the verified attribute.
                self.save_file()
                if self.label_file is not None:
                    self.label_file.toggle_verify()
                else:
                    return

            self.canvas.verified = self.label_file.verified
            self.paint_canvas()
            self.save_file()

    def open_prev_image(self, _value=False):
        # Proceeding prev image without dialog if having any label
        if self.auto_saving.isChecked():
            if self.default_save_dir is not None:
                if self.dirty is True:
                    self.save_file()
            else:
                self.change_save_dir_dialog()
                return

        if not self.may_continue():
            return

        if self.img_count <= 0:
            return

        if self.file_path is None:
            return

        if self.cur_img_idx - 1 >= 0:
            self.cur_img_idx -= 1
            filename = self.m_img_list[self.cur_img_idx]
            if filename:
                self.load_file(filename)

    def open_next_image(self, _value=False):
        # Proceeding next image without dialog if having any label
        if self.auto_saving.isChecked():
            if self.default_save_dir is not None:
                if self.dirty is True:
                    self.save_file()
            else:
                self.change_save_dir_dialog()
                return

        if not self.may_continue():
            return

        if self.img_count <= 0:
            return

        if not self.m_img_list:
            return

        filename = None
        if self.file_path is None:
            filename = self.m_img_list[0]
            self.cur_img_idx = 0
        else:
            if self.cur_img_idx + 1 < self.img_count:
                self.cur_img_idx += 1
                filename = self.m_img_list[self.cur_img_idx]

        if filename:
            self.load_file(filename)

    def open_file(self):
        """Open an image or label file."""
        if not self.may_continue():
            return
        path = os.path.dirname(self.file_path) if self.file_path else "."
        formats = ["*.%s" % fmt.data().decode("ascii").lower() for fmt in QImageReader.supportedImageFormats()]

        # Get dialog options from file_handler
        options = self.file_handler.get_open_file_dialog_options(path, formats)

        filename, _ = QFileDialog.getOpenFileName(self, f"{self.__appname__} - Choose Image or Label file", options["directory"], options["filters"])

        if filename:
            if isinstance(filename, (tuple, list)):
                filename = filename[0]
            self.cur_img_idx = 0
            self.img_count = 1
            self.load_file(filename)

    def save_file(self, _value=False):
        if self.annotation_mode == "camera":
            save_dir = self.camera_save_dir
        else:
            save_dir = self.default_save_dir

        if save_dir and os.path.exists(save_dir):
            if self.file_path:
                image_file_name = os.path.basename(self.file_path)
                saved_file_name = os.path.splitext(image_file_name)[0]
                saved_path = os.path.join(save_dir, saved_file_name)
                self._save_file(saved_path)
        else:
            image_file_dir = os.path.dirname(self.file_path)
            image_file_name = os.path.basename(self.file_path)
            saved_file_name = os.path.splitext(image_file_name)[0]
            saved_path = os.path.join(image_file_dir, saved_file_name)
            self._save_file(saved_path if self.label_file else self.save_file_dialog(remove_ext=False))

    def save_file_as(self, _value=False):
        assert not self.image.isNull(), "cannot save empty image"
        self._save_file(self.save_file_dialog())

    def save_file_dialog(self, remove_ext=True):
        caption = "%s - Choose File" % self.__appname__
        open_dialog_path = self.current_path()
        filename_without_extension = os.path.splitext(self.file_path)[0]

        # Get dialog options from file_handler
        options = self.file_handler.get_save_file_dialog_options(open_dialog_path, filename_without_extension)

        dlg = QFileDialog(self, caption, options["directory"], options["filters"])
        dlg.setDefaultSuffix(options["default_suffix"])
        dlg.setAcceptMode(QFileDialog.AcceptSave)
        dlg.selectFile(options["default_filename"])
        dlg.setOption(QFileDialog.DontUseNativeDialog, False)

        if dlg.exec_():
            full_file_path = dlg.selectedFiles()[0]
            if remove_ext:
                return os.path.splitext(full_file_path)[0]  # Return file path without the extension.
            else:
                return full_file_path
        return ""

    def _save_file(self, annotation_file_path):
        if annotation_file_path and self.save_labels(annotation_file_path):
            self.set_clean()
            self.status(f"Saved to {annotation_file_path}")  # Use status instead of setup_status_bar

    def close_file(self, _value=False):
        if not self.may_continue():
            return
        self.reset_state()  # Calls set_clean -> update_action_states

    def delete_image(self):
        delete_path = self.file_path
        if delete_path is not None:
            idx = self.cur_img_idx
            if os.path.exists(delete_path):
                os.remove(delete_path)
            self.import_dir_images(self.last_open_dir)
            if self.img_count > 0:
                self.cur_img_idx = min(idx, self.img_count - 1)
                filename = self.m_img_list[self.cur_img_idx]
                self.load_file(filename)
            else:
                self.close_file()

    def reset_all(self):
        self.settings.reset()
        self.close()
        process = QProcess()
        process.startDetached(os.path.abspath(__file__))

    def may_continue(self):
        if not self.dirty:
            return True
        else:
            discard_changes = self.discard_changes_dialog()
            if discard_changes == QMessageBox.No:
                return True
            elif discard_changes == QMessageBox.Yes:
                self.save_file()
                return True
            else:
                return False

    def discard_changes_dialog(self):
        yes, no, cancel = QMessageBox.Yes, QMessageBox.No, QMessageBox.Cancel
        msg = 'You have unsaved changes, would you like to save them and proceed?\nClick "No" to undo all changes.'
        return QMessageBox.warning(self, "Attention", msg, yes | no | cancel)

    def error_message(self, title, message):
        return QMessageBox.critical(self, title, "<p><b>%s</b></p>%s" % (title, message))

    def current_path(self):
        return os.path.dirname(self.file_path) if self.file_path else "."

    def delete_selected_shape(self):
        selected_shape = self.canvas.delete_selected()
        if selected_shape:  # Check if a shape was actually deleted
            self.remove_label(selected_shape)
            self.set_dirty()
            # Shape specific actions and run model button state handled by update_action_states

    def choose_shape_line_color(self):
        color = self.color_dialog.getColor(self.line_color, "Choose Line Color", default=DEFAULT_LINE_COLOR)
        if color:
            if self.canvas.selected_shape:
                self.canvas.selected_shape.line_color = color
                self.canvas.update()
                self.set_dirty()

    def choose_shape_fill_color(self):
        color = self.color_dialog.getColor(self.fill_color, "Choose Fill Color", default=DEFAULT_FILL_COLOR)
        if color:
            if self.canvas.selected_shape:
                self.canvas.selected_shape.fill_color = color
                self.canvas.update()
                self.set_dirty()

    def copy_shape(self):
        if self.canvas.selected_shape is None:
            # True if one accidentally touches the left mouse button before releasing
            return
        new_shape = self.canvas.copy_selected_shape()  # Use the returned new shape
        self.add_label(new_shape)
        self.set_dirty()

    def move_shape(self):
        self.canvas.end_move(copy=False)
        self.set_dirty()

    def load_annotations(self, annotation_path, image_path=None):
        """Load annotations from a file and display them on the canvas."""
        annotations = self.file_handler.load_annotations(annotation_path, image_path, self.image)

        if annotations:
            self.load_labels(annotations["shapes"])
            self.canvas.verified = annotations.get("verified", False)
            self.canvas.update()  # Ensure canvas updates with the shapes
            self.status("Annotations loaded successfully.")

            # Inform the camera manager if homography data is present
            if self.camera_manager.current_camera_thread:
                for shape in self.canvas.shapes:
                    if hasattr(shape, "homography") and shape.homography and hasattr(shape, "shelve_id") and shape.shelve_id:
                        # Set the homography for the camera
                        self.camera_manager.current_camera_thread.set_homography(shape.homography, shape.shelve_id)
                        self.status(f"Loaded homography data for shelve '{shape.shelve_id}'.")
        else:
            self.status(f"Failed to load annotations from {annotation_path}")

        self.update_action_states()  # Update states after loading annotations

    def copy_previous_bounding_boxes(self):
        current_index = self.m_img_list.index(self.file_path)
        if current_index - 1 >= 0:
            prev_file_path = self.m_img_list[current_index - 1]
            self.show_bounding_box_from_annotation_file(prev_file_path)
            self.save_file()

    def toggle_auto_saving(self, checked):
        """Toggle auto-saving of annotations."""
        self.settings.set(SETTING_AUTO_SAVE, checked)
        self.status(f"Auto-saving {'enabled' if checked else 'disabled'}.")

    def toggle_single_class_mode(self, checked):
        """Toggle single-class annotation mode."""
        self.settings.set(SETTING_SINGLE_CLASS, checked)
        self.status(f"Single-class mode {'enabled' if checked else 'disabled'}.")
        if checked and self.label_hist:
            self.current_label = self.label_hist[-1]
            print("Single-class mode: Set label to:", self.current_label)

    def toggle_paint_labels_option(self):
        """Toggle the display of labels on bounding boxes."""
        checked = self.actions.display_label_option.isChecked()
        for shape in self.canvas.shapes:
            shape.paint_label = checked
        self.canvas.update()

    def toggle_draw_square(self, checked):
        """Toggle drawing squares instead of rectangles."""
        self.canvas.set_drawing_shape_to_square(checked)
        self.settings.set(SETTING_DRAW_SQUARE, checked)

    @pyqtSlot(QTreeWidgetItem, name="itemDoubleClicked")
    def on_camera_selected(self, item: QTreeWidgetItem):
        """Slot called when a camera is selected from the list"""
        if item.childCount() > 0:  # This is an main node
            return

        camera_data = item.data(0, Qt.ItemDataRole.UserRole)
        if not isinstance(camera_data, dict):
            self.error_message("Camera Error", "Invalid camera data")
            return

        rtsp_url = camera_data.get("rtsp_url")
        camera_name = camera_data.get("name")

        if rtsp_url:
            # Check if there are unsaved changes before switching cameras
            if not self.may_continue():
                return  # User canceled the operation

            # Stop previous camera using the existing stop_camera method
            self.stop_camera()
            logger.info("Previous camera thread stopped successfully")

            # Reset state after stopping the camera
            self.reset_state()

            # Load the camera thread using CameraManager
            logger.info(f"Loading new camera: {camera_name} with URL: {rtsp_url}")
            video_thread = self.camera_manager.load_camera(rtsp_url, camera_name, self, self.handle_camera_error_signal)

            if video_thread:
                self.camera_manager.current_camera_thread = video_thread
                # Connect signals directly from the video_thread
                video_thread.frame_updated.connect(self.update_canvas_frame)
                video_thread.camera_error.connect(self.handle_camera_error_signal)
                self.is_awaiting_first_frame = True
                self.status(f"Starting camera {camera_name}...")
                logger.info(f"Camera {camera_name} started successfully")
            else:
                self.error_message("Camera Error", f"Failed to initialize camera thread for {camera_name}.")
                logger.error(f"Failed to initialize camera thread for {camera_name}")
            self.update_action_states()
        else:
            self.error_message("Camera Selection Error", "No valid camera selected or RTSP URL found.")

    @pyqtSlot(QImage)
    def update_canvas_frame(self, frame_image):
        """Slot to receive QImage frames from VideoThread and update the canvas."""
        if frame_image and not frame_image.isNull():
            # Update internal image reference
            self.image = frame_image
            # Convert QImage to QPixmap and update the canvas
            frame_pixmap = QPixmap.fromImage(frame_image)
            self.canvas.update_image(frame_pixmap)

            # Handle first frame logic
            if self.is_awaiting_first_frame and self.camera_manager.current_camera_thread:
                self.is_awaiting_first_frame = False  # Reset flag immediately
                camera_name = self.camera_manager.current_camera_thread.camera_name
                print(f"First frame received for {camera_name}, adjusting UI elements.")
                self.file_path = os.path.join(self.camera_save_dir or ".", f"{camera_name}.jpg")
                # can be redundant so can be removed.
                try:
                    if not os.path.exists(os.path.dirname(self.file_path)):
                        os.makedirs(os.path.dirname(self.file_path))
                    if frame_image.save(self.file_path):
                        print(f"Saved first frame to {self.file_path}")
                    else:
                        print(f"Failed to save first frame to {self.file_path}")
                except Exception as e:
                    print(f"Error saving first frame: {e}")

                self.canvas.load_pixmap(QPixmap.fromImage(self.image))
                self.canvas.set_editing(True)

                self.set_clean()
                self.adjust_scale(initial=True)
                self.paint_canvas()
                self.canvas.setFocus(Qt.FocusReason.MouseFocusReason)

                # Load annotations for camera
                print(f"Loading annotations for {camera_name} after first frame.")
                self.camera_manager.load_annotations(camera_name)

    def update_mode_indicator(self):
        """Update the mode indicator in the status bar."""
        mode_text = self.get_str("cameraModeIndicator") if self.annotation_mode == "camera" else self.get_str("fileModeIndicator")
        self.mode_label.setText(mode_text)

    def on_homography_points_selected(self, image_points):
        """Handle when homography points are selected in the canvas."""
        if not self.current_shelve_index:
            self.error_message(self.get_str("shelfError"), self.get_str("invalidShelfSelection"))
            return

        # Calculate homography and update shape
        homography_data = homography_points_selected(self, self.shelve_dimensions[self.current_shelve_index], image_points)

        if homography_data and self.canvas.shapes:
            # Find the shape created for homography and set its roi_type
            for shape in self.canvas.shapes:
                if shape.homography is not None:
                    shape.roi_type = "emptyshelve"
                    self.status(f"Homography computed and saved for shelve '{self.current_shelve_index}' as emptyshelve ROI")
                    break

        # Return to normal editing mode
        if self.beginner():
            self.canvas.set_editing(True)

        self.update_action_states()  # Update states after homography selection

    @pyqtSlot(str)
    def handle_camera_error_signal(self, error_message):
        """Displays the error message received from the camera thread."""
        self.error_message("Camera Error", error_message)
        self.status(f"Camera error: {error_message}")

        # Reset canvas state for clean error handling without fallback images
        if self.camera_manager.current_camera_thread:
            self.camera_manager.current_camera_thread = None
        self.update_action_states()
