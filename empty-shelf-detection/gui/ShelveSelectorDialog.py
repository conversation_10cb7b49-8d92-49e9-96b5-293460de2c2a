#!/usr/bin/python
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QTableWidget,
    QTableWidgetItem, QAbstractItemView, QPushButton, QMessageBox
)
from PyQt5.QtCore import Qt

class ShelveSelectorDialog(QDialog):
    def __init__(self, parent=None, shelve_dimensions=None):
        super(ShelveSelectorDialog, self).__init__(parent)
        self.shelve_dimensions = shelve_dimensions or {}
        self.selected_shelve = None
        self.setup_ui()
        
    def setup_ui(self):
        self.setWindowTitle("Raf Seçimi")
        self.setMinimumWidth(600)
        self.setMinimumHeight(400)
        
        layout = QVBoxLayout()
        
        # Summary label
        self.summary_label = QLabel()
        self.update_summary_label()
        layout.addWidget(self.summary_label)
        
        # Search bar
        search_layout = QHBoxLayout()
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("<PERSON><PERSON> ara (<PERSON><PERSON> adı veya numarası)")
        self.search_edit.textChanged.connect(self.filter_shelves)
        search_layout.addWidget(self.search_edit)
        
        # Create the table
        self.shelve_table = QTableWidget()
        self.shelve_table.setColumnCount(5)
        self.shelve_table.setHorizontalHeaderLabels(["ID", "İsim", "Genişlik (cm)", "Yükseklik (cm)", "Raf Sayısı"])
        self.shelve_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.shelve_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.shelve_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.shelve_table.doubleClicked.connect(self.accept_selection)
        self.shelve_table.setSortingEnabled(True)
        
        # Button layout
        button_layout = QHBoxLayout()
        self.cancel_button = QPushButton("İptal")
        self.select_button = QPushButton("Seç")
        self.cancel_button.clicked.connect(self.reject)
        self.select_button.clicked.connect(self.accept_selection)
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.select_button)
        
        # Add all components to main layout
        layout.addLayout(search_layout)
        layout.addWidget(self.shelve_table)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
        # Populate the table
        self.populate_table()
        
        # Set column widths
        self.shelve_table.setColumnWidth(0, 50)  # ID
        self.shelve_table.setColumnWidth(1, 250) # Name
    
    def update_summary_label(self):
        """Update the summary label with information about the shelves."""
        total_shelves = len(self.shelve_dimensions)
        
        # Count how many shelves have different shelve counts
        shelve_count_distribution = {}
        for shelve_id, shelve_data in self.shelve_dimensions.items():
            shelve_count = shelve_data.get('shelve_count', 0)
            if shelve_count in shelve_count_distribution:
                shelve_count_distribution[shelve_count] += 1
            else:
                shelve_count_distribution[shelve_count] = 1
        
        shelve_count_info = ", ".join([f"{count} raf: {amount} adet" for count, amount in shelve_count_distribution.items()])
        
        self.summary_label.setText(f"<b>Toplam {total_shelves} raf bulundu.</b> {shelve_count_info}")
        
    def populate_table(self):
        """Populate the table with shelve data."""
        self.shelve_table.setRowCount(0)  # Clear the table
        row = 0
        
        for shelve_index, shelve_data in self.shelve_dimensions.items():
            self.shelve_table.insertRow(row)
            
            # Add data to cells
            id_item = QTableWidgetItem(shelve_data.get('id', ''))
            name_item = QTableWidgetItem(shelve_data.get('name', ''))
            width_item = QTableWidgetItem(str(shelve_data.get('width_cm', '')))
            height_item = QTableWidgetItem(str(shelve_data.get('height_cm', '')))
            shelve_count_item = QTableWidgetItem(str(shelve_data.get('shelve_count', '')))
            
            # Store the full shelve_index as the UserRole data
            id_item.setData(Qt.UserRole, shelve_index)
            
            self.shelve_table.setItem(row, 0, id_item)
            self.shelve_table.setItem(row, 1, name_item)
            self.shelve_table.setItem(row, 2, width_item)
            self.shelve_table.setItem(row, 3, height_item)
            self.shelve_table.setItem(row, 4, shelve_count_item)
            
            row += 1
    
    def filter_shelves(self, search_text):
        """Filter the shelves based on search text."""
        for row in range(self.shelve_table.rowCount()):
            match = False
            
            
            # Check in ID and Name columns (0 and 1)
            for col in range(2):
                item = self.shelve_table.item(row, col)
                if item and search_text.lower() in item.text().lower():
                    match = True
                    break
            
            # Show/hide the row based on the match
            self.shelve_table.setRowHidden(row, not match)
    
    def accept_selection(self):
        """Accept the current selection and close the dialog."""
        selected_rows = self.shelve_table.selectionModel().selectedRows()
        if selected_rows:
            # Get the first selected row
            row = selected_rows[0].row()
            # Get the full shelve_index from the UserRole data
            self.selected_shelve = self.shelve_table.item(row, 0).data(Qt.UserRole)
            self.accept()
        else:
            QMessageBox.warning(self, "Seçim Hatası", "Lütfen bir raf seçin.")
    
    @staticmethod
    def get_selected_shelve(parent, shelve_dimensions):
        """Static method to create the dialog and return the selected shelve."""
        dialog = ShelveSelectorDialog(parent, shelve_dimensions)
        result = dialog.exec_()
        if result == QDialog.Accepted:
            return dialog.selected_shelve
        return None 