import os
import sys
import unittest
from unittest.mock import MagicMock, patch

import numpy as np
import supervision as sv

# Add the parent directory to the path so we can import the modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.trackers import ShelfTracker


class TestShelfTracker(unittest.TestCase):
    """
    Unit tests for the ShelveTracker class.
    """

    def setUp(self):
        """Set up test fixtures."""
        # Mock the logger and config
        self.logger_patcher = patch("core.get_logger")
        self.mock_logger = self.logger_patcher.start()
        self.mock_logger.return_value = MagicMock()

        self.config_patcher = patch("core.get_config")
        self.mock_config = self.config_patcher.start()
        self.mock_config.return_value = MagicMock()
        self.mock_config.return_value.get.side_effect = self._mock_config_get

        # Create a tracker
        self.tracker = ShelfTracker("test_camera", "test_shelve")

    def tearDown(self):
        """Tear down test fixtures."""
        self.logger_patcher.stop()
        self.config_patcher.stop()

    def _mock_config_get(self, section, key, default=None):
        """Mock the config.get method."""
        config_values = {
            "tracker": {
                "empty_threshold": 5,
                "max_missing_frames": 3,
                "iou_threshold": 0.3,
                "api_report_interval": 30,
                "min_report_periods": 5,
            },
            "api": {
                "url": "http://test-api.com",
                "timeout": 10,
                "max_retries": 3,
                "retry_delay": 5,
            },
        }

        if section in config_values and key in config_values[section]:
            return config_values[section][key]
        return default

    def create_detection(self, bbox):
        """Helper method to create a detection object"""
        box = np.array([bbox])
        return sv.Detections(xyxy=box, confidence=np.array([0.9]), class_id=np.array([0]))

    def test_init(self):
        """Test initialization of the tracker."""
        self.assertEqual(self.tracker.camera_name, "test_camera")
        self.assertEqual(self.tracker.shelve_name, "test_shelve")
        self.assertEqual(self.tracker.EMPTY_THRESHOLD, 5)
        self.assertEqual(self.tracker.MAX_MISSING_FRAMES, 3)
        self.assertEqual(self.tracker.IOU_THRESHOLD, 0.3)
        self.assertEqual(self.tracker.API_REPORT_INTERVAL, 30)
        self.assertEqual(self.tracker.MIN_REPORT_PERIODS, 5)
        self.assertEqual(len(self.tracker.tracked_spots), 0)
        self.assertEqual(len(self.tracker.reported_spots), 0)

    def test_spot_becomes_reported_after_threshold(self):
        """Test that a spot becomes reported after being detected for EMPTY_THRESHOLD frames."""
        bbox = [0, 0, 10, 10]
        detection = self.create_detection(bbox)

        # Track for EMPTY_THRESHOLD - 1 frames
        for frame in range(1, self.tracker.EMPTY_THRESHOLD):
            track_ids = self.tracker.track_detections(detection, frame)
            self.assertEqual(len(track_ids), 1)
            self.assertEqual(track_ids[0], 1)  # Should maintain same track ID
            self.assertFalse(self.tracker.tracked_spots[1]["reported"])
            self.assertEqual(len(self.tracker.reported_spots), 0)

        # Track for the threshold frame
        track_ids = self.tracker.track_detections(detection, self.tracker.EMPTY_THRESHOLD)
        self.assertTrue(self.tracker.tracked_spots[1]["reported"])
        self.assertEqual(len(self.tracker.reported_spots), 1)
        self.assertEqual(self.tracker.report_counts[1], 1)

    def test_spot_unreported_when_missed(self):
        """Test that a spot's empty_count decreases when missed and becomes unreported when below threshold."""
        bbox = [0, 0, 10, 10]
        detection = self.create_detection(bbox)

        # First get the spot to be reported
        for frame in range(1, self.tracker.EMPTY_THRESHOLD + 1):
            self.tracker.track_detections(detection, frame)

        self.assertTrue(self.tracker.tracked_spots[1]["reported"])
        initial_empty_count = self.tracker.tracked_spots[1]["empty_count"]

        # Miss the spot for 2 frames
        missed_frames = 2
        empty_detection = sv.Detections.empty()
        self.tracker.track_detections(empty_detection, self.tracker.EMPTY_THRESHOLD + missed_frames)

        # Check that empty_count decreased
        self.assertEqual(
            self.tracker.tracked_spots[1]["empty_count"],
            max(0, initial_empty_count - missed_frames),
        )

        # Miss enough frames to go below threshold
        missed_frames = self.tracker.EMPTY_THRESHOLD
        self.tracker.track_detections(empty_detection, self.tracker.EMPTY_THRESHOLD + missed_frames)

        # Spot should be removed after MAX_MISSING_FRAMES
        self.assertNotIn(1, self.tracker.tracked_spots)
        self.assertEqual(len(self.tracker.reported_spots), 0)

    def test_spot_removed_after_max_missing_frames(self):
        """Test that a spot is removed after being missed for more than MAX_MISSING_FRAMES."""
        bbox = [0, 0, 10, 10]
        detection = self.create_detection(bbox)

        # First get the spot tracked
        self.tracker.track_detections(detection, 1)
        self.assertIn(1, self.tracker.tracked_spots)

        # Miss the spot for MAX_MISSING_FRAMES
        empty_detection = sv.Detections.empty()
        self.tracker.track_detections(empty_detection, self.tracker.MAX_MISSING_FRAMES + 1)

        # Spot should still be tracked
        self.assertIn(1, self.tracker.tracked_spots)

        # Miss the spot for one more frame
        self.tracker.track_detections(empty_detection, self.tracker.MAX_MISSING_FRAMES + 2)

        # Spot should be removed
        self.assertNotIn(1, self.tracker.tracked_spots)

    def test_spot_requires_min_report_periods(self):
        """Test that a spot needs MIN_REPORT_PERIODS before being reported to API."""
        bbox = [0, 0, 10, 10]
        detection = self.create_detection(bbox)

        # Get spot to be reported
        for frame in range(1, self.tracker.EMPTY_THRESHOLD + 1):
            self.tracker.track_detections(detection, frame)

        self.assertTrue(self.tracker.tracked_spots[1]["reported"])

        # For this test, we'll manually set the report_counts to test the behavior
        # First, set it to just below MIN_REPORT_PERIODS
        self.tracker.report_counts[1] = self.tracker.MIN_REPORT_PERIODS - 1

        # Verify it's not reportable yet
        reportable_objects = [track_id for track_id, count in self.tracker.report_counts.items() if count >= self.tracker.MIN_REPORT_PERIODS]
        self.assertEqual(len(reportable_objects), 0)

        # Now set it to exactly MIN_REPORT_PERIODS
        self.tracker.report_counts[1] = self.tracker.MIN_REPORT_PERIODS

        # Now should be reportable
        reportable_objects = [track_id for track_id, count in self.tracker.report_counts.items() if count >= self.tracker.MIN_REPORT_PERIODS]
        self.assertEqual(len(reportable_objects), 1)

    def test_compute_iou(self):
        """Test computation of IoU."""
        # Import compute_iou directly from core
        from core import compute_iou

        # Test with non-overlapping boxes
        box1 = [0, 0, 10, 10]
        box2 = [20, 20, 30, 30]
        self.assertEqual(compute_iou(box1, box2), 0)

        # Test with partially overlapping boxes
        box1 = [0, 0, 10, 10]
        box2 = [5, 5, 15, 15]
        self.assertAlmostEqual(compute_iou(box1, box2), 0.14285714285714285)

        # Test with fully overlapping boxes
        box1 = [0, 0, 10, 10]
        box2 = [0, 0, 10, 10]
        self.assertEqual(compute_iou(box1, box2), 1.0)

    def test_spot_state_maintained_when_redetected(self):
        """Test that a spot maintains its state when redetected after being missed."""
        bbox = [0, 0, 10, 10]
        detection = self.create_detection(bbox)

        # First get the spot to be reported
        for frame in range(1, self.tracker.EMPTY_THRESHOLD + 1):
            self.tracker.track_detections(detection, frame)

        self.assertTrue(self.tracker.tracked_spots[1]["reported"])
        initial_empty_count = self.tracker.tracked_spots[1]["empty_count"]

        # Miss the spot for 2 frames (less than MAX_MISSING_FRAMES)
        missed_frames = 2
        empty_detection = sv.Detections.empty()
        current_frame = self.tracker.EMPTY_THRESHOLD + missed_frames
        self.tracker.track_detections(empty_detection, current_frame)

        # Redetect the spot
        self.tracker.track_detections(detection, current_frame + 1)

        # Check that the spot is still tracked with the same ID
        self.assertIn(1, self.tracker.tracked_spots)
        # Empty count should have decreased by missed frames and then increased by 1
        expected_count = max(0, initial_empty_count - missed_frames) + 1
        self.assertEqual(self.tracker.tracked_spots[1]["empty_count"], expected_count)


if __name__ == "__main__":
    unittest.main()
