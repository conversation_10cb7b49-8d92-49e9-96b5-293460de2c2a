import pytest
from PyQt5.QtGui import QImage
from managers.file_handler import <PERSON><PERSON>andler
from libs.constants import FORMAT_PASCALVOC, FORMAT_YOLO, FORMAT_CREATEML
import os
import tempfile
import json

class TestFileHandler:
    @pytest.fixture
    def file_handler(self):
        return FileHandler()
    
    @pytest.fixture
    def sample_image(self):
        # Create a small test image
        return QImage(100, 100, QImage.Format_RGB32)
    
    def test_set_format_pascal_voc(self, file_handler):
        result = file_handler.set_format(FORMAT_PASCALVOC)
        assert result['format'] == FORMAT_PASCALVOC
        assert result['ext'] == '.xml'
        assert result['icon'] == 'format_voc'
        
    def test_set_format_yolo(self, file_handler):
        result = file_handler.set_format(FORMAT_YOLO)
        assert result['format'] == FORMAT_YOLO
        assert result['ext'] == '.txt'
        assert result['icon'] == 'format_yolo'
        
    def test_set_format_createml(self, file_handler):
        result = file_handler.set_format(FORMAT_CREATEML)
        assert result['format'] == FORMAT_CREATEML
        assert result['ext'] == '.json'
        assert result['icon'] == 'format_createml'
    
    def test_load_pascal_xml_nonexistent_file(self, file_handler, sample_image):
        result = file_handler.load_pascal_xml('nonexistent.xml', sample_image)
        assert result is None
    
    def test_load_yolo_txt_nonexistent_file(self, file_handler, sample_image):
        result = file_handler.load_yolo_txt('nonexistent.txt', sample_image)
        assert result is None
    
    def test_load_create_ml_json_nonexistent_file(self, file_handler):
        result = file_handler.load_create_ml_json('nonexistent.json', 'test.jpg')
        assert result is None
    
    def test_load_create_ml_json_with_valid_file(self, file_handler):
        # Create a temporary JSON file with valid CreateML format
        valid_json = {
            "images": [{
                "image": "test.jpg",
                "annotations": [{
                    "label": "test",
                    "coordinates": {
                        "x": 100,
                        "y": 100,
                        "width": 50,
                        "height": 50
                    }
                }]
            }]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(valid_json, f)
            temp_path = f.name
        
        try:
            result = file_handler.load_create_ml_json(temp_path, 'test.jpg')
            assert result is not None
            assert 'shapes' in result
            assert 'verified' in result
        finally:
            os.unlink(temp_path)

    # Additional test cases can be added here for other specific scenarios