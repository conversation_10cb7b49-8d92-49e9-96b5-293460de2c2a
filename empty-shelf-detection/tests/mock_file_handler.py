from typing import Optional, Dict, Any
from PyQt5.QtGui import QImage
from managers.file_handler import FileHandlerInterface

class MockF<PERSON>Handler(FileHandlerInterface):
    def __init__(self):
        self.set_format_called = False
        self.load_pascal_xml_called = False
        self.load_yolo_txt_called = False
        self.load_create_ml_json_called = False
        
    def set_format(self, format_type: str) -> Optional[Dict[str, str]]:
        self.set_format_called = True
        return {
            'ext': '.test',
            'format': format_type,
            'icon': 'test_icon'
        }
    
    def load_pascal_xml(self, xml_path: str, image: QImage) -> Optional[Dict[str, Any]]:
        self.load_pascal_xml_called = True
        return {
            'shapes': [],
            'verified': False
        }
    
    def load_yolo_txt(self, txt_path: str, image: QImage) -> Optional[Dict[str, Any]]:
        self.load_yolo_txt_called = True
        return {
            'shapes': [],
            'verified': False
        }
    
    def load_create_ml_json(self, json_path: str, file_path: str) -> Optional[Dict[str, Any]]:
        self.load_create_ml_json_called = True
        return {
            'shapes': [],
            'verified': False
        }