from PyQt5.QtWidgets import QApplication
from unittest import TestCase
from PyQt5.QtCore import QPointF
from pathlib import Path
from gui.ROIGui import MainWindow
from managers.file_handler import FileHandler
from libs.labelFile import LabelFileFormat
from utilities.HomoGraphyFunctions import homography_points_selected

# Assuming these are your custom imports - adjust paths as needed

class TestAnnotations(TestCase):
    @classmethod
    def setUpClass(cls):
        cls.app = QApplication([])
    
    @classmethod
    def tearDownClass(cls):
        cls.app.quit()
        
    def setUp(self):
        self.test_dir = Path("test_outputs")
        self.test_dir.mkdir(exist_ok=True)
        
    def tearDown(self):
        # Clean up test files
        for file in self.test_dir.glob("*"):
            file.unlink()
        self.test_dir.rmdir()

    def test_multiple_annotations_different_formats(self):
        # Create mock mainwindow and related attributes
        file_handler = FileHandler()
        mainwindow = MainWindow("TestApp", file_handler, None)
        mainwindow.current_shelve_index = "Shelve_A"
        mainwindow.shelve_dimensions = {
            "Shelve_A": {
                "width_cm": 100,
                "height_cm": 50,
                "real_world_points": [[0, 0], [100, 0], [100, 50], [0, 50]]
            },
            "Shelve_B": {
                "width_cm": 200,
                "height_cm": 100,
                "real_world_points": [[0, 0], [200, 0], [200, 100], [0, 100]]
            }
        }
        mainwindow.canvas.shapes = []
        mainwindow.image_data = {'width': 1920, 'height': 1080, 'depth': 3}
        mainwindow.file_handler = FileHandler()

        # Simulate selecting homography points for Shelve_A
        image_points_a = [QPointF(100, 100), QPointF(1100, 100), 
                         QPointF(1100, 500), QPointF(100, 500)]
        homography_points_selected(mainwindow, mainwindow.shelve_dimensions[mainwindow.current_shelve_index], image_points_a)

        # Simulate selecting homography points for Shelve_B
        mainwindow.current_shelve_index = "Shelve_B"
        image_points_b = [QPointF(1200, 100), QPointF(2200, 100), 
                         QPointF(2200, 600), QPointF(1200, 600)]
        homography_points_selected(mainwindow, mainwindow.shelve_dimensions[mainwindow.current_shelve_index], image_points_b)

        # Test different formats
        formats = {
            LabelFileFormat.PASCAL_VOC: "xml",
            LabelFileFormat.YOLO: "txt",
            LabelFileFormat.CREATE_ML: "json"
        }

        for format_type, extension in formats.items():
            with self.subTest(format=format_type):
                # Set format
                mainwindow.file_handler.set_format(format_type)
                
                # Save annotations
                annotation_file = self.test_dir / f"test_annotation.{extension}"
                success = mainwindow.save_labels(str(annotation_file))
                self.assertTrue(success, f"Failed to save annotations in {format_type}")

                # Load annotations
                loaded_data = mainwindow.file_handler.load_annotations(
                    str(annotation_file),
                    mainwindow.file_path,
                    None,
                    class_list=["Shelve_A", "Shelve_B"]
                )
                
                self.assertIsNotNone(loaded_data, f"Failed to load annotations in {format_type}")
                self.assertEqual(len(loaded_data['shapes']), 2, 
                               f"Incorrect number of shapes loaded in {format_type}")