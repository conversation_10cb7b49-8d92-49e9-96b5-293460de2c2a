from abc import ABC, abstractmethod
from typing import List, Dict, Callable, Optional
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from gui.ROIGui import MainWindow

class State(ABC):
    @abstractmethod
    def handle(self, context: 'MainWindow') -> None:
        pass

    @abstractmethod
    def on_enter(self, context: 'MainWindow') -> None:
        pass

    @abstractmethod
    def on_exit(self, context: 'MainWindow') -> None:
        pass


class EditingState(State):
    def handle(self, context: 'MainWindow') -> None:
        pass  # Add any continuous handling logic here

    def on_enter(self, context: 'MainWindow') -> None:
        context.canvas.setEditing(True)
        context.actions['createMode'].setEnabled(True)
        context.actions['editMode'].setEnabled(False)

    def on_exit(self, context: 'MainWindow') -> None:
        pass  # Add any cleanup logic here

class DrawingState(State):
    def handle(self, context: 'MainWindow') -> None:
        pass  # Add any continuous handling logic here

    def on_enter(self, context: 'MainWindow') -> None:
        context.canvas.setEditing(False)
        context.actions['createMode'].setEnabled(False)
        context.actions['editMode'].setEnabled(True)

    def on_exit(self, context: 'MainWindow') -> None:
        pass  # Add any cleanup logic here

class StateManager:
    def __init__(self, initial_state: State):
        self._state = initial_state

    def set_state(self, context: 'MainWindow', new_state: State) -> None:
        self._state.on_exit(context)
        self._state = new_state
        self._state.on_enter(context)

    def handle(self, context: 'MainWindow') -> None:
        self._state.handle(context)

    @property
    def current_state(self) -> State:
        return self._state
