# state_manager.py
from dataclasses import dataclass, field
from typing import List, Dict, Optional
from PyQt5.QtGui import QColor, QImage
from PyQt5.QtCore import QSize, QPoint



#@dataclass
class ImageState:
    current_index: int = 0
    total_images: int = 0
    file_list: List[str] = field(default_factory=list)
    current_path: Optional[str] = None
    dirty: bool = False
    verified: bool = False

#@dataclass
class AnnotationState:
    shapes: list = field(default_factory=list)
    labels: List[str] = field(default_factory=list)
    selected_shape: Optional[object] = None

#@dataclass
class ViewState:
    zoom_level: int = 100
    light_level: int = 50
    window_size: QSize = QSize(800, 600)
    window_position: QPoint = QPoint(0, 0)

class AppState:
    def __init__(self):
        self.image = ImageState()
        self.annotation = AnnotationState()
        self.view = ViewState()
        self.recent_files: List[str] = []
        self.current_mode: str = 'file'  # 'file' or 'camera'
        
    def reset_image_state(self):
        self.image = ImageState()
        
    def reset_annotation_state(self):
        self.annotation = AnnotationState()