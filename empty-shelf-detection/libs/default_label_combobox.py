
from PyQt5.QtWidgets import QWidget, QHBoxLayout, QComboBox
from typing import List, Optional



class DefaultLabelComboBox(QWidget):
    def __init__(self, parent: Optional[QWidget] = None, items: Optional[List[str]] = None):
        super().__init__(parent)

        if items is None:
            items = []

        layout = QHBoxLayout()
        self.cb = QComboBox()
        self.items = items
        self.cb.addItems(self.items)

        self.cb.currentIndexChanged.connect(parent.default_label_combo_selection_changed)

        layout.addWidget(self.cb)
        self.setLayout(layout)