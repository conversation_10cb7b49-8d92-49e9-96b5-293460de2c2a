#!/usr/bin/python
# -*- coding: utf-8 -*-


import sys

from libs.utils import distance
from PyQt5.QtCore import *
from PyQt5.QtGui import *

DEFAULT_LINE_COLOR = QColor(0, 255, 0, 128)
DEFAULT_FILL_COLOR = QColor(255, 0, 0, 128)
DEFAULT_SELECT_LINE_COLOR = QColor(255, 255, 255)
DEFAULT_SELECT_FILL_COLOR = QColor(0, 128, 255, 155)
DEFAULT_VERTEX_FILL_COLOR = QColor(0, 255, 0, 255)
DEFAULT_HVERTEX_FILL_COLOR = QColor(255, 0, 0)


class Shape(object):
    P_SQUARE, P_ROUND = range(2)

    MOVE_VERTEX, NEAR_VERTEX = range(2)

    # The following class variables influence the drawing
    # of _all_ shape objects.
    line_color = DEFAULT_LINE_COLOR
    fill_color = DEFAULT_FILL_COLOR
    select_line_color = DEFAULT_SELECT_LINE_COLOR
    select_fill_color = DEFAULT_SELECT_FILL_COLOR
    vertex_fill_color = DEFAULT_VERTEX_FILL_COLOR
    h_vertex_fill_color = DEFAULT_HVERTEX_FILL_COLOR
    point_type = P_ROUND
    point_size = 16
    scale = 1.0
    label_font_size = 8

    def __init__(
        self,
        label=None,
        line_color=None,
        difficult=False,
        paint_label=False,
        shape_type="rectangle",
        fixed=False,
        homography=None,
        homography_points=None,
        shelve_id=None,
        magaza_no=None,
        shelve_width_cm=None,
        shelve_height_cm=None,
        roi_type=None,
    ):
        self.label = label
        self.points = []
        self.fill = False
        self.selected = False
        self.difficult = difficult
        self.paint_label = paint_label
        self.shape_type = shape_type
        self.skew_amount = 0.0
        self.fixed = fixed
        self.homography = homography
        self.homography_points = homography_points or []
        self.shelve_id = shelve_id
        self.magaza_no = magaza_no
        self.shelve_width_cm = shelve_width_cm
        self.shelve_height_cm = shelve_height_cm
        self.roi_type = roi_type

        self._highlight_index = None
        self._highlight_mode = self.NEAR_VERTEX
        self._highlight_settings = {
            self.NEAR_VERTEX: (4, self.P_ROUND),
            self.MOVE_VERTEX: (1.5, self.P_SQUARE),
        }

        self._closed = False

        if line_color is not None:
            self.line_color = line_color

    @classmethod
    def create(
        cls,
        label,
        shape_type="rectangle",
        # Visual properties
        line_color=None,
        fill_color=None,
        paint_label=False,
        # Shelve properties
        shelve_id=None,
        magaza_no=None,
        shelve_width_cm=None,
        shelve_height_cm=None,
        # Homography properties
        homography=None,
        homography_points=None,
        # Other properties
        difficult=False,
        fixed=False,
        points=None,
        skew_amount=0.0,
        # ROI properties
        roi_type=None,
    ):
        """
        Create a new shape with all possible parameters.

        Args:
            label (str): The label/name of the shape
            shape_type (str): Type of shape (e.g., "rectangle")

            # Visual properties
            line_color (QColor): Color of the shape outline
            fill_color (QColor): Fill color of the shape
            paint_label (bool): Whether to display the label on the shape

            # Shelve properties
            shelve_id (str): Shelve ID (Reyon No)
            magaza_no (str): Store number
            shelve_width_cm (float): Width of the shelve in centimeters
            shelve_height_cm (float): Height of the shelve in centimeters

            # Homography properties
            homography (list): Homography matrix as list
            homography_points (list): Points used for homography calculation

            # Other properties
            difficult (bool): Whether this is a difficult annotation
            fixed (bool): Whether shape position is fixed
            points (list): List of (x, y) coordinates defining the shape
            skew_amount (float): Amount of skew applied to the shape

            # ROI properties
            roi_type (str): Type of ROI (e.g., "emptyshelve")

        Returns:
            Shape: A new shape instance
        """
        shape = cls(
            label=label,
            line_color=line_color,
            difficult=difficult,
            paint_label=paint_label,
            shape_type=shape_type,
            fixed=fixed,
            homography=homography,
            homography_points=homography_points,
            shelve_id=shelve_id,
            magaza_no=magaza_no,
            shelve_width_cm=shelve_width_cm,
            shelve_height_cm=shelve_height_cm,
            roi_type=roi_type,
        )

        if fill_color:
            shape.fill_color = fill_color

        if points:
            for point in points:
                shape.add_point(QPointF(*point))

        shape.skew_amount = skew_amount

        return shape

    def to_dict(self):
        """Convert the shape to a dictionary for serialization."""
        return {
            "label": self.label,
            "line_color": self.line_color.getRgb(),
            "fill_color": self.fill_color.getRgb(),
            "points": [(p.x(), p.y()) for p in self.points],
            "skew_amount": getattr(self, "skew_amount", 0.0),
            "difficult": self.difficult,
            "shape_type": self.shape_type,
            "flags": {},
            "homography": getattr(self, "homography", None),
            "homography_points": getattr(self, "homography_points", []),
            "shelve_id": getattr(self, "shelve_id", None),
            "magaza_no": getattr(self, "magaza_no", None),
            "shelve_width_cm": getattr(self, "shelve_width_cm", None),
            "shelve_height_cm": getattr(self, "shelve_height_cm", None),
            "roi_type": getattr(self, "roi_type", None),
        }

    @classmethod
    def from_dict(cls, shape_data):
        """Create a Shape instance from a dictionary."""
        # Check if shape_data is already a Shape object
        if isinstance(shape_data, Shape):
            return shape_data

        # Otherwise, process it as a dictionary
        return cls.create(
            label=shape_data.get("label"),
            line_color=QColor(*shape_data.get("line_color", (0, 255, 0, 128))),
            fill_color=QColor(*shape_data.get("fill_color")) if "fill_color" in shape_data else None,
            difficult=shape_data.get("difficult", False),
            paint_label=shape_data.get("paint_label", False),
            shape_type=shape_data.get("shape_type", "rectangle"),
            fixed=shape_data.get("fixed", False),
            homography=shape_data.get("homography"),
            homography_points=shape_data.get("homography_points", []),
            shelve_id=shape_data.get("shelve_id"),
            magaza_no=shape_data.get("magaza_no"),
            points=shape_data.get("points", []),
            skew_amount=shape_data.get("skew_amount", 0.0),
            shelve_width_cm=shape_data.get("shelve_width_cm"),
            shelve_height_cm=shape_data.get("shelve_height_cm"),
            roi_type=shape_data.get("roi_type"),
        )

    def close(self):
        self._closed = True

    def reach_max_points(self):
        if len(self.points) >= 4:
            return True
        return False

    def add_point(self, point):
        if not self.reach_max_points():
            self.points.append(point)

    def pop_point(self):
        if self.points:
            return self.points.pop()
        return None

    def is_closed(self):
        return self._closed

    def set_open(self):
        self._closed = False

    def paint(self, painter):
        if self.points:
            color = self.select_line_color if self.selected else self.line_color
            pen = QPen(color)

            if self.fixed:
                pen.setStyle(Qt.DashLine)
            else:
                pen.setStyle(Qt.SolidLine)

            # Try using integer sizes for smoother drawing(?)
            pen.setWidth(max(1, int(round(2.0 / self.scale))))
            painter.setPen(pen)

            line_path = QPainterPath()
            vertex_path = QPainterPath()

            line_path.moveTo(self.points[0])

            for i, p in enumerate(self.points):
                line_path.lineTo(p)
                self.draw_vertex(vertex_path, i)
            if self.is_closed():
                line_path.lineTo(self.points[0])

            painter.drawPath(line_path)
            painter.drawPath(vertex_path)
            painter.fillPath(vertex_path, self.vertex_fill_color)

            # Draw text at the top-left
            if self.paint_label:
                min_x = sys.maxsize
                min_y = sys.maxsize
                min_y_label = int(1.25 * self.label_font_size)
                for point in self.points:
                    min_x = min(min_x, point.x())
                    min_y = min(min_y, point.y())
                if min_x != sys.maxsize and min_y != sys.maxsize:
                    font = QFont()
                    font.setPointSize(self.label_font_size)
                    font.setBold(True)
                    painter.setFont(font)
                    if self.label is None:
                        self.label = ""
                    if min_y < min_y_label:
                        min_y += min_y_label
                    painter.drawText(int(min_x), int(min_y), self.label)

            if self.fill:
                color = self.select_fill_color if self.selected else self.fill_color
                painter.fillPath(line_path, color)

    def draw_vertex(self, path, i):
        d = self.point_size / self.scale
        shape = self.point_type
        point = self.points[i]
        if i == self._highlight_index:
            size, shape = self._highlight_settings[self._highlight_mode]
            d *= size
        if self._highlight_index is not None:
            self.vertex_fill_color = self.h_vertex_fill_color
        else:
            self.vertex_fill_color = Shape.vertex_fill_color
        if shape == self.P_SQUARE:
            path.addRect(point.x() - d / 2, point.y() - d / 2, d, d)
        elif shape == self.P_ROUND:
            path.addEllipse(point, d / 2.0, d / 2.0)
        else:
            assert False, "unsupported vertex shape"

    def nearest_vertex(self, point, epsilon):
        index = None
        for i, p in enumerate(self.points):
            dist = distance(p - point)
            if dist <= epsilon:
                index = i
                epsilon = dist
        return index

    def contains_point(self, point):
        return self.make_path().contains(point)

    def make_path(self):
        path = QPainterPath(self.points[0])
        for p in self.points[1:]:
            path.lineTo(p)
        return path

    def bounding_rect(self):
        return self.make_path().boundingRect()

    def move_by(self, offset):
        if not self.fixed:
            self.points = [p + offset for p in self.points]

    def move_vertex_by(self, i, offset):
        if not self.fixed:
            self.points[i] = self.points[i] + offset

    def highlight_vertex(self, i, action):
        self._highlight_index = i
        self._highlight_mode = action

    def highlight_clear(self):
        self._highlight_index = None

    def copy(self):
        shape = Shape("%s" % self.label)
        shape.points = [p for p in self.points]
        shape.fill = self.fill
        shape.selected = self.selected
        shape._closed = self._closed
        if self.line_color != Shape.line_color:
            shape.line_color = self.line_color
        if self.fill_color != Shape.fill_color:
            shape.fill_color = self.fill_color
        shape.difficult = self.difficult
        shape.shape_type = self.shape_type
        shape.skew_amount = self.skew_amount

        return shape

    def __len__(self):
        return len(self.points)

    def __getitem__(self, key):
        return self.points[key]

    def __setitem__(self, key, value):
        self.points[key] = value

    def apply_skew(self, skew_amount):
        """Apply skew transformation to the shape."""
        self.skew_amount += skew_amount
        center_x = sum(p.x() for p in self.points) / len(self.points)
        for point in self.points:
            dx = point.x() - center_x
            point.setY(point.y() + dx * skew_amount)

    def reset_skew(self):
        """Reset the skew of the shape."""
        if self.skew_amount != 0:
            self.apply_skew(-self.skew_amount)
        self.skew_amount = 0.0
