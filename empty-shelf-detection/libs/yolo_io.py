#!/usr/bin/env python
# -*- coding: utf8 -*-
import codecs
import os
import json
from libs.constants import DEFAULT_ENCODING
from libs.shape import Shape

TXT_EXT = '.txt'
JSON_EXT = '.json'
ENCODE_METHOD = DEFAULT_ENCODING

class YOLOWriter:

    def __init__(self, folder_name, filename, img_size, database_src='Unknown', local_img_path=None):
        self.folder_name = folder_name
        self.filename = filename
        self.database_src = database_src
        self.img_size = img_size
        self.box_list = []
        self.local_img_path = local_img_path
        self.verified = False

    def add_bnd_box(self, x_min, y_min, x_max, y_max, name, difficult, skew_amount=0.0, homography=None, homography_points=[], shelve_id=None, magaza_no=None, shelve_width_cm=None, shelve_height_cm=None, roi_type=None):
        bnd_box = {
            'xmin': x_min,
            'ymin': y_min,
            'xmax': x_max,
            'ymax': y_max,
            'name': name,
            'difficult': difficult,
            'skew_amount': skew_amount,
            'homography': homography,
            'homography_points': homography_points,
            'shelve_id': shelve_id,
            'magaza_no': magaza_no,
            'shelve_width_cm': shelve_width_cm,
            'shelve_height_cm': shelve_height_cm,
            'roi_type': roi_type
        }
        self.box_list.append(bnd_box)

    def bnd_box_to_yolo_line(self, box, class_list=[]):
        x_min = box['xmin']
        x_max = box['xmax']
        y_min = box['ymin']
        y_max = box['ymax']

        x_center = float((x_min + x_max)) / 2 / self.img_size[1]
        y_center = float((y_min + y_max)) / 2 / self.img_size[0]

        w = float((x_max - x_min)) / self.img_size[1]
        h = float((y_max - y_min)) / self.img_size[0]

        # PR387
        box_name = box['name']
        if box_name not in class_list:
            class_list.append(box_name)

        class_index = class_list.index(box_name)

        return class_index, x_center, y_center, w, h

    def save(self, class_list=[], target_file=None):

        out_file = None  # Update yolo .txt
        out_class_file = None   # Update class list .txt
        out_json_file = None    # Update metadata .json

        if target_file is None:
            out_file = open(
            self.filename + TXT_EXT, 'w', encoding=ENCODE_METHOD)
            classes_file = os.path.join(os.path.dirname(os.path.abspath(self.filename)), "classes.txt")
            out_class_file = open(classes_file, 'w')
            json_filename = self.filename + JSON_EXT
            out_json_file = open(json_filename, 'w', encoding='utf-8')

        else:
            out_file = codecs.open(target_file, 'w', encoding=ENCODE_METHOD)
            classes_file = os.path.join(os.path.dirname(os.path.abspath(target_file)), "classes.txt")
            out_class_file = open(classes_file, 'w')
            json_filename = target_file.replace(TXT_EXT, JSON_EXT)
            out_json_file = open(json_filename, 'w', encoding='utf-8')
        
        annotations = []


        for box in self.box_list:
            class_index, x_center, y_center, w, h = self.bnd_box_to_yolo_line(box, class_list)
            # print (classIndex, x_center, y_center, w, h)
            out_file.write("%d %.6f %.6f %.6f %.6f\n" % (class_index, x_center, y_center, w, h))
            # Collect metadata
            annotation_meta = {
                'label': box['name'],
                'difficult': box['difficult'],
                'skew_amount': box.get('skew_amount', 0.0),
                'homography': box.get('homography', None),
                'homography_points': box.get('homography_points', []),
                'shelve_id': box.get('shelve_id', None),
                'magaza_no': box.get('magaza_no', None),
                'shelve_width_cm': box.get('shelve_width_cm', None),
                'shelve_height_cm': box.get('shelve_height_cm', None),
                'roi_type': box.get('roi_type', None)
            }
            annotations.append(annotation_meta)

        # print (classList)
        # print (out_class_file)
        for c in class_list:
            out_class_file.write(c+'\n')
        out_class_file.close()
        
        # Write metadata JSON
        with open(json_filename, 'w', encoding='utf-8') as f_json:
            json.dump(annotations, f_json, ensure_ascii=False, indent=4)

        out_file.close()



class YoloReader:

    def __init__(self, file_path, image, class_list_path=None):
        # shapes type:
        # [label, [(x1,y1), (x2,y2), (x3,y3), (x4,y4)], color, color, difficult, skew_amount, homography, homography_points, shelve_id, magaza_no]
        self.shapes = []
        self.file_path = file_path

        if class_list_path is None:
            dir_path = os.path.dirname(os.path.realpath(self.file_path))
            self.class_list_path = os.path.join(dir_path, "classes.txt")
        else:
            self.class_list_path = class_list_path

        # print (file_path, self.class_list_path)

        classes_file = open(self.class_list_path, 'r')
        self.classes = classes_file.read().strip('\n').split('\n')

        # print (self.classes)

        img_size = [image.height(), image.width(),
                    1 if image.isGrayscale() else 3]

        self.img_size = img_size

        self.verified = False
        # try:
        self.parse_yolo_format()
        # except:
        #     pass

    def get_shapes(self):
        return self.shapes

    def add_shape(self, label, x_min, y_min, x_max, y_max, difficult, skew_amount=0.0, homography=None, homography_points=[], shelve_id=None, magaza_no=None, shelve_width_cm=None, shelve_height_cm=None, roi_type=None):
        points = [(x_min, y_min), (x_max, y_min), (x_max, y_max), (x_min, y_max)]
        
        shape = Shape.create(
            label=label,
            shape_type='rectangle',
            # Shelve properties
            shelve_id=shelve_id,
            magaza_no=magaza_no,
            shelve_width_cm=shelve_width_cm,
            shelve_height_cm=shelve_height_cm,
            # Homography properties
            homography=homography,
            homography_points=homography_points,
            # Other properties
            difficult=difficult,
            points=points,
            skew_amount=skew_amount,
            # ROI properties
            roi_type=roi_type
        )
        
        self.shapes.append(shape)

    def yolo_line_to_shape(self, class_index, x_center, y_center, w, h):
        
        label = self.classes[int(class_index)]

        x_min = max(float(x_center) - float(w) / 2, 0)
        x_max = min(float(x_center) + float(w) / 2, 1)
        y_min = max(float(y_center) - float(h) / 2, 0)
        y_max = min(float(y_center) + float(h) / 2, 1)

        x_min = round(self.img_size[1] * x_min)
        x_max = round(self.img_size[1] * x_max)
        y_min = round(self.img_size[0] * y_min)
        y_max = round(self.img_size[0] * y_max)

        return label, x_min, y_min, x_max, y_max

    def parse_yolo_format(self):
        
        with open(self.file_path, 'r') as bnd_box_file:
        
            for bndBox in bnd_box_file:
                parts = bndBox.strip().split()
                if len(parts) < 5:
                    continue

                class_index, x_center, y_center, w, h = parts[:5]

                class_index, x_center, y_center, w, h = bndBox.strip().split(' ')
                label, x_min, y_min, x_max, y_max = self.yolo_line_to_shape(class_index, x_center, y_center, w, h)

                # Initial shape with basic properties
                self.add_shape(label, x_min, y_min, x_max, y_max, False)
        
        # Load additional metadata from JSON if available
        json_filename = self.file_path.replace(TXT_EXT, JSON_EXT)
        if os.path.exists(json_filename):
            with open(json_filename, 'r', encoding='utf-8') as f_json:
                metadata = json.load(f_json)
            # Assuming metadata list matches the shapes list
            for shape, meta in zip(self.shapes, metadata):
                shape['difficult'] = meta.get('difficult', False)
                shape['skew_amount'] = meta.get('skew_amount', 0.0)
                shape['homography'] = meta.get('homography', None)
                shape['homography_points'] = meta.get('homography_points', [])
                shape['shelve_id'] = meta.get('shelve_id', None)
                shape['magaza_no'] = meta.get('magaza_no', None)
                shape['shelve_width_cm'] = meta.get('shelve_width_cm', None)
                shape['shelve_height_cm'] = meta.get('shelve_height_cm', None)
                shape['roi_type'] = meta.get('roi_type', None)