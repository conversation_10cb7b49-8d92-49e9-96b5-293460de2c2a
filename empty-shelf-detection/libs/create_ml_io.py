#!/usr/bin/env python
# -*- coding: utf8 -*-
import json
from pathlib import Path
from libs.constants import DEFAULT_ENCODING
import os
from libs.shape import Shape

JSON_EXT = '.json'
ENCODE_METHOD = DEFAULT_ENCODING


class CreateMLWriter:
    def __init__(self, folder_name, filename, img_size, shapes, output_file, database_src='Unknown', local_img_path=None):
        self.folder_name = folder_name
        self.filename = filename
        self.database_src = database_src
        self.img_size = img_size
        self.box_list = []
        self.local_img_path = local_img_path
        self.verified = False
        self.shapes = shapes
        self.output_file = output_file

        # Added fields
        self.homography = None
        self.name = ''
        self.homography_points = []

    def write(self):
        if os.path.isfile(self.output_file):
            with open(self.output_file, "r") as file:
                input_data = file.read()
                output_dict = json.loads(input_data)
        else:
            output_dict = []

        output_image_dict = {
            "image": self.filename,
            "verified": self.verified,
            "annotations": []
        }

        for shape in self.shapes:
            points = shape["points"]

            x1 = points[0][0]
            y1 = points[0][1]
            x2 = points[1][0]
            y2 = points[2][1]

            height, width, x, y = self.calculate_coordinates(x1, x2, y1, y2)

            shape_dict = {
                "label": shape["label"],
                "coordinates": {
                    "x": x,
                    "y": y,
                    "width": width,
                    "height": height
                },
                "difficult": shape.get('difficult', False),
                "skew_amount": shape.get('skew_amount', 0.0),
                "homography": shape.get('homography', None),
                "homography_points": shape.get('homography_points', []),
                "shelve_id": shape.get('shelve_id', None),
                "magaza_no": shape.get('magaza_no', None)
            }
            output_image_dict["annotations"].append(shape_dict)

        # check if image already in output
        exists = False
        for i in range(0, len(output_dict)):
            if output_dict[i]["image"] == output_image_dict["image"]:
                exists = True
                output_dict[i] = output_image_dict
                break

        if not exists:
            output_dict.append(output_image_dict)

        Path(self.output_file).write_text(json.dumps(output_dict), ENCODE_METHOD)

    def calculate_coordinates(self, x1, x2, y1, y2):
        if x1 < x2:
            x_min = x1
            x_max = x2
        else:
            x_min = x2
            x_max = x1
        if y1 < y2:
            y_min = y1
            y_max = y2
        else:
            y_min = y2
            y_max = y1
        width = x_max - x_min
        if width < 0:
            width = width * -1
        height = y_max - y_min
        # x and y from center of rect
        x = x_min + width / 2
        y = y_min + height / 2
        return height, width, x, y


class CreateMLReader:
    def __init__(self, json_path, file_path):
        self.json_path = json_path
        self.shapes = []
        self.verified = False
        self.filename = os.path.basename(file_path)
        try:
            self.parse_json()
        except ValueError:
            print("JSON decoding failed")

    def parse_json(self):
        with open(self.json_path, "r") as file:
            input_data = file.read()

        # Returns a list
        output_list = json.loads(input_data)
        if len(self.shapes) > 0:
            self.shapes = []

        if output_list:
            image_entry = next((item for item in output_list if item["image"] == self.filename), None)
            if image_entry:
                self.verified = image_entry.get("verified", False)
                for shape in image_entry.get("annotations", []):
                    label = shape.get("label", "unknown")
                    coords = shape.get("coordinates", {})
                    x = coords.get("x", 0)
                    y = coords.get("y", 0)
                    width = coords.get("width", 0)
                    height = coords.get("height", 0)
                    
                    # Convert to bounding box
                    x_min = x - width / 2
                    y_min = y - height / 2
                    x_max = x + width / 2
                    y_max = y + height / 2

                    points = [(x_min, y_min), (x_max, y_min), (x_max, y_max), (x_min, y_max)]
                    
                    # Create shape dictionary with all properties
                    shape_dict = {
                        'label': label,
                        'points': points,
                        'difficult': shape.get("difficult", False),
                        'skew_amount': shape.get("skew_amount", 0.0),
                        'homography': shape.get("homography", None),
                        'homography_points': shape.get("homography_points", []),
                        'shelve_id': shape.get("shelve_id", None),
                        'magaza_no': shape.get("magaza_no", None),
                        'shape_type': 'rectangle'
                    }
                    self.shapes.append(shape_dict)

    def add_shape(self, label, bnd_box):
        x_min = bnd_box["x"] - (bnd_box["width"] / 2)
        y_min = bnd_box["y"] - (bnd_box["height"] / 2)
        x_max = bnd_box["x"] + (bnd_box["width"] / 2)
        y_max = bnd_box["y"] + (bnd_box["height"] / 2)

        points = [(x_min, y_min), (x_max, y_min), (x_max, y_max), (x_min, y_max)]
        
        shape = Shape.create(
            label=label,
            shape_type='rectangle',
            difficult=False,
            points=points
        )
        
        self.shapes.append(shape)

    def get_shapes(self):
        return self.shapes
