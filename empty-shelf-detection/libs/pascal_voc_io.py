#!/usr/bin/env python
# -*- coding: utf8 -*-
import sys
from xml.etree import ElementTree
from xml.etree.ElementTree import Element, SubElement
from lxml import etree
import codecs
from libs.constants import DEFAULT_ENCODING
import json
from libs.shape import Shape


XML_EXT = '.xml'
ENCODE_METHOD = DEFAULT_ENCODING

class PascalVocWriter:

    def __init__(self, folder_name, filename, img_size, database_src='Unknown', local_img_path=None):
        self.folder_name = folder_name
        self.filename = filename
        self.database_src = database_src
        self.img_size = img_size
        self.box_list = []
        self.local_img_path = local_img_path
        self.verified = False

    def prettify(self, elem):
        """
            Return a pretty-printed XML string for the Element.
        """
        rough_string = ElementTree.tostring(elem, 'utf8')
        root = etree.fromstring(rough_string)
        return etree.tostring(root, pretty_print=True, encoding=ENCODE_METHOD).replace("  ".encode(), "\t".encode())

    def gen_xml(self):
        """
            Return XML root
        """
        # Check conditions
        if self.filename is None or \
                self.folder_name is None or \
                self.img_size is None:
            return None

        top = Element('annotation')
        if self.verified:
            top.set('verified', 'yes')

        folder = SubElement(top, 'folder')
        folder.text = self.folder_name

        filename = SubElement(top, 'filename')
        filename.text = self.filename

        if self.local_img_path is not None:
            local_img_path = SubElement(top, 'path')
            local_img_path.text = self.local_img_path

        source = SubElement(top, 'source')
        database = SubElement(source, 'database')
        database.text = self.database_src

        size_part = SubElement(top, 'size')
        width = SubElement(size_part, 'width')
        height = SubElement(size_part, 'height')
        depth = SubElement(size_part, 'depth')
        width.text = str(self.img_size[1])
        height.text = str(self.img_size[0])
        if len(self.img_size) == 3:
            depth.text = str(self.img_size[2])
        else:
            depth.text = '1'

        segmented = SubElement(top, 'segmented')
        segmented.text = '0'
        return top

    def add_bnd_box(self, x_min, y_min, x_max, y_max, name, difficult, skew_amount, homography=None, homography_points=[], shelve_id=None, magaza_no=None, shelve_width_cm=None, shelve_height_cm=None, roi_type=None):
        bnd_box = {'xmin': x_min, 'ymin': y_min, 'xmax': x_max, 'ymax': y_max}
        bnd_box['name'] = name
        bnd_box['difficult'] = difficult
        bnd_box['skew'] = skew_amount
        bnd_box['homography'] = homography
        bnd_box['homography_points'] = homography_points
        bnd_box['shelve_id'] = shelve_id
        bnd_box['magaza_no'] = magaza_no
        bnd_box['shelve_width_cm'] = shelve_width_cm
        bnd_box['shelve_height_cm'] = shelve_height_cm
        bnd_box['roi_type'] = roi_type
        self.box_list.append(bnd_box)

    def append_objects(self, top):
        for each_object in self.box_list:
            object_item = SubElement(top, 'object')
            name = SubElement(object_item, 'name')
            name.text = each_object['name']
            pose = SubElement(object_item, 'pose')
            pose.text = "Unspecified"
            truncated = SubElement(object_item, 'truncated')
            if int(float(each_object['ymax'])) == int(float(self.img_size[0])) or (int(float(each_object['ymin'])) == 1):
                truncated.text = "1"  # max == height or min
            elif (int(float(each_object['xmax'])) == int(float(self.img_size[1]))) or (int(float(each_object['xmin'])) == 1):
                truncated.text = "1"  # max == width or min
            else:
                truncated.text = "0"
            difficult = SubElement(object_item, 'difficult')
            difficult.text = str(bool(each_object['difficult']) & 1)
            bnd_box = SubElement(object_item, 'bndbox')
            x_min = SubElement(bnd_box, 'xmin')
            x_min.text = str(each_object['xmin'])
            y_min = SubElement(bnd_box, 'ymin')
            y_min.text = str(each_object['ymin'])
            x_max = SubElement(bnd_box, 'xmax')
            x_max.text = str(each_object['xmax'])
            y_max = SubElement(bnd_box, 'ymax')
            y_max.text = str(each_object['ymax'])
            skew = SubElement(object_item, 'skew')
            skew.text = str(each_object.get('skew', 0.0))
            
            if each_object.get('homography') is not None:
                homography = SubElement(object_item, 'homography')
                homography.text = json.dumps(each_object['homography'])  # Store as JSON string

            if each_object.get('homography_points'):
                homography_pts = SubElement(object_item, 'homography_points')
                for point in each_object['homography_points']:
                    pt = SubElement(homography_pts, 'point')
                    x_pt = SubElement(pt, 'x')
                    x_pt.text = str(point[0])
                    y_pt = SubElement(pt, 'y')
                    y_pt.text = str(point[1])
                    
            if each_object.get('shelve_id'):
                shelve_id = SubElement(object_item, 'shelve_id')
                shelve_id.text = each_object['shelve_id']
            
            if each_object.get('magaza_no'):
                magaza_no = SubElement(object_item, 'magaza_no')
                magaza_no.text = each_object['magaza_no']
                
            if each_object.get('shelve_width_cm'):
                shelve_width = SubElement(object_item, 'shelve_width_cm')
                shelve_width.text = str(each_object['shelve_width_cm'])
                
            if each_object.get('shelve_height_cm'):
                shelve_height = SubElement(object_item, 'shelve_height_cm')
                shelve_height.text = str(each_object['shelve_height_cm'])
                
            # Add roi_type to XML if it exists
            if each_object.get('roi_type'):
                roi_type = SubElement(object_item, 'roi_type')
                roi_type.text = each_object['roi_type']

    def save(self, target_file=None):
        root = self.gen_xml()
        self.append_objects(root)
        out_file = None
        if target_file is None:
            out_file = codecs.open(
                self.filename + XML_EXT, 'w', encoding=ENCODE_METHOD)
        else:
            out_file = codecs.open(target_file, 'w', encoding=ENCODE_METHOD)

        prettify_result = self.prettify(root)
        out_file.write(prettify_result.decode('utf8'))
        out_file.close()


class PascalVocReader:

    def __init__(self, file_path):
        # shapes type:
        # [label, [(x1,y1), (x2,y2), (x3,y3), (x4,y4)], color, color, difficult, skew_amount, homography, homography_points, shelve_id, magaza_no]
        self.shapes = []
        self.file_path = file_path
        self.verified = False
        try:
            self.parse_xml()
        except:
            pass

    def get_shapes(self):
        return self.shapes

    def add_shape(self, label, bnd_box, difficult, skew, homography=None, homography_points=[], shelve_id=None, magaza_no=None, shelve_width_cm=None, shelve_height_cm=None, roi_type=None):
        x_min = int(float(bnd_box.find('xmin').text))
        y_min = int(float(bnd_box.find('ymin').text))
        x_max = int(float(bnd_box.find('xmax').text))
        y_max = int(float(bnd_box.find('ymax').text))
        points = [(x_min, y_min), (x_max, y_min), (x_max, y_max), (x_min, y_max)]
        
        shape = Shape.create(
            label=label,
            shape_type='rectangle',
            # Shelve properties
            shelve_id=shelve_id,
            magaza_no=magaza_no,
            shelve_width_cm=shelve_width_cm,
            shelve_height_cm=shelve_height_cm,
            # Homography properties
            homography=homography,
            homography_points=homography_points,
            # Other properties
            difficult=difficult,
            points=points,
            skew_amount=skew,
            # ROI properties
            roi_type=roi_type
        )
        
        self.shapes.append(shape)

    def parse_xml(self):
        assert self.file_path.endswith(XML_EXT), "Unsupported file format"
        parser = etree.XMLParser(encoding=ENCODE_METHOD)
        xml_tree = ElementTree.parse(self.file_path, parser=parser).getroot()
        filename = xml_tree.find('filename').text
        try:
            verified = xml_tree.attrib['verified']
            if verified == 'yes':
                self.verified = True
        except KeyError:
            self.verified = False

        for object_iter in xml_tree.findall('object'):
            bnd_box = object_iter.find("bndbox")
            label = object_iter.find('name').text
            # Add chris
            difficult = False
            if object_iter.find('difficult') is not None:
                difficult = bool(int(object_iter.find('difficult').text))
            
            # Get skew amount if it exists
            skew_amount = 0.0
            if object_iter.find('skew') is not None:
                skew_amount = float(object_iter.find('skew').text)
            
            # Get homography data if it exists
            homography = None
            if object_iter.find('homography') is not None:
                homography_text = object_iter.find('homography').text
                try:
                    homography = json.loads(homography_text)
                except json.JSONDecodeError:
                    print("Error parsing homography JSON")
            
            # Get homography points if they exist
            homography_points = []
            if object_iter.find('homography_points') is not None:
                for point in object_iter.find('homography_points').findall('point'):
                    x = float(point.find('x').text)
                    y = float(point.find('y').text)
                    homography_points.append((x, y))
            
            # Get shelve_id if it exists
            shelve_id = None
            if object_iter.find('shelve_id') is not None:
                shelve_id = object_iter.find('shelve_id').text
            
            # Get magaza_no if it exists
            magaza_no = None
            if object_iter.find('magaza_no') is not None:
                magaza_no = object_iter.find('magaza_no').text
                
            # Get shelve dimensions if they exist
            shelve_width_cm = None
            if object_iter.find('shelve_width_cm') is not None:
                shelve_width_cm = float(object_iter.find('shelve_width_cm').text)
                
            shelve_height_cm = None
            if object_iter.find('shelve_height_cm') is not None:
                shelve_height_cm = float(object_iter.find('shelve_height_cm').text)
                
            # Get roi_type if it exists
            roi_type = None
            if object_iter.find('roi_type') is not None:
                roi_type = object_iter.find('roi_type').text

            self.add_shape(label, bnd_box, difficult, skew_amount, homography, homography_points, shelve_id, magaza_no, shelve_width_cm, shelve_height_cm, roi_type)
        return True