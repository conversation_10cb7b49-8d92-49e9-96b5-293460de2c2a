import os
import pickle
from typing import Optional, Any
from PyQt5.QtCore import  QSize, QPoint, QByteArray
from libs.SettingsInterface import SettingsInterface
from typing import Any, Optional
from libs.shape import DEFAULT_LINE_COLOR,DEFAULT_FILL_COLOR 

from libs.constants import (
    SETTING_RECENT_FILES, SETTING_WIN_SIZE, SETTING_SAVE_DIR, 
    SETTING_WIN_POSE, SETTING_LINE_COLOR, SETTING_FILL_COLOR, 
    SETTING_WIN_STATE, SETTING_FILENAME, SETTING_ADVANCE_MODE, 
    CAMERA_SETTING, SETTING_CAMERA_SAVE_DIR, SETTING_LAST_OPEN_DIR,
    SETTING_AUTO_SAVE, SETTING_SINGLE_CLASS, SETTING_PAINT_LABEL,
    SETTING_DRAW_SQUARE, SETTING_LABEL_FILE_FORMAT
)

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from gui.ROIGui import MainWindow


class SettingsManager(SettingsInterface):
    def __init__(self):
        # Be default, the home will be in the same folder as labelImg
        home = os.path.expanduser("~")
        self.data = {}
        self.path = os.path.join(home, '.labelImgSettings.pkl')

    def __setitem__(self, key, value):
        self.data[key] = value

    def __getitem__(self, key):
        return self.data[key]
    
    def set(self, key: str, value: Any) -> None:
        """Set a setting value"""
        self.data[key] = value 
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get a setting value by key"""
        return self.data.get(key, default)

    

    def save(self):
        if self.path:
            with open(self.path, 'wb') as f:
                pickle.dump(self.data, f, pickle.HIGHEST_PROTOCOL)
                return True
        return False

    def load(self):
        try:
            if os.path.exists(self.path):
                with open(self.path, 'rb') as f:
                    self.data = pickle.load(f)
                    return True
            else:
            #if file is not exist load settings inside resources folder named defaultSetting.pkl find
            #use adrress hardcoded but portable.
                from pathlib import Path
                default_setting_path = Path(__file__).parent.parent / "resources" / "defaultSetting.pkl"
                if os.path.exists(default_setting_path):
                    with open(default_setting_path, 'rb') as f:
                        self.data = pickle.load(f)
                        return True

        except:
            print('Loading setting failed')
        return False

    def reset(self):
        if os.path.exists(self.path):
            os.remove(self.path)
            print('Remove setting pkl file ${0}'.format(self.path))
        self.data = {}
        self.path = None
    
    def get_recent_files(self) -> list:
        """Get list of recent files"""
        return self.get(SETTING_RECENT_FILES, [])  # Assuming SETTING_RECENT_FILES is defined elsewhere

    def get_default_save_dir(self) -> Optional[str]:
        """Get default save directory"""
        return self.get(SETTING_SAVE_DIR, None)  # Assuming SETTING_SAVE_DIR is defined
    def get_camera_save_dir(self) -> Optional[str]:
            """Get default save directory"""
            return self.get(SETTING_CAMERA_SAVE_DIR, None)  # Assuming SETTING_SAVE_DIR is defined

    def get_window_settings(self) -> tuple[Any, Any, Any]:
        """Get window-related settings"""
        return (
             self.get(SETTING_WIN_SIZE, QSize(600, 500)),
            self.get(SETTING_WIN_POSE, QPoint(0, 0)),
           self.get(SETTING_WIN_STATE, QByteArray()),
        )

    def get_labeling_settings(self) -> tuple[Any, Any, Any]:
        """Get labeling-related settings"""
        return (
        self.get(SETTING_LINE_COLOR, DEFAULT_LINE_COLOR),
        self.get(SETTING_FILL_COLOR, DEFAULT_FILL_COLOR),
        self.get('difficult', False)
    )

    def save_all_settings(self, filename: str, size: QSize, pos: QPoint, 
        state: QByteArray,
        line_color: Any, 
        fill_color: Any,
        recent_files: list,
        is_advanced: bool,
        camera_settings: dict,
        default_save_dir: Optional[str],
        camera_save_dir: Optional[str],
        last_open_dir: Optional[str],
        auto_save: bool,
        single_class: bool,
        paint_label: bool,
        draw_square: bool,
        label_file_format: str
    ):
        """
        Saves all settings by delegating to specific save methods.
        """
        self.save_filename_setting(filename)
        self.save_window_geometry(size, pos, state)
        self.save_labeling_settings(line_color, fill_color)
        self.save_recent_files(recent_files)
        self.save_mode_settings(is_advanced)
        self.save_camera_settings(camera_settings)
        self.save_directories_properties(default_save_dir, camera_save_dir, last_open_dir)
        self.save_action_settings(auto_save, single_class, paint_label, draw_square)
        self.save_label_file_format(label_file_format)


    def save_filename_setting(self, filename: str):
        self.set(SETTING_FILENAME, filename)

    def save_window_geometry(self, size: QSize, pos: QPoint, state: QByteArray):
        self.set(SETTING_WIN_SIZE, size)
        self.set(SETTING_WIN_POSE, pos)
        self.set(SETTING_WIN_STATE, state)

    def save_labeling_settings(self, line_color: Any, fill_color: Any):
        self.set(SETTING_LINE_COLOR, line_color)
        self.set(SETTING_FILL_COLOR, fill_color)

    def save_recent_files(self, recent_files: list):
        self.set(SETTING_RECENT_FILES, recent_files)

    def save_mode_settings(self, is_advanced: bool):
        self.set(SETTING_ADVANCE_MODE, is_advanced)

    def save_camera_settings(self, camera_settings: dict):
        self.set(CAMERA_SETTING, camera_settings)

    def save_directories_properties(self, default_save_dir: Optional[str], 
                              camera_save_dir: Optional[str], 
                              last_open_dir: Optional[str]):
        # Save default directory
        if default_save_dir and os.path.exists(default_save_dir):
            self.set(SETTING_SAVE_DIR, default_save_dir)
        else:
            self.set(SETTING_SAVE_DIR, '')
            
        # Save camera save directory
        if camera_save_dir and os.path.exists(camera_save_dir):
            self.set(SETTING_CAMERA_SAVE_DIR, camera_save_dir)
        else:
            # Create camera directory if it doesn't exist
            default_camera_dir = './camera'
            os.makedirs(default_camera_dir, exist_ok=True)
            self.set(SETTING_CAMERA_SAVE_DIR, default_camera_dir)

        # Save last opened directory
        if last_open_dir and os.path.exists(last_open_dir):
            self.set(SETTING_LAST_OPEN_DIR, last_open_dir)
        else:
            self.set(SETTING_LAST_OPEN_DIR, '')

    def save_action_settings(self, auto_save: bool, single_class: bool, 
                             paint_label: bool, draw_square: bool):
        self.set(SETTING_AUTO_SAVE, auto_save)
        self.set(SETTING_SINGLE_CLASS, single_class)
        self.set(SETTING_PAINT_LABEL, paint_label)
        self.set(SETTING_DRAW_SQUARE, draw_square)

    def save_label_file_format(self, label_file_format: str):
        self.set(SETTING_LABEL_FILE_FORMAT, label_file_format)



 
    def apply_settings(self, main_window: 'MainWindow'):
        """
        Apply settings to the MainWindow.
        This method centralizes the application of settings to the MainWindow.
        """
        # Window Geometry
        main_window.resize(self.get(SETTING_WIN_SIZE))
        main_window.move(self.get(SETTING_WIN_POSE))
        main_window.restoreState(self.get(SETTING_WIN_STATE))
    
        # Labeling Settings
        main_window.line_color = self.get(SETTING_LINE_COLOR, DEFAULT_LINE_COLOR)
        main_window.fill_color = self.get(SETTING_FILL_COLOR, DEFAULT_FILL_COLOR)
    
        # Recent Files
        main_window.recent_files = self.get(SETTING_RECENT_FILES, [])
    
        # Mode Settings
        main_window._beginner = not self.get(SETTING_ADVANCE_MODE, False)
    
        # Camera Settings
        camera_settings = self.get(CAMERA_SETTING, {"width": 1920, "height": 1080, "frame_rate": 15})
        main_window.camera_manager.apply_settings(camera_settings)
    
        # Save Directories
        main_window.default_save_dir = self.get(SETTING_SAVE_DIR, './')
        main_window.camera_save_dir = self.get(SETTING_CAMERA_SAVE_DIR, './')
        main_window.last_open_dir = self.get(SETTING_LAST_OPEN_DIR, './')
    
        # Action-based Settings
        main_window.actions.auto_saving.setChecked(self.get(SETTING_AUTO_SAVE, False))
        main_window.actions.single_class_mode.setChecked(self.get(SETTING_SINGLE_CLASS, False))
        main_window.actions.display_label_option.setChecked(self.get(SETTING_PAINT_LABEL, False))
        main_window.actions.draw_squares_option.setChecked(self.get(SETTING_DRAW_SQUARE, False))
    
        # Label File Format
        main_window.label_file_format = self.get(SETTING_LABEL_FILE_FORMAT, 'JSON')
    
    def initialize_recent_files(self,main_window:'MainWindow'):

        if self.get(SETTING_RECENT_FILES):
            main_window.recent_files = self.get(SETTING_RECENT_FILES)