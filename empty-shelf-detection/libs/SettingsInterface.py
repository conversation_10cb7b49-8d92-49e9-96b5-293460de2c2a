from abc import ABC, abstractmethod
from typing import Any, Optional



class SettingsInterface(ABC):
    @abstractmethod
    def get(self, key: str, default: Any = None) -> Any:
        """Get a setting value by key
        :rtype: String
        """

    @abstractmethod
    def set(self, key: str, value: Any) -> None:
        """Set a setting value"""

    @abstractmethod
    def save(self) -> None:
        """Save all settings"""

    @abstractmethod
    def reset(self) -> None:
        """Reset settings to defaults"""

    @abstractmethod
    def load(self) -> None:
        """load settings"""

    @abstractmethod
    def get_recent_files(self) -> list:
        """Get list of recent files"""

    @abstractmethod
    def get_default_save_dir(self) -> Optional[str]:
        """Get default save directory"""

    @abstractmethod
    def get_window_settings(self) -> tuple[Any, Any, Any]:
        """Get window-related settings"""

    @abstractmethod
    def get_labeling_settings(self) -> tuple[Any, Any, Any]:
        """Get labeling-related settings"""

    def save_all_settings(self, filename, size, pos, state, line_color, fill_color, recent_files, is_advanced,
                          camera_settings, default_save_dir, camera_save_dir, last_open_dir, auto_save, single_class,
                          paint_label, draw_square, label_file_format):
        pass