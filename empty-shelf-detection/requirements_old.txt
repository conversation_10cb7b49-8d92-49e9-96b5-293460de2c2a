-i https://pypi.org/simple
albumentations==1.3.1
aliyun-python-sdk-core==2.14.0
aliyun-python-sdk-kms==2.16.2
appdirs==1.4.4
certifi==2023.7.22 ; python_version >= '3.6'
cffi==1.16.0 ; python_version >= '3.8'
chardet==4.0.0 ; python_version >= '2.7' and python_version not in '3.0, 3.1, 3.2, 3.3, 3.4'
charset-normalizer==3.3.2 ; python_full_version >= '3.7.0'
click==8.1.7 ; python_version >= '3.7'
colorama==0.4.6 ; python_version >= '2.7' and python_version not in '3.0, 3.1, 3.2, 3.3, 3.4, 3.5, 3.6'
contourpy==1.2.0 ; python_version >= '3.9'
crcmod==1.7
cryptography==41.0.7 ; python_version >= '3.7'
cycler==0.10.0
docker-pycreds==0.4.0
filelock==3.13.1 ; python_version >= '3.8'
fonttools==4.46.0 ; python_version >= '3.8'
fsspec==2023.12.1 ; python_version >= '3.8'
gitdb==4.0.11 ; python_version >= '3.7'
gitpython==3.1.40 ; python_version >= '3.7'
huggingface-hub==0.19.4 ; python_full_version >= '3.8.0'
idna==2.10 ; python_version >= '2.7' and python_version not in '3.0, 3.1, 3.2, 3.3'
imageio==2.33.0 ; python_version >= '3.8'
jinja2==3.1.2 ; python_version >= '3.7'
jmespath==0.10.0 ; python_version >= '2.6' and python_version not in '3.0, 3.1, 3.2'
joblib==1.3.2 ; python_version >= '3.7'
kiwisolver==1.4.5 ; python_version >= '3.7'
lazy-loader==0.3 ; python_version >= '3.7'
markdown==3.5.1 ; python_version >= '3.8'
markdown-it-py==3.0.0 ; python_version >= '3.8'
markupsafe==2.1.3 ; python_version >= '3.7'
matplotlib==3.8.2 ; python_version >= '3.9'
mdurl==0.1.2 ; python_version >= '3.7'
model-index==0.1.11
mpmath==1.3.0
networkx==3.2.1 ; python_version >= '3.9'
numpy==1.26.2 ; python_version >= '3.9'
opencv-python==******** ; python_version >= '3.6'
opencv-python-headless==******** ; python_version >= '3.6'
opendatalab==0.0.10 ; python_version >= '3.7'
openmim==0.3.9
openxlab==0.0.31 ; python_version >= '3.8'
ordered-set==4.1.0 ; python_version >= '3.7'
oss2==2.17.0
packaging==23.2 ; python_version >= '3.7'
pandas==2.1.4 ; python_version >= '3.9'
pillow==10.1.0 ; python_version >= '3.8'
pip==23.3.1 ; python_version >= '3.7'
protobuf==4.25.1 ; sys_platform != 'linux'
psutil==5.9.6 ; python_version >= '2.7' and python_version not in '3.0, 3.1, 3.2, 3.3, 3.4, 3.5'
py-cpuinfo==9.0.0
pycocotools==2.0.7
pycparser==2.21
pycryptodome==3.19.0 ; python_version >= '2.7' and python_version not in '3.0, 3.1, 3.2, 3.3, 3.4'
pygments==2.17.2 ; python_version >= '3.7'
pyparsing==2.4.7 ; python_version >= '2.6' and python_version not in '3.0, 3.1, 3.2'
python-dateutil==2.8.2 ; python_version >= '2.7' and python_version not in '3.0, 3.1, 3.2'
python-dotenv==1.0.0 ; python_version >= '3.8'
python-magic==0.4.27 ; python_version >= '2.7' and python_version not in '3.0, 3.1, 3.2, 3.3, 3.4'
pytz==2023.3.post1
pyyaml==6.0.1 ; python_version >= '3.6'
qudida==0.0.4 ; python_full_version >= '3.5.0'
requests==2.28.2 ; python_version >= '3.7' and python_version < '4'
requests-toolbelt==1.0.0 ; python_version >= '2.7' and python_version not in '3.0, 3.1, 3.2, 3.3'
rich==13.4.2 ; python_full_version >= '3.7.0'
roboflow==1.1.12
safetensors==0.4.1 ; python_version >= '3.7'
scikit-image==0.22.0 ; python_version >= '3.9'
scikit-learn==1.3.2 ; python_version >= '3.8'
scipy==1.11.4 ; python_version >= '3.9'
seaborn==0.13.0 ; python_version >= '3.8'
sentry-sdk==1.38.0
setproctitle==1.3.3 ; python_version >= '3.7'
setuptools==60.2.0
six==1.16.0 ; python_version >= '2.7' and python_version not in '3.0, 3.1, 3.2'
smmap==5.0.1 ; python_version >= '3.7'
supervision==0.17.1 ; python_version >= '3.8' and python_version < '4.0'
sympy==1.12 ; python_version >= '3.8'
tabulate==0.9.0 ; python_version >= '3.7'
thop==0.1.1.post2209072238
threadpoolctl==3.2.0 ; python_version >= '3.8'
tifffile==2023.9.26 ; python_version >= '3.9'
timm==0.9.12
torch==2.1.1
torchsummary==1.5.1
torchvision==0.16.1
tqdm==4.65.2 ; python_version >= '3.7'
typing-extensions==4.8.0 ; python_version >= '3.8'
tzdata==2023.3 ; python_version >= '2'
ultralytics==8.0.225
urllib3==1.26.18 ; python_version >= '2.7' and python_version not in '3.0, 3.1, 3.2, 3.3, 3.4, 3.5'
wandb==0.16.1
dependency-injector
pytest
