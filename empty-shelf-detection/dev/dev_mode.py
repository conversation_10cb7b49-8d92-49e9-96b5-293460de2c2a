#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Development mode module for the empty shelf detection application.

This module provides functionality to speed up development by automating
common tasks such as starting the camera and enabling detection.
"""

# Import from core module
from core import get_config, get_logger
from PyQt5.QtCore import QPointF, Qt, QTimer

# Configure logger
logger = get_logger("dev_mode")

# Load configuration
config = get_config()


def apply_dev_mode(main_window_class):
    """
    Apply development mode to the MainWindow class.

    This function modifies the MainWindow class to add development mode
    functionality. It should be called before the MainWindow class is instantiated.

    Args:
        main_window_class: The MainWindow class from ROIGui.py
    """
    logger.info("Applying development mode")

    # Store original setup_startup_dialog method
    original_setup_startup_dialog = main_window_class.setup_startup_dialog

    # Define new setup_startup_dialog method
    def new_setup_startup_dialog(self, default_save_dir, default_prefdef_shelves_file):
        """Modified startup dialog method that bypasses the dialog."""
        # Get dev mode settings
        dev_config = config.get_section("dev_mode")
        bypass_dialog = dev_config.get("bypass_startup_dialog", True)
        auto_start_delay_ms = dev_config.get("auto_start_delay_ms", 1000)

        if not bypass_dialog:
            # If bypass_dialog is False, use the original method
            logger.info("Using original startup dialog")
            return original_setup_startup_dialog(self, default_save_dir, default_prefdef_shelves_file)

        # Otherwise, bypass the dialog
        logger.info("Bypassing startup dialog")
        self.annotation_mode = "camera"
        self.initialize_camera_mode(default_save_dir)

        # Load shelve data if method exists
        if hasattr(self, "load_shelve_data") and hasattr(self, "default_prefdef_shelves_file"):
            from utilities.utility_functions import load_shelve_data

            load_shelve_data(self, default_prefdef_shelves_file)

        self.setup_camera_mode_ui()

        # Auto-start camera
        logger.info(f"Scheduling auto-start camera in {auto_start_delay_ms} ms")
        QTimer.singleShot(auto_start_delay_ms, lambda: auto_start_camera(self))

        return

    # Define auto-start camera function
    def auto_start_camera(window):
        """Automatically start camera."""
        logger.info("Auto-starting camera")

        # Get dev mode settings
        dev_config = config.get_section("dev_mode")
        default_camera = dev_config.get("default_camera", "People Presence Demo")
        detection_delay_ms = dev_config.get("detection_delay_ms", 2000)

        # Start camera if method exists
        if hasattr(window, "start_camera") and hasattr(window, "dock_manager"):
            # Get the camera tree
            camera_tree = window.dock_manager.get_camera_tree()
            logger.info(f"Camera tree has {camera_tree.topLevelItemCount()} top-level items")

            # Search through all NVR nodes
            for i in range(camera_tree.topLevelItemCount()):
                nvr_item = camera_tree.topLevelItem(i)
                nvr_text = nvr_item.text(0)
                logger.info(f"Checking NVR: {nvr_text} with {nvr_item.childCount()} cameras")

                # Search through all cameras in this NVR
                for j in range(nvr_item.childCount()):
                    camera_item = nvr_item.child(j)
                    camera_text = camera_item.text(0)
                    camera_data = camera_item.data(0, Qt.UserRole)

                    # Check if this is the target camera
                    if isinstance(camera_data, dict):
                        camera_name = camera_data.get("name", "Unknown")
                        logger.info(f"Found camera: {camera_name} (text: {camera_text})")
                        if camera_name == default_camera:
                            logger.info(f"Found target camera: {camera_name}")
                            # We're not using this anymore, just log it
                            logger.info("Target camera found but using simplified approach instead")
                    else:
                        logger.warning(f"Camera data is not a dict for {camera_text}")

            # Try to find the football camera specifically
            football_camera_item = None

            if camera_tree.topLevelItemCount() > 0:
                nvr_item = camera_tree.topLevelItem(0)  # Get the first NVR (local_videos)
                logger.info(f"Using first NVR: {nvr_item.text(0)}")

                if nvr_item.childCount() > 0:
                    # Look for the football camera
                    for j in range(nvr_item.childCount()):
                        camera_item = nvr_item.child(j)
                        camera_text = camera_item.text(0)
                        logger.info(f"Checking camera: {camera_text}")

                        if camera_text == "football":
                            logger.info("Found football camera")
                            football_camera_item = camera_item
                            break

                    # If we found the football camera, select it
                    if football_camera_item:
                        logger.info("Selecting football camera")
                        camera_tree.setCurrentItem(football_camera_item)
                        window.on_camera_selected(football_camera_item)
                    else:
                        # Otherwise, use the third camera (football) in the first NVR
                        if nvr_item.childCount() >= 3:
                            camera_item = nvr_item.child(2)  # Index 2 is the third camera
                            camera_text = camera_item.text(0)
                            logger.info(f"Selecting third camera: {camera_text}")
                            camera_tree.setCurrentItem(camera_item)
                            window.on_camera_selected(camera_item)
                        else:
                            logger.warning("Not enough cameras in the first NVR")
                            window.start_camera()
                else:
                    logger.warning("No cameras found in the first NVR")
                    window.start_camera()
            else:
                logger.warning("No NVRs found in the camera tree")
                window.start_camera()

            # Auto-enable detection
            logger.info(f"Scheduling auto-enable detection in {detection_delay_ms} ms")
            QTimer.singleShot(detection_delay_ms, lambda: auto_enable_detection(window))
        else:
            logger.error("Could not start camera - required methods not found")

    # Define auto-enable detection function
    def auto_enable_detection(window):
        """Automatically enable detection."""
        logger.info("Auto-enabling detection")

        # Get dev mode settings from the dev_mode section
        dev_config = config.get_section("dev_mode")
        auto_enable_detection_setting = dev_config.get("auto_enable_detection", True)
        create_annotation = dev_config.get("create_annotation", False)  # Default to False to prevent creating annotations

        if not auto_enable_detection_setting:
            logger.info("Auto-enable detection is disabled in config")
            return

        # Check if canvas has a pixmap
        if not hasattr(window, "canvas") or not hasattr(window.canvas, "pixmap") or not window.canvas.pixmap or window.canvas.pixmap.isNull():
            logger.warning("No image loaded in canvas yet, waiting...")
            # Try again after a delay
            QTimer.singleShot(1000, lambda: auto_enable_detection(window))
            return

        # Check if any presence check ROI already exists
        has_presence_roi = any(shape.roi_type == "presence_check" for shape in window.canvas.shapes)

        # Create presence check ROI if none exists and create_annotation is True
        if not has_presence_roi and create_annotation:
            logger.info("No presence check ROI found, creating one automatically")
            create_roi(window)
        elif has_presence_roi:
            logger.info("Presence check ROI already exists, using existing ROI")
        else:
            logger.info("No presence check ROI found, but create_annotation is False, skipping ROI creation")

        # Set to presence check mode
        window._is_presence_check_mode = True

        # Run model if method exists and we have ROIs
        if hasattr(window, "run_model") and (has_presence_roi or create_annotation):
            logger.info("Starting model processing")
            # Update the model path in CameraManager
            window.run_model()
        else:
            if not (has_presence_roi or create_annotation):
                logger.warning("No ROIs available and not creating new ones - skipping model processing")
            else:
                logger.error("Could not run model - run_model method not found")

    # Define create ROI function
    def create_roi(window):
        """Create ROI based on configuration."""
        logger.info("Creating presence_check ROI")

        # Import Shape class
        from libs.shape import Shape

        # Get canvas dimensions
        width, height = window.canvas.pixmap.width(), window.canvas.pixmap.height()
        logger.info(f"Canvas dimensions: {width}x{height}")

        # Use fixed dimensions if canvas is too small
        if width < 100 or height < 100:
            logger.warning("Canvas dimensions too small, using default values")
            width, height = 640, 480

        # Get ROI configuration from AppConfig
        roi_config = config.get_section("dev_mode").get("roi", {})
        width_percent = roi_config.get("width_percent", 0.5)  # Smaller width (was 0.8)
        height_percent = roi_config.get("height_percent", 0.5)  # Smaller height (was 0.8)
        x_percent = roi_config.get("x_percent", 0.25)  # Centered horizontally (was 0.1)
        y_percent = roi_config.get("y_percent", 0.25)  # Centered vertically (was 0.1)
        roi_label = roi_config.get("label", "Auto Presence Check")
        roi_type = roi_config.get("roi_type", "presence_check")

        # Calculate ROI coordinates
        x1 = width * x_percent
        y1 = height * y_percent
        x2 = width * (x_percent + width_percent)
        y2 = height * (y_percent + height_percent)

        # Create points for the rectangle
        points = [
            QPointF(x1, y1),  # Top-left
            QPointF(x2, y1),  # Top-right
            QPointF(x2, y2),  # Bottom-right
            QPointF(x1, y2),  # Bottom-left
        ]

        # Create the shape
        shape = Shape(label=roi_label, roi_type=roi_type)
        for point in points:
            shape.add_point(point)
        shape.close()

        # Add shape to canvas
        window.canvas.shapes.append(shape)

        # Add the shape to the label list
        if hasattr(window, "add_label"):
            window.add_label(shape)

        # Update canvas
        window.canvas.update()

        # Set roi_annotations for the current camera thread
        if (
            hasattr(window, "camera_manager")
            and window.camera_manager
            and hasattr(window.camera_manager, "current_camera_thread")
            and window.camera_manager.current_camera_thread
        ):
            camera_thread = window.camera_manager.current_camera_thread
            logger.info(f"Setting roi_annotations for camera {camera_thread.camera_name}")
            camera_thread.roi_annotations = True

    # Replace setup_startup_dialog method
    main_window_class.setup_startup_dialog = new_setup_startup_dialog
