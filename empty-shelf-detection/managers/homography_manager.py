import numpy as np
import cv2


class HomographyManager:
    def __init__(self):
        self.shelve_name = None
        self.homography_matrix = None
        self.homography_points = []

    def set_shelve_name(self, shelve_name):
        self.shelve_name = shelve_name

    def compute_homography(self, image_points, shelve_data):
        """Compute homography given image points and shelve data."""
        # image_points: list of (x, y) in order: sol üst, sol alt, sağ üst, sağ alt
        # shelve_data: {'width_cm': ..., 'height_cm': ...}

        width_cm = shelve_data["width_cm"]
        height_cm = shelve_data["height_cm"]

        # Real-world points (sol üst, sol alt, sağ üst, sağ alt):
        real_world_points = np.array([[width_cm, height_cm], [width_cm, 0], [0, height_cm], [0, 0]], dtype=np.float32)

        img_pts = np.array(image_points, dtype=np.float32)

        H, _ = cv2.findHomography(img_pts, real_world_points)

        print()
        if H is not None:
            self.homography_matrix = H
            return True
        return False

    def to_dict(self):
        """Serialize homography data to a dictionary for annotation saving."""
        return {
            "shelve_name": self.shelve_name,
            "homography": self.homography_matrix.tolist() if self.homography_matrix is not None else None,
            "homography_points": [{"label": lp[0], "point": lp[1]} for lp in self.homography_points],
        }

    def from_dict(self, data):
        """Load homography data from a dictionary."""
        self.shelve_name = data.get("shelve_name")
        homography_list = data.get("homography")
        if homography_list:
            self.homography_matrix = np.array(homography_list, dtype=np.float32)
        else:
            self.homography_matrix = None
        self.homography_points = [(d["label"], tuple(d["point"])) for d in data.get("homography_points", [])]
