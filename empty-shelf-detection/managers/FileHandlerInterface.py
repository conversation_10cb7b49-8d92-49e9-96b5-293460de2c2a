from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

from PyQt5.QtGui import QImage


class FileHandlerInterface(ABC):
    def __init__(self):
        self.label_file_format = None

    @abstractmethod
    def set_format(self, format_type: str) -> Optional[Dict[str, str]]:
        """Set the file format and return appropriate settings"""
        pass

    @abstractmethod
    def load_file_data(self, file_path):
        """Set the file format and return appropriate settings"""
        pass

    @abstractmethod
    def load_pascal_xml(self, xml_path: str, image: QImage) -> Optional[Dict[str, Any]]:
        """Load Pascal VOC format annotations"""
        pass

    @abstractmethod
    def load_yolo_txt(self, txt_path: str, image: QImage) -> Optional[Dict[str, Any]]:
        """Load YOLO format annotations"""
        pass

    @abstractmethod
    def load_create_ml_json(self, json_path: str, file_path: str) -> Optional[Dict[str, Any]]:
        """Load CreateML format annotations"""
        pass

    @abstractmethod
    def load_annotations(self, annotation_path, image_path, image):
        pass

    @abstractmethod
    def save_annotations(self, annotation_file_path, shapes, file_path, image_data, param, param1, label_hist, verified):
        pass

    @abstractmethod
    def change_format(self):
        pass
