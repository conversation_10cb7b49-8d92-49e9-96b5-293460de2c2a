import os

from libs.constants import *
from libs.create_ml_io import <PERSON><PERSON><PERSON>_EXT, Create<PERSON>Reader
from libs.labelFile import LabelFile, LabelFileError, LabelFileFormat
from libs.pascal_voc_io import XML_EXT, <PERSON><PERSON>ocReader
from libs.yolo_io import TXT_EXT, Yo<PERSON><PERSON>eader
from PyQt5.QtGui import QImage
from utilities.utility_functions import read


class FileHandler:
    def __init__(self):
        self.label_file_format = LabelFileFormat.PASCAL_VOC
        self.label_file = LabelFile()

    def set_format(self, format_type):
        """Set the file format and return appropriate settings"""
        if format_type == FORMAT_PASCALVOC:
            self.label_file_format = LabelFileFormat.PASCAL_VOC
            return {"ext": XML_EXT, "format": FORMAT_PASCALVOC, "icon": "format_voc"}
        elif format_type == FORMAT_YOLO:
            self.label_file_format = LabelFileFormat.YOLO
            return {"ext": TXT_EXT, "format": FORMAT_YOLO, "icon": "format_yolo"}
        elif format_type == FORMAT_CREATEML:
            self.label_file_format = LabelFileFormat.CREATE_ML
            return {"ext": JSON_EXT, "format": FORMAT_CREATEML, "icon": "format_createml"}

    def load_annotations(self, annotation_path, image_path=None, image=None):
        """Load annotations based on the file extension"""
        extension = os.path.splitext(annotation_path)[1].lower()
        if extension == XML_EXT:
            return self.load_pascal_xml(annotation_path)
        elif extension == TXT_EXT:
            return self.load_yolo_txt(annotation_path, image)
        elif extension == JSON_EXT:
            return self.load_create_ml_json(annotation_path, image_path)
        else:
            return None

    def load_pascal_xml(self, xml_path):
        """Load Pascal VOC format annotations"""
        if not os.path.isfile(xml_path):
            return None

        reader = PascalVocReader(xml_path)
        shapes = reader.get_shapes()

        return {"shapes": shapes, "verified": reader.verified}  # Already Shape objects from PascalVocReader

    def load_yolo_txt(self, txt_path, image):
        """Load YOLO format annotations"""
        if not os.path.isfile(txt_path):
            return None

        reader = YoloReader(txt_path, image)

        # Convert to Shape objects if needed
        shapes = []
        for shape in reader.get_shapes():
            try:
                shapes.append(shape)
            except TypeError:
                # Skip invalid shapes
                continue

        return {"shapes": shapes, "verified": reader.verified}

    def load_create_ml_json(self, json_path, image_path):
        """Load CreateML format annotations"""
        if not os.path.isfile(json_path):
            return None

        reader = CreateMLReader(json_path, image_path)

        # Convert to Shape objects if needed
        shapes = []
        for shape in reader.get_shapes():
            try:
                shapes.append(shape)
            except TypeError:
                # Skip invalid shapes
                continue

        return {"shapes": shapes, "verified": reader.verified}

    def save_annotations(self, annotation_file_path, shapes_data, image_path, image_data, line_color, fill_color, label_hist, verified=False):
        try:
            self.label_file.verified = verified

            if self.label_file_format == LabelFileFormat.PASCAL_VOC:
                if not annotation_file_path.endswith(XML_EXT):
                    annotation_file_path += XML_EXT
                self.label_file.save_pascal_voc_format(annotation_file_path, shapes_data, image_path, image_data, line_color, fill_color)
            elif self.label_file_format == LabelFileFormat.YOLO:
                if not annotation_file_path.endswith(TXT_EXT):
                    annotation_file_path += TXT_EXT
                self.label_file.save_yolo_format(annotation_file_path, shapes_data, image_path, image_data, label_hist, line_color, fill_color, verified)
            elif self.label_file_format == LabelFileFormat.CREATE_ML:
                if not annotation_file_path.endswith(JSON_EXT):
                    annotation_file_path += JSON_EXT
                self.label_file.save_create_ml_format(annotation_file_path, shapes_data, image_path, image_data, label_hist, line_color, fill_color, verified)
            else:
                # Default save method uses Pascal VOC format
                if not annotation_file_path.endswith(XML_EXT):
                    annotation_file_path += XML_EXT
                self.label_file.save_pascal_voc_format(annotation_file_path, shapes_data, image_path, image_data, line_color, fill_color)
            return True
        except LabelFileError as e:
            print("Error saving label data:", e)
            return False

    def get_file_suffix(self):
        """Return the current file format suffix."""
        if self.label_file_format == LabelFileFormat.PASCAL_VOC:
            return XML_EXT
        elif self.label_file_format == LabelFileFormat.YOLO:
            return TXT_EXT
        elif self.label_file_format == LabelFileFormat.CREATE_ML:
            return JSON_EXT
        else:
            return XML_EXT  # Default to XML

    def is_label_file(self, filename):
        """Check if the given file is a label file."""
        file_suffix = os.path.splitext(filename)[1].lower()
        return file_suffix in [XML_EXT, TXT_EXT, JSON_EXT]

    def load_label_file(self):
        """Load a label file and return it."""
        try:
            label_file = LabelFile()
            return label_file
        except LabelFileError as e:
            raise Exception(f"Error loading label file: {e}")

    def change_format(self):
        """Cycle through available file formats (PASCAL_VOC -> YOLO -> CREATE_ML -> PASCAL_VOC)"""
        if self.label_file_format == LabelFileFormat.PASCAL_VOC:
            return self.set_format(FORMAT_YOLO)
        elif self.label_file_format == LabelFileFormat.YOLO:
            return self.set_format(FORMAT_CREATEML)
        elif self.label_file_format == LabelFileFormat.CREATE_ML:
            return self.set_format(FORMAT_PASCALVOC)
        else:
            # Default to PASCAL_VOC if unknown format
            return self.set_format(FORMAT_PASCALVOC)

    def load_file_data(self, file_path):
        """Load file data from a path and return relevant information.

        Returns:
            dict: Dictionary containing:
                success (bool): Whether loading was successful
                error_msg (str): Error message if loading failed
                image_data: Raw image data
                image: QImage object
                label_file: Label file object or None
                is_verified (bool): Whether the file is verified
        """
        result = {"success": False, "error_msg": "", "image_data": None, "image": None, "label_file": None, "is_verified": False}

        if not file_path or not os.path.exists(file_path):
            result["error_msg"] = f"File {file_path} does not exist"
            return result

        if self.is_label_file(file_path):
            try:
                label_file = self.load_label_file(file_path)
                result["label_file"] = label_file
                result["image_data"] = label_file.image_data
                result["is_verified"] = label_file.verified
            except Exception as e:
                result["error_msg"] = f"Error opening label file: {e}"
                return result
        else:
            try:
                result["image_data"] = read(file_path, None)
            except Exception as e:
                result["error_msg"] = f"Error reading image file: {e}"
                return result

        # Convert image data to QImage
        if isinstance(result["image_data"], QImage):
            result["image"] = result["image_data"]
        else:
            result["image"] = QImage.fromData(result["image_data"])

        if result["image"].isNull():
            result["error_msg"] = f"Invalid image file: {file_path}"
            return result

        result["success"] = True
        return result

    def get_save_file_dialog_options(self, current_path, filename_without_ext):
        """Return options for a save file dialog.

        Args:
            current_path: Current directory path
            filename_without_ext: Current filename without extension

        Returns:
            dict: Dictionary with dialog options
        """
        suffix = self.get_file_suffix()
        return {
            "filters": f"File (*{suffix})",
            "default_suffix": suffix[1:],  # Remove the dot
            "directory": current_path,
            "default_filename": filename_without_ext,
        }

    def get_open_file_dialog_options(self, current_path, formats):
        """Return options for an open file dialog.

        Args:
            current_path: Current directory path
            formats: List of supported image formats

        Returns:
            dict: Dictionary with dialog options
        """
        suffix = self.get_file_suffix()
        filters = "Image & Label files (%s)" % " ".join(formats + [f"*{suffix}"])
        return {"filters": filters, "directory": current_path}
