"""
Manages camera feeds, model processing, and annotation loading.
Provides centralized control for camera operations.
"""

import os
from typing import Optional

from core import get_config, get_logger, get_model_manager
from helpers.LoopingVideoThread import LoopingVideoThread
from helpers.ProcessCamera import VideoThread
from libs.create_ml_io import JSON_EXT
from libs.pascal_voc_io import XML_EXT
from libs.yolo_io import TXT_EXT


class CameraManager:
    def __init__(self, main_window):
        self.current_camera_thread: Optional[VideoThread] = None  # Added type hint
        self.main_window = main_window
        self.detection_mode_manager = main_window.detection_mode_manager  # Get the instance from main window
        self.paused = False
        self.camera_settings = main_window.settings.get("camera_settings", {"width": 1920, "height": 1080, "frame_rate": 15})

        # Initialize image-based model processing attributes
        self.image_model_processing = False
        self.logger = get_logger(self.__class__.__name__)

        # Get config instance
        self.config = get_config()
        self.model_manager = get_model_manager()
        self.current_model_id = "empty_shelf"  # Default model ID

    def initialize_camera_and_load_annotations(self, camera_name, rtsp_url):
        """
        Initializes the camera feed and loads annotations based on the camera name.
        """
        if rtsp_url:
            # Create a camera-specific error handler that includes the camera_name
            def camera_error_handler(error_message):
                self.handle_camera_error(camera_name, error_message)

            self.current_camera_thread = self.load_camera(rtsp_url, camera_name, self.main_window, camera_error_handler)

        else:
            self.handle_camera_error(camera_name, f"No URL found for the selected camera: {camera_name}")

    def is_model_processing(self):
        return self.current_camera_thread and self.current_camera_thread.model_processing

    def start_model_processing(self):
        """Starts model processing on the current camera thread using centralized trackers."""

        camera_name = self.current_camera_thread.camera_name

        # Check if we have any registered trackers for this camera
        tracker_registry = self.detection_mode_manager.get_all_trackers()
        has_trackers = False

        for tracker_type, trackers in tracker_registry.items():
            if camera_name in trackers:
                has_trackers = True
                break

        if not has_trackers:
            self.logger.warning(f"No trackers registered for camera {camera_name}")
            return False

        # Set model processing flag on camera thread
        self.current_camera_thread.set_model_processing(True)
        self.logger.info(f"Started model processing for {camera_name} using centralized tracker registry")
        return True

    def stop_model_processing(self):
        if self.current_camera_thread:
            self.current_camera_thread.set_model_processing(False)  # Signal thread to stop processing
            self.logger.info(f"Stopping model processing for {self.current_camera_thread.camera_name}")
        else:
            self.main_window.status("Cannot stop model processing: No active camera thread.")

    def load_annotations(self, camera_name, image_path=None):
        """Loads annotations specific to a camera."""
        annotation_file_base = os.path.join(self.main_window.camera_save_dir or "", camera_name)

        # Determine the file format and set annotation file accordingly
        label_format = self.main_window.label_file_format
        self.main_window.set_format(label_format)

        # Try loading annotations from possible formats
        for ext in [XML_EXT, TXT_EXT, JSON_EXT]:
            annotation_file = f"{annotation_file_base}{ext}"
            if os.path.exists(annotation_file):
                self.main_window.load_annotations(annotation_file, image_path)
                self.main_window.status(f"Loaded annotations for camera: {camera_name}")
                break
        else:
            self.main_window.status(f"No existing annotations found for camera: {camera_name}")

    def handle_camera_error(self, camera_name, error_message):
        """
        Handles the camera error event and stops the camera thread.
        This method is now primarily for logging and stopping the thread internally.
        The signal emitted by VideoThread should trigger UI updates in ROIGui.
        """
        self.logger.error(f"Camera error for {camera_name}: {error_message}")
        # The actual QMessageBox is shown by ROIGui when it receives the camera_error signal
        self.stop_current_camera()
        self.logger.info(f"Camera {camera_name} could not be started: {error_message}")

    def load_camera(self, rtsp_url, camera_name, main_window, handle_error_callback):
        """Initializes a camera feed thread. Signal connection happens in ROIGui."""
        # Create a new video thread to handle camera feed
        if rtsp_url.startswith("file://"):
            # Use LoopingVideoThread for local video files
            thread = LoopingVideoThread(rtsp_url, camera_name, main_window)
            self.logger.info(f"Using LoopingVideoThread for local video file: {rtsp_url}")
        else:
            # Use regular VideoThread for RTSP streams
            thread = VideoThread(rtsp_url, camera_name, main_window)
        self.logger.info(f"Initialized camera thread {camera_name}")

        # Do not set tracker here; it will be set when run_model is called.
        self.current_tracker = None  # Ensure tracker is None initially

        # Connect error handling
        thread.camera_error.connect(handle_error_callback)

        thread.start()
        return thread  # Return the thread object so ROIGui can connect signals

    def stop_current_camera(self):
        """Stops the current camera thread safely if it's running."""
        if self.current_camera_thread:
            self.logger.info(f"Stopping camera thread for {self.current_camera_thread.camera_name}")
            self.stop_model_processing()  # Ensure model processing is stopped first
            self.current_camera_thread.stop()  # Tell the thread to stop
            self.current_camera_thread.wait()  # Wait for the thread to finish
            self.current_camera_thread = None
            self.logger.info("Camera thread stopped.")
        else:
            self.logger.debug("Stop camera requested, but no thread was running.")

    def pause(self):
        """Pauses the current camera feed."""
        if self.current_camera_thread and not self.paused:
            self.current_camera_thread.pause()
            self.paused = True
            self.logger.info("Camera feed paused.")

    def resume(self):
        """Resumes the current camera feed."""
        if self.current_camera_thread and self.paused:
            self.current_camera_thread.resume()
            self.paused = False
            self.logger.info("Camera feed resumed.")

    def is_running(self):
        """Checks if the camera thread is running."""
        return self.current_camera_thread is not None and not self.paused

    def is_paused(self):
        """Checks if the camera feed is paused."""
        return self.paused

    def get_settings(self):
        """Returns the current camera settings."""
        return self.camera_settings

    def apply_settings(self, new_settings):
        """Applies new camera settings and saves them to MainWindow settings."""
        self.camera_settings.update(new_settings)
        # Update settings in MainWindow's settings manager
        self.main_window.settings.set("camera_settings", self.camera_settings)

        if self.current_camera_thread:
            self.stop_current_camera()
            # Restart the camera with new settings
            self.initialize_camera_and_load_annotations(self.current_camera_thread.camera_name, self.current_camera_thread.rtsp_url)

    def initialize_trackers(self):
        """
        Initialize all trackers in the central registry.
        This method is called during startup to ensure all trackers are properly initialized.
        """
        self.logger.info("Initializing trackers from central registry")

        # Get the DetectionModeManager instance
        detection_mode_manager = self.detection_mode_manager

        # Get all trackers from the central registry
        all_trackers = detection_mode_manager.get_all_trackers()

        # Log the number of trackers found
        total_trackers = sum(len(trackers) for trackers in all_trackers.values())
        self.logger.info(f"Found {total_trackers} trackers in central registry")

        # Initialize each tracker if it has an initialize method
        for tracker_type, trackers in all_trackers.items():
            for camera_name, tracker in trackers.items():
                if hasattr(tracker, "initialize"):
                    try:
                        tracker.initialize()
                        self.logger.debug(f"Initialized {tracker_type} tracker for camera {camera_name}")
                    except Exception as e:
                        self.logger.error(f"Error initializing {tracker_type} tracker for camera {camera_name}: {e}")

        self.logger.info("Tracker initialization complete")
