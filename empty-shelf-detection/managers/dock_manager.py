from core.config import CoreConfig
from libs.combobox import <PERSON>mboBox
from libs.default_label_combobox import De<PERSON>ultLabelComboBox
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtWidgets import (
    QCheckBox,
    QDockWidget,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QListWidget,
    QListWidgetItem,
    QToolButton,
    QTreeWidget,
    QTreeWidgetItem,
    QVBoxLayout,
    QWidget,
)


class DockManager:
    def __init__(self, main_window):
        self.camera_search = None
        self.main_window = main_window
        self.dock_features = QDockWidget.DockWidgetClosable | QDockWidget.DockWidgetFloatable
        self.docks = {}
        self.dock_widgets = {}
        self.list_widget = None
        self.camera_tree = None
        self.label_list = None

    def setup_dock_areas(self, mode="file"):
        """Setup dock areas based on selected mode"""
        # Always setup the label list for both modes

        # Setup file list or camera tree based on mode
        if mode == "file":
            self.setup_file_list()
            if "camera" in self.docks:
                self.docks["camera"].setVisible(False)
        elif mode == "camera":
            self.setup_camera_tree()
            if "file" in self.docks:
                self.docks["file"].setVisible(False)

        self.setup_label_list()
        # Apply dock features to all docks
        self.set_dock_features()

    def setup_file_list(self):
        """Setup the file list dock for file mode"""
        # If file dock already exists, make it visible and return
        if "file" in self.docks:
            self.docks["file"].setVisible(True)
            return

        self.list_widget = QListWidget()
        self.list_widget.itemDoubleClicked.connect(self.main_window.file_item_double_clicked)

        file_list_layout = QVBoxLayout()
        file_list_layout.setContentsMargins(0, 0, 0, 0)
        file_list_layout.addWidget(self.list_widget)
        file_list_container = QWidget()
        file_list_container.setLayout(file_list_layout)

        self.file_dock = QDockWidget(self.main_window.get_str("fileList"), self.main_window)
        self.file_dock.setObjectName(self.main_window.get_str("files"))
        self.file_dock.setWidget(file_list_container)
        self.main_window.addDockWidget(Qt.RightDockWidgetArea, self.file_dock)

        # Store dock and its main widget
        self.docks["file"] = self.file_dock
        self.dock_widgets["file"] = self.list_widget

    def setup_camera_tree(self):
        """Setup the camera tree dock for camera mode"""
        # If camera dock already exists, make it visible and return
        if "camera" in self.docks:
            self.docks["camera"].setVisible(True)
            return

        # Create a tree widget for the camera hierarchy
        self.camera_tree = QTreeWidget()
        self.camera_tree.setHeaderLabels(["Cameras"])
        self.camera_tree.setColumnCount(1)
        self.camera_tree.setUpdatesEnabled(True)  # Ensure updates are enabled
        self.camera_tree.setAnimated(True)  # Enable animations for smoother updates
        self.camera_tree.setSortingEnabled(True)  # Enable sorting
        self.camera_tree.setAlternatingRowColors(True)  # Make it easier to read

        # Get camera tree data from config cameras_all
        config = CoreConfig.get_instance()
        camera_tree_data = config.get_camera_map()

        # Store the original data for filtering
        self.original_camera_data = []

        # Populate the tree with NVRs and their cameras
        for nvr_ip, nvr_data in camera_tree_data.get("nvrs", {}).items():

            nvr_item = QTreeWidgetItem([f"NVR: {nvr_ip}"])
            nvr_item.setData(0, Qt.UserRole, nvr_ip)  # Store the IP on the item
            nvr_item.setFlags(nvr_item.flags() | Qt.ItemIsEnabled)  # Ensure item is enabled
            self.camera_tree.addTopLevelItem(nvr_item)

            for camera in nvr_data.get("cameras", []):
                if camera.get("rtsp_url"):  # Only add cameras with valid RTSP URLs
                    camera_item = QTreeWidgetItem([camera["name"]])
                    camera_item.setFlags(camera_item.flags() | Qt.ItemIsEnabled | Qt.ItemIsSelectable)
                    camera_item.setData(0, Qt.UserRole, {"rtsp_url": camera["rtsp_url"], "name": camera["name"], "nvr_ip": nvr_ip})
                    nvr_item.addChild(camera_item)
                    # Store original data for filtering
                    self.original_camera_data.append({"nvr": nvr_ip, "camera": camera["name"], "item": camera_item, "parent": nvr_item})

        # Expand all NVR nodes by default for better visibility
        self.camera_tree.expandAll()

        # Connect double-click handler
        self.camera_tree.itemDoubleClicked.connect(self.main_window.on_camera_selected)

        # Create layout and container
        camera_tree_layout = QVBoxLayout()
        camera_tree_layout.setContentsMargins(0, 0, 0, 0)
        camera_tree_layout.addWidget(self.camera_tree)
        camera_tree_container = QWidget()
        camera_tree_container.setLayout(camera_tree_layout)

        # Create dock widget
        self.camera_dock = QDockWidget(self.main_window.get_str("cameraList"), self.main_window)
        self.camera_dock.setObjectName(self.main_window.get_str("cameraList"))

        # Create search box in title bar widget
        from PyQt5.QtGui import QColor, QPalette

        title_bar = QWidget()
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(5, 0, 5, 0)
        title_label = QLabel(self.main_window.get_str("cameraList"))

        # Create and style the search box
        self.camera_search = QLineEdit()
        self.camera_search.setPlaceholderText("Kamera Ara")
        self.camera_search.setMinimumWidth(230)

        # Set yellow background for search box
        palette = self.camera_search.palette()
        palette.setColor(QPalette.Base, QColor(255, 255, 224))  # Light yellow
        self.camera_search.setPalette(palette)

        # Set style sheet for better appearance
        self.camera_search.setStyleSheet(
            """
            QLineEdit {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 3px;
                font-size: 12px;
                background-color: #FFFACD;  /* Light goldenrod yellow */
            }
            QLineEdit:focus {
                border: 1px solid #4a90e2;
                background-color: #FFFFE0;  /* Light yellow */
            }
        """
        )

        ## TODO: fix Cannot find reference 'connect' in 'pyqtSignal | function'

        self.camera_search.textChanged.connect(self.filter_camera_tree)

        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(self.camera_search)

        # Set custom title bar widget
        self.camera_dock.setTitleBarWidget(title_bar)

        # Set the container as the dock widget's main widget
        self.camera_dock.setWidget(camera_tree_container)
        self.main_window.addDockWidget(Qt.RightDockWidgetArea, self.camera_dock)

        # Set initial size for camera dock - make it taller
        self.camera_dock.setMinimumHeight(550)
        self.camera_dock.resize(self.camera_dock.width(), 550)

        # Store dock and its main widget
        self.docks["camera"] = self.camera_dock
        self.dock_widgets["camera"] = self.camera_tree

        # Force initial update
        self.camera_tree.update()
        self.camera_dock.update()

    def setup_label_list(self):
        """Setup the label list dock for both modes"""
        if "label" in self.docks:
            self.docks["label"].setVisible(True)
            return

        # Create checkbox and combo box for default label
        self.use_default_label_checkbox = QCheckBox(self.main_window.get_str("useDefaultLabel"))
        self.use_default_label_checkbox.setChecked(False)
        self.default_label_combo_box = DefaultLabelComboBox(self.main_window, items=self.main_window.label_hist)

        # Create layout for default label controls
        use_default_label_qhbox_layout = QHBoxLayout()
        use_default_label_qhbox_layout.addWidget(self.use_default_label_checkbox)
        use_default_label_qhbox_layout.addWidget(self.default_label_combo_box)

        use_default_label_container = QWidget()
        use_default_label_container.setLayout(use_default_label_qhbox_layout)

        # Create edit button and difficulty checkbox
        self.edit_button = QToolButton()
        self.edit_button.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        self.diffc_button = QCheckBox(self.main_window.get_str("useDifficult"))
        self.diffc_button.setChecked(False)
        self.diffc_button.stateChanged.connect(self.main_window.button_state)

        # Create main layout
        list_layout = QVBoxLayout()
        list_layout.setContentsMargins(0, 0, 0, 0)
        list_layout.addWidget(self.edit_button)
        list_layout.addWidget(self.diffc_button)
        list_layout.addWidget(use_default_label_container)

        self.combo_box = ComboBox(self.main_window)
        list_layout.addWidget(self.combo_box)

        self.label_list = QListWidget()
        label_list_container = QWidget()
        label_list_container.setLayout(list_layout)
        self.label_list.itemActivated.connect(self.main_window.label_selection_changed)
        self.label_list.itemSelectionChanged.connect(self.main_window.label_selection_changed)
        self.label_list.itemDoubleClicked.connect(self.main_window.edit_label)
        self.label_list.itemChanged.connect(self.main_window.label_item_changed)
        # Add label list to layout
        list_layout.addWidget(self.label_list)

        self.label_dock = QDockWidget(self.main_window.get_str("boxLabelText"), self.main_window)
        self.label_dock.setObjectName(self.main_window.get_str("labels"))
        self.label_dock.setWidget(label_list_container)
        self.main_window.addDockWidget(Qt.RightDockWidgetArea, self.label_dock)

        self.label_dock.setMinimumHeight(350)
        self.label_dock.resize(self.label_dock.width(), 350)

        # Store dock and its main widget
        self.docks["label"] = self.label_dock
        self.dock_widgets["label"] = self.label_list

    def toggle_dock_visibility(self, mode="file"):
        """Toggle visibility of docks based on mode"""
        if mode == "file":
            if "file" in self.docks:
                self.docks["file"].setVisible(True)
            if "camera" in self.docks:
                self.docks["camera"].setVisible(False)
        elif mode == "camera":
            if "file" in self.docks:
                self.docks["file"].setVisible(False)
            if "camera" in self.docks:
                self.docks["camera"].setVisible(True)

        # The label dock is always visible
        if "label" in self.docks:
            self.docks["label"].setVisible(True)

    def set_dock_features(self):
        """Set dock features for all docks"""
        for dock in self.docks.values():
            dock.setFeatures(dock.features() ^ self.dock_features)

    def update_file_list(self, img_list):
        """Update the file list with new images"""
        if not self.list_widget:
            return

        self.list_widget.clear()
        for img_path in img_list:
            item = QListWidgetItem(img_path)
            self.list_widget.addItem(item)

    def highlight_file_in_list(self, file_path):
        """Highlight a specific file in the file list"""
        if not self.list_widget or not file_path:
            return

        for i in range(self.list_widget.count()):
            if self.list_widget.item(i).text() == file_path:
                self.list_widget.setCurrentItem(self.list_widget.item(i))
                break

    def update_label_list(self, shapes):
        """Update the label list with new shapes"""
        if not self.label_list:
            return

        self.label_list.clear()
        for shape in shapes:
            item = self.main_window.create_label_list_item(shape)
            self.label_list.addItem(item)

        # Update the combo box with new label items
        self.update_combo_box()

    def update_combo_box(self):
        """Update the combo box with unique label names"""
        if not self.label_list or not self.combo_box:
            return

        items_text_list = [str(self.label_list.item(i).text()) for i in range(self.label_list.count())]
        unique_text_list = list(set(items_text_list))
        unique_text_list.append("")
        unique_text_list.sort()
        self.combo_box.update_items(unique_text_list)

    def get_current_label_item(self):
        """Get the currently selected label item"""
        if not self.label_list:
            return None

        items = self.label_list.selectedItems()
        if items:
            return items[0]
        return None

    def get_camera_tree(self):
        """Get the camera tree widget"""
        return self.camera_tree

    def get_file_list(self):
        """Get the file list widget"""
        return self.list_widget

    def filter_camera_tree(self, text):
        """Filter the camera tree based on search text"""

        if not self.camera_tree or not hasattr(self, "original_camera_data"):
            return

        text = text.lower().strip()

        # First, hide all items
        for i in range(self.camera_tree.topLevelItemCount()):
            nvr_item = self.camera_tree.topLevelItem(i)
            nvr_item.setHidden(True)
            for j in range(nvr_item.childCount()):
                camera_item = nvr_item.child(j)
                camera_item.setHidden(True)

        if not text:
            # Show all items if search is empty
            for i in range(self.camera_tree.topLevelItemCount()):
                nvr_item = self.camera_tree.topLevelItem(i)
                nvr_item.setHidden(False)
                for j in range(nvr_item.childCount()):
                    camera_item = nvr_item.child(j)
                    camera_item.setHidden(False)
                nvr_item.setExpanded(True)
            self.camera_tree.update()
            return

        # Track which NVRs have visible children using their IPs
        visible_nvr_ips = set()

        # Filter based on the search text
        for data in self.original_camera_data:
            camera_name = data["camera"].lower()
            nvr_name = data["nvr"].lower()

            # Check if either camera name or NVR name contains the search text
            if text in camera_name or text in nvr_name:
                data["item"].setHidden(False)
                data["parent"].setHidden(False)
                data["parent"].setExpanded(True)
                visible_nvr_ips.add(data["nvr"])  # Add the NVR IP, not the item

        # Update NVR visibility
        for i in range(self.camera_tree.topLevelItemCount()):
            nvr_item = self.camera_tree.topLevelItem(i)
            nvr_ip = nvr_item.data(0, Qt.UserRole)  # get the IP
            if nvr_ip not in visible_nvr_ips:
                nvr_item.setHidden(True)
            else:
                nvr_item.setHidden(False)

        # Force the tree to update its display
        self.camera_tree.update()
        self.camera_tree.repaint()

        # Ensure the dock widget updates as well
        if "camera" in self.docks:
            self.docks["camera"].update()
            self.docks["camera"].repaint()
