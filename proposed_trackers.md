# Model Scheduling for Retail Applications

## Introduction

This document outlines a practical approach to scheduling deep learning models in retail environments based on a system with three primary models. By implementing intelligent scheduling strategies, retailers can optimize resource usage, improve monitoring effectiveness, and address specific business needs at different times of the day.

## Current System Models

The current system consists of three primary models:

### 1. Object Detection Model

**Purpose**: Detect and classify objects in the retail environment
**Implementation**: YOLOv8/YOLOv10
**Applications**:
- Product detection on shelves
- Customer detection and counting
- Queue monitoring - TODO:Planned
- Staff presence detection

### 2. Segmentation Model -  TODO:Planned

**Purpose**: Perform pixel-level classification of images
**Implementation**: DeepLabv3+ or SAM (Segment Anything Model)
**Applications**:
- Floor area measurement
- Empty space detection
- Corridor monitoring
- Shelf space analysis

### 3. Tracking Model  TODO:Planned

**Purpose**: Track objects across video frames
**Implementation**: ByteTrack or StrongSORT
**Applications**:
- Customer journey analysis  TODO:Planned
- Staff movement tracking  TODO:Planned
- Queue dynamics  TODO:Planned
- Dwell time measurement  TODO:Planned

## Scheduling Scenarios

The following scheduling scenarios are designed to work with the three models above, optimizing when and how they run.

### 1. Time-Based Scheduling

#### After-Hours Processing

**Description**: Run intensive processing tasks when the store is closed
**Applicable Models**: Object Detection, Segmentation
**Configuration Example**:
```yaml
model: "object_detection"
active_hours:
  start: "20:00"  # 8 PM - After store closing
  end: "06:00"    # 6 AM - Before store opening
pause_periods: []
frequency: 300    # 5 minutes
```
**Benefits**:
- Utilizes computing resources when store traffic is low
- Allows for more intensive processing without impacting customer-facing systems
- Provides comprehensive analysis before the next business day

#### Lunch Break Pause

**Description**: Pause non-critical processing during lunch hours
**Applicable Models**: All models
**Configuration Example**:
```yaml
model: "all"
active_hours:
  start: null  # Run all day
  end: null
pause_periods:
  - start: "12:00"
    end: "13:00"
    reason: "Lunch break - reduce system load"
```
**Benefits**:
- Reduces system load during peak employee break times
- Prioritizes computing resources for customer-facing applications
- Allows for system maintenance during a predictable downtime

### 2. Priority-Based Scheduling

**Description**: Assign different priorities to models based on business needs
**Configuration Example**:
```yaml
normal_hours:
  object_detection: 3  # Medium priority
  segmentation: 2      # Lower priority
  tracking: 4          # Higher priority

peak_hours:  # During busy periods
  object_detection: 5  # Higher priority for customer detection
  segmentation: 1      # Lowest priority
  tracking: 5          # Higher priority for queue management
```
**Benefits**:
- Ensures critical models get processing priority
- Adapts to changing store conditions
- Optimizes resource allocation

### 3. ROI-Specific Scheduling

**Description**: Run models only on specific regions of interest based on time
**Configuration Example**:
```yaml
morning_shift:  # 8 AM - 12 PM
  object_detection:
    active: true
    regions: ["entrance", "checkout", "promo_area"]
  segmentation:
    active: true
    regions: ["floor_area", "main_corridor"]

evening_shift:  # 5 PM - 9 PM
  object_detection:
    active: true
    regions: ["checkout", "high_value_area", "exit"]
  tracking:
    active: true
    regions: ["main_aisle", "checkout"]
```
**Benefits**:
- Focuses processing on relevant areas during specific time periods
- Reduces unnecessary processing of irrelevant areas
- Optimizes resource usage

## Specialized Application Scenarios

These scenarios combine models and scheduling to address specific retail needs.

### 1. Staff Presence Monitoring

**Business Need**: Ensure critical departments (butcher, bakery, pharmacy) are always staffed
**Primary Model**: People presence model 
**Scheduling Strategy**: Department-specific hours with continuous monitoring
**Configuration Example**:
```yaml
model: "object_detection"
application: "staff_presence"
departments:
  butcher:
    active_hours:
      start: "09:00"
      end: "19:00"
    required_staff: 1
    alert_delay: 300  # 5 minutes before alerting
  pharmacy:
    active_hours:
      start: "09:00"
      end: "21:00"
    required_staff: 1
    alert_delay: 120  # 2 minutes before alerting
frequency: 60  # Check every minute
```
**Benefits**:
- Ensures compliance with staffing requirements
- Improves customer service in specialized departments
- Provides accountability and management data

### 2. Promotion Effectiveness Analysis

**Business Need**: Measure customer engagement with promotional displays
**Primary Model**: Object Detection + Tracking (when available)
**Scheduling Strategy**: Condition-based activation during promotional periods
**Configuration Example**:
```yaml
model: "object_detection"
application: "promotion_analysis"
active_hours:
  start: null  # Can run any time
  end: null
activation_condition: "promotion_active"
roi_specific: true  # Only monitors promotion areas
frequency: 60  # 1 minute
```
**Benefits**:
- Measures promotion effectiveness in real-time
- Enables A/B testing of promotional strategies
- Optimizes staff allocation during promotions

### 3. Empty Corridor Monitoring

**Business Need**: Ensure emergency corridors remain clear of obstacles
**Primary Model**: Segmentation
**Scheduling Strategy**: Continuous monitoring with different alert thresholds
**Configuration Example**:
```yaml
model: "segmentation"
application: "corridor_monitoring"
active_hours:
  start: null  # Always active
  end: null
business_hours:
  start: "09:00"
  end: "21:00"
  frequency: 300  # 5 minutes
  alert_threshold: "medium"
after_hours:
  frequency: 600  # 10 minutes
  alert_threshold: "high"
critical_areas:
  - "emergency_exit_corridor"
  - "fire_equipment_access"
```
**Benefits**:
- Ensures compliance with safety regulations
- Prevents potential hazards
- Provides documentation for compliance reporting

## Customizable Scheduling Template

To make model scheduling flexible and customizable, we propose a standardized template system.

### Base Template Structure

```yaml
# Model Scheduling Template
model: "object_detection"  # Which model to use
application: "staff_monitoring"  # Application purpose
description: "Monitor staff presence in critical areas"

# Basic Scheduling
active_hours:
  start: "08:00"  # Start time (null for always active)
  end: "22:00"    # End time (null for always active)
  days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]

# Pause Periods
pause_periods:
  - start: "12:00"
    end: "13:00"
    days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]
    reason: "Lunch break"

# Execution Parameters
frequency: 300  # Base frequency in seconds
priority: 3     # Priority level (1-10, higher is more important)

# Conditional Execution
conditions:
  - type: "time_based"
    parameters:
      time_range: ["20:00", "06:00"]
      frequency: 600  # Different frequency during this time
      priority: 5     # Different priority during this time

# ROI Configuration
roi_specific: true
roi_ids: ["butcher_area", "pharmacy_counter", "bakery_section"]

# Model-Specific Parameters
parameters:
  resolution: "640x640"
  confidence_threshold: 0.4
  classes: ["person"]
```

## Implementation Recommendations

To implement the model scheduling system, the following enhancements are recommended:

1. **Extended BaseTracker Class**: Enhance the `BaseTracker` class with time-based scheduling capabilities
2. **Configuration System**: Update the configuration system to support template-based configuration
3. **GUI Integration**: Add scheduling configuration to the GUI for easy management
4. **Conditional Logic**: Implement conditional scheduling based on day, time, and external factors
5. **ROI-Specific Activation**: Add capability to activate models for specific ROIs based on conditions

## Conclusion

This document has presented a practical approach to model scheduling for retail applications based on a three-model system. By implementing intelligent scheduling strategies, retailers can:

- **Optimize resource usage** by scheduling intensive processing during off-hours (after 8 PM)
- **Reduce system load** during specific periods (like lunch hours 12:00-13:00)
- **Focus processing** on the most critical areas during relevant time periods
- **Address specific business needs** with specialized application scenarios

The proposed scheduling system is designed to be flexible and extensible, allowing for easy integration of the planned third model when it becomes available.
