# Plan for Creating Installers with Poetry + PyInstaller + Inno Setup

This plan outlines the steps to create standalone installers for your project using Poetry for dependency management, PyInstaller for bundling, and Inno Setup for creating Windows installers. The installers will **only include files from the `empty-shelf-detection/` directory**.

## Phase 1: Poetry Setup

### 1.1. Install Poetry
- Install Poetry using the official installer
- Configure Poetry to create virtual environments in the project directory

### 1.2. Initialize Poetry in the Project
- Run Poetry initialization in the empty-shelf-detection directory
- Create pyproject.toml with all required dependencies
- Configure project metadata and scripts

### 1.3. Create and Manage Virtual Environment
- Install dependencies and create a local virtual environment
- Learn how to activate, run commands, and manage dependencies
- Set up development dependencies for building installers

### 1.4. Clean Up Old Dependency Files
- Remove requirements.txt and requirements_updated.txt
- Remove any setup.py or setup.cfg files
- Remove any conda environment files (environment.yml)
- Remove any custom environment setup scripts
- Keep only Poetry-related configuration files

## Phase 2: PyInstaller Configuration

### 2.1. Create PyInstaller Spec Files
- Create spec file for GUI mode (main_gui.py)
- Create spec file for Seamless mode (main_seamless.py)
- Configure resource inclusion and icon settings

### 2.2. Build Applications with PyInstaller
- Build GUI mode application
- Build Seamless mode application
- Verify the built applications work correctly

## Phase 3: Inno Setup Configuration

### 3.1. Install Inno Setup
- Download and install Inno Setup for creating Windows installers

### 3.2. Create Installer Scripts
- Create script for GUI mode installer
- Create script for Seamless mode installer
- Configure application metadata and installation options

### 3.3. Create Supporting Files
- Create license.txt
- Create readme.txt
- Create service_installer.bat for Seamless mode

### 3.4. Build Installers
- Build GUI mode installer
- Build Seamless mode installer
- Test the installers on a clean system

## Phase 4: Virtual Environment Management

### 4.1. Development Workflow
- Export requirements when needed
- Check environment health
- Clean up unused packages

### 4.2. Deployment Preparation
- Update dependencies to latest compatible versions
- Verify installation and run tests
- Manage version numbers

## Phase 5: Automation (Optional)

### 5.1. Create Build Script
- Create a Python script to automate the build process
- Add command-line arguments for flexibility
- Implement clean, build, and package steps

## Summary of Steps

1. **Set up Poetry in the empty-shelf-detection directory**
   - Install Poetry
   - Configure Poetry to create virtual environments in the project directory
   - Create pyproject.toml
   - Install dependencies and create a local virtual environment
   - Remove old pip and conda configuration files

2. **Create PyInstaller spec files**
   - Create gui_mode.spec
   - Create seamless_mode.spec
   - Create app_icon.ico

3. **Build with PyInstaller**
   - Build GUI mode application
   - Build Seamless mode application

4. **Create Inno Setup scripts**
   - Create gui_installer.iss
   - Create seamless_installer.iss
   - Create supporting files (license.txt, readme.txt, service_installer.bat)

5. **Build installers with Inno Setup**
   - Build GUI mode installer
   - Build Seamless mode installer

6. **Manage Virtual Environment for Development and Deployment**
   - Export requirements when needed
   - Check environment health
   - Prepare for deployment
   - Manage version numbers

7. **Optional: Create automation script**
   - Create build_installers.py

## Final Result

You will have two standalone installers:
- `empty-shelf-detection/installer/HakmarAnnotate_Setup.exe` - GUI mode installer
- `empty-shelf-detection/installer/HakmarAnnotateSeamless_Setup.exe` - Seamless mode installer

These installers will only include the files from the `empty-shelf-detection/` directory, packaged as standalone executables with all dependencies included.
