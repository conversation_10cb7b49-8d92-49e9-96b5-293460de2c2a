# Detaylı Proje G<PERSON>tirme Planı (Güncellenmiş)

## Yüksek Öncelikli Görevler

## 1. API Entegrasyonu ve Görsel Gönderimi (4 saat) - ⚠️ YÜKSEK ÖNCELİK

### Açıklama
Sistemin hem çevrimdışı modda hem de API'ler açık olduğunda görsel gönderebilmesi için gerekli düzenlemelerin yapılması. API uçlarına yapılan isteklerin optimize edilmesi ve standardizasyonu.

### Mevcut Durum
- Tracker-centric mimariye geçiş tamamlandı
- `BaseTracker` sınıfı ve türevleri (`ShelfTracker`, `PresenceStateTracker`, `PersonTracker`) oluşturuldu
- `TrackerScheduler` sınıfı ile merkezi izleme yönetimi sağlandı
- `DetectionModeManager` sınıfı ile izleyicilerin merkezi yönetimi sağlandı

### Detayl<PERSON> Görevler
- `core/reporting` dizini oluşturulması ve raporlama sınıflarının buraya taşınması
- `ApiReporter` sınıfının, görsel gönderimi destekleyecek şekilde güncellenmesi
- `OfflineReporter` sınıfının, API formatıyla uyumlu JSON raporları oluşturacak şekilde düzenlenmesi
- `BaseTracker` sınıfındaki `send_report()` metodunun görsel gönderimi destekleyecek şekilde güncellenmesi
- `ShelfTracker`, `PresenceStateTracker` ve `PersonTracker` sınıflarındaki raporlama fonksiyonlarının güncellenmesi
- API isteklerinin başarısız olması durumunda otomatik olarak çevrimdışı moda geçiş yapılması
- Çevrimdışı modda kaydedilen raporların, internet bağlantısı sağlandığında otomatik olarak API'ye gönderilmesi için mekanizma oluşturulması

### Teknik Detaylar
- Görsel gönderimi için base64 kodlaması kullanılacak
- API istekleri için yeniden deneme mekanizması iyileştirilecek
- Çevrimdışı mod ve API modu arasında sorunsuz geçiş sağlanacak
- `TrackerScheduler._process_tracker_task()` metodunda raporlama kontrolü yapılıyor, bu kısım güncellenecek

## 2. Server Kurulumu ve İstemci Yapılandırması (4 saat)

### Açıklama
Uygulama sunucusunun kurulması ve GUI istemci makinasının yapılandırılması. Mustafa Bey ile koordinasyon sağlanarak gerekli kurulumların yapılması.

### Detaylı Görevler
- Sunucu gereksinimleri ve yapılandırma detaylarının belirlenmesi
- Sunucu üzerinde gerekli yazılımların kurulması (Python, gerekli kütüphaneler, YOLO modelleri)
- Sunucu üzerinde uygulama servisinin yapılandırılması
- GUI istemci makinasının kurulumu ve yapılandırılması
- Sunucu-istemci iletişiminin test edilmesi

### Teknik Detaylar
- Sunucu için gerekli donanım özellikleri: En az 8GB RAM, 4 çekirdekli işlemci, GPU desteği (tercihen)
- Sunucu işletim sistemi: Ubuntu 20.04 LTS veya Windows Server
- İstemci için gerekli donanım: En az 4GB RAM, 2 çekirdekli işlemci
- Ağ yapılandırması: Statik IP adresleri, güvenlik duvarı ayarları

## 3. API Senaryoları ve Dokümantasyon (1 saat)

### Açıklama
API kullanımı için senaryoların oluşturulması ve ilgili dokümantasyonun hazırlanması. Gelecekteki güncellemeler için yol haritasının belirlenmesi.

### Detaylı Görevler
- API endpoint'lerinin detaylı dokümantasyonunun hazırlanması
- Örnek API istekleri ve yanıtlarının oluşturulması
- Farklı kullanım senaryoları için API kullanım kılavuzunun hazırlanması
- Gelecekteki API güncellemeleri için yol haritasının oluşturulması

### Teknik Detaylar
- API dokümantasyonu için Markdown formatı kullanılacak
- Örnek istekler için curl ve Python kod örnekleri eklenecek
- API endpoint'leri: `/api/RaftakiBosKonumlar`, `/api/ROITespitler`, `/api/KisiSayisiRapor`
- İstek ve yanıt formatları JSON olarak standardize edilecek

## 4. Bildirim Sistemi ve Çalışma Saatleri Yapılandırması (24 saat) - ⚠️ YÜKSEK ÖNCELİK

### Açıklama
Durum değişikliklerinde bildirim gönderilmesi ve çalışma saatlerinin yapılandırılabilir hale getirilmesi. Algoritmaların ve izleyicilerin (tracker) bu çalışma saatlerine göre yapılandırılması.

### Mevcut Durum
- `TrackerScheduler` sınıfı ile izleyicilerin çalışma zamanları kontrol ediliyor
- `BaseTracker.should_run()` metodu ile izleyicilerin ne zaman çalışacağı belirleniyor
- `BaseTracker.should_report()` metodu ile izleyicilerin ne zaman rapor göndereceği belirleniyor
- `CoreConfig` sınıfı ile merkezi yapılandırma yönetimi sağlandı

### Detaylı Görevler
- `CoreConfig` sınıfına çalışma saatleri yapılandırması için yeni metodlar eklenmesi
- GUI üzerinden çalışma saatlerinin yapılandırılabilmesi için arayüz eklenmesi
- Çalışma saati bilgilerinin Shape sınıfına ve anotasyon dosyalarına kaydedilmesi
- Shape nesnesine aktarilan yeni calisma saatleri, calisma kurallari(interval), raporlama surelerinin okunaraka trackerlara aktarilmasi config den gelen verilerin override edilmesi. 
- `BaseTracker.should_run()` metodunun çalışma saatlerine göre davranışının düzenlenmesi
- `BaseTracker` sınıfına bildirim gönderme yeteneği eklenmesi (`send_notification()` metodu)
- `ShelfTracker`, `PresenceStateTracker` ve `PersonTracker` sınıflarına bildirim gönderme yeteneği eklenmesi
- Bildirim tercihlerinin GUI üzerinden yapılandırılabilmesi
- `TrackerScheduler._process_tracker_task()` metoduna bildirim kontrolü eklenmesi

### Teknik Detaylar
- Çalışma saatleri için `config.yaml` dosyasında yeni bir bölüm oluşturulacak
- Bildirimler için e-posta ve/veya webhook desteği eklenecek
- `core/notifications` dizini oluşturularak bildirim sınıfları burada tanımlanacak
- Çalışma saatleri dışında sistem düşük güç modunda çalışacak şekilde optimizasyon yapılacak
- `BaseTracker.should_run()` metoduna çalışma saati kontrolü eklenecek

## 5. Kapı Giriş-Çıkış Sayımı ve Model İyileştirmeleri (20 saat)

### Açıklama
Kapılardan giren-çıkan kişi sayısının takibi için model ve izleme algoritmasında iyileştirmeler yapılması.

### Detaylı Görevler
- Kapı bölgelerinin tanımlanması için GUI'ye özel bir anotasyon tipi eklenmesi
- Giriş-çıkış çizgilerinin tanımlanması ve yapılandırılması
- Kişi izleme algoritmasının (`IndividualPersonTracker`) giriş-çıkış sayımı için geliştirilmesi
- Sayım verilerinin raporlanması ve görselleştirilmesi
- Model doğruluğunun artırılması için iyileştirmeler

### Teknik Detaylar
- Kapı bölgeleri için yeni bir ROI tipi: "door_counting"
- Giriş-çıkış çizgilerinin yönlerinin tanımlanması
- Kişi izleme için gelişmiş algoritma: DeepSORT veya ByteTrack entegrasyonu
- Sayım verilerinin gerçek zamanlı görselleştirilmesi
- Model eğitimi için veri artırma (data augmentation) teknikleri

## 6. Raporlama Sürelerinin Yapılandırılması (24 saat) - ⚠️ YÜKSEK ÖNCELİK

### Açıklama
Raporlama süreleri ve kontrol sürelerinin anotasyon dokümanlarına GUI'den aktarılması. Tracker'ların bu kurallar üzerinde çalışacak şekilde düzenlenmesi ve senaryo bazlı şablon oluşturulması.

### Mevcut Durum
- `BaseTracker.should_report()` metodu ile izleyicilerin ne zaman rapor göndereceği belirleniyor
- `TrackerScheduler._process_tracker_task()` metodunda raporlama kontrolü yapılıyor
- `Shape` sınıfı ile anotasyon bilgileri yönetiliyor
- `DetectionModeManager.get_tracker_for_shape()` metodu ile izleyiciler oluşturuluyor

### Detaylı Görevler
- `Shape` sınıfına raporlama ve kontrol süreleri için yeni alanlar eklenmesi
- GUI üzerinden raporlama ve kontrol sürelerinin yapılandırılabilmesi için arayüz eklenmesi
- Süre bilgilerinin anotasyon dosyalarına kaydedilmesi için `file_handler.py` ve `pascal_voc_io.py` dosyalarının güncellenmesi
- `BaseTracker.should_report()` metodunun yapılandırılan sürelere göre davranışının düzenlenmesi
- `BaseTracker.set_shape_data()` metodunun süre bilgilerini alacak şekilde güncellenmesi
- `ShelfTracker`, `PresenceStateTracker` ve `PersonTracker` sınıflarının süre yapılandırmasını kullanacak şekilde güncellenmesi
- Farklı senaryolar için şablonların oluşturulması
- Şablonların kaydedilmesi ve yüklenmesi için mekanizma eklenmesi

### Teknik Detaylar
- Raporlama süreleri için `config.yaml` dosyasında yeni bir bölüm oluşturulacak
- Anotasyon dosyalarına süre bilgilerinin eklenmesi için format güncellemesi
- Şablonlar için JSON formatında yapılandırma dosyaları
- GUI'ye şablon yönetimi için yeni bir sekme eklenmesi
- `BaseTracker` sınıfına dinamik süre yapılandırması için yeni metodlar eklenmesi
- `DetectionModeManager.get_tracker_for_shape()` metodunun süre bilgilerini izleyicilere aktaracak şekilde güncellenmesi

## 7. Test ve Hata Ayıklama (10 saat) - ⚠️ YÜKSEK ÖNCELİK

### Açıklama
Uygulamanın kapsamlı testlerinin yapılması, sürüm çıkarılması ve hataların giderilmesi.

### Mevcut Durum
- `verify_tracker_registry.py` ile izleyici kayıt sisteminin doğruluğu test ediliyor
- `verify_redundant_code.py` ile gereksiz kod kontrolü yapılıyor
- `test_flow.py` ile uygulama akışı test ediliyor
- `TrackerScheduler` sınıfı ile izleyicilerin çalışması kontrol ediliyor
- `ModelManager` sınıfı ile model yönetimi ve bellek optimizasyonu sağlanıyor

### Detaylı Görevler
- Yeni eklenen özelliklerin birim testlerinin yazılması:
  - `BaseTracker.should_run()` ve `should_report()` metodları için testler
  - `TrackerScheduler._check_and_schedule_tracker()` ve `_create_and_queue_task()` metodları için testler
  - Raporlama ve bildirim sistemleri için testler
  - Çalışma saatleri yapılandırması için testler
- Entegrasyon testlerinin yapılması:
  - Farklı izleyici türlerinin birlikte çalışmasının testi
  - API ve çevrimdışı mod arasında geçişin testi
  - Çalışma saatleri ve raporlama sürelerinin testi
- Performans testleri ve optimizasyonlar:
  - Bellek kullanımının izlenmesi ve optimizasyonu
  - CPU kullanımının izlenmesi ve optimizasyonu
  - Çoklu kamera senaryolarında performans testi
- Hata ayıklama ve düzeltme
- Sürüm paketinin hazırlanması

### Teknik Detaylar
- Birim testleri için pytest kullanımı
- `empty-shelf-detection/tests` dizininde test dosyalarının oluşturulması
- Farklı kamera ve video kaynakları ile test
- Yüksek yük altında performans testleri için `core/utils/performance.py` modülünün oluşturulması
- Bellek sızıntılarının tespiti ve giderilmesi için `ModelManager.get_memory_stats()` metodunun kullanılması
- Dağıtım için cx_Freeze kullanılarak paket oluşturulması

## 8. Fayans Kontrolü Araştırması (1 saat)

### Açıklama
Fayans varlığının kontrolü için basit bir yaklaşım geliştirilmesi ve alternatif çalışmaların araştırılması.

### Detaylı Görevler
- Fayans tespiti için mevcut modelin kullanılabilirliğinin değerlendirilmesi
- Alternatif yaklaşımların araştırılması (renk analizi, doku analizi, vb.)
- Basit bir prototip çözümün geliştirilmesi
- Sonuçların değerlendirilmesi ve raporlanması

### Teknik Detaylar
- Görüntü işleme teknikleri: Kenar tespiti, doku analizi
- Mevcut YOLO modelinin fayans tespiti için uyarlanması
- Basit bir sınıflandırma modeli için transfer öğrenme
- Prototip için Jupyter notebook hazırlanması

## 9. Tek Model Entegrasyonu (24 saat)

### Açıklama
Boş raf algılama ve kişi bulma modellerinin tek bir modelde çalışmasını sağlayacak düzenlemelerin yapılması.

### Mevcut Durum
- `ModelManager` sınıfı ile model yönetimi sağlanıyor
- `BaseTracker.get_preferred_model_id()` metodu ile izleyiciler kendi modellerini seçebiliyor
- `TrackerScheduler._process_tracker_task()` metodu ile model işleme yapılıyor

### Detaylı Görevler
- Mevcut modellerin analizi ve ortak özelliklerin belirlenmesi
- Tek bir model için veri setinin hazırlanması
- Modelin eğitilmesi ve optimize edilmesi
- `ModelManager` sınıfının tek model desteği için güncellenmesi
- `BaseTracker.get_preferred_model_id()` metodunun güncellenmesi
- Entegrasyon için kod güncellemeleri
- Performans ve doğruluk testleri

### Teknik Detaylar
- YOLOv8 modeli kullanılarak çoklu sınıf tespiti
- Sınıflar: "Empty-Space", "Person", "Reduced"
- Veri seti hazırlama ve etiketleme
- Model eğitimi için GPU kullanımı
- Modelin quantization ve optimizasyonu
- `ModelManager.get_model()` metodunun tek model için optimize edilmesi

## 10. Yüksek Öncelikli Görevler İçin Detaylı Uygulama Planı

### 1. API Entegrasyonu ve Görsel Gönderimi (4 saat)

#### Adım 1: Raporlama Altyapısının Hazırlanması (1 saat)
- `core/reporting` dizinini oluştur
- `core/reporting/__init__.py` dosyasını oluştur
- `core/reporting/base_reporter.py` dosyasını oluştur ve `BaseReporter` sınıfını tanımla
- `core/reporting/api_reporter.py` dosyasını oluştur ve `ApiReporter` sınıfını tanımla
- `core/reporting/offline_reporter.py` dosyasını oluştur ve `OfflineReporter` sınıfını tanımla

#### Adım 2: Görsel Gönderimi Desteğinin Eklenmesi (1 saat)
- `BaseReporter` sınıfına görsel gönderimi için metodlar ekle
- `ApiReporter` sınıfına base64 kodlaması ile görsel gönderimi ekle
- `OfflineReporter` sınıfına görsel kaydetme ve JSON raporlama ekle
- `core/__init__.py` dosyasına raporlama sınıflarını ekle

#### Adım 3: Tracker Sınıflarının Güncellenmesi (1 saat)
- `BaseTracker.send_report()` metodunu görsel gönderimi destekleyecek şekilde güncelle
- `ShelfTracker`, `PresenceStateTracker` ve `PersonTracker` sınıflarındaki raporlama fonksiyonlarını güncelle
- `TrackerScheduler._process_tracker_task()` metodunu güncelle

#### Adım 4: Çevrimdışı-Çevrimiçi Geçiş Mekanizması (1 saat)
- `ApiReporter` sınıfına otomatik çevrimdışı moda geçiş ekle
- `OfflineReporter` sınıfına internet bağlantısı kontrolü ve otomatik gönderim ekle
- Test ve hata ayıklama

### 2. Bildirim Sistemi ve Çalışma Saatleri Yapılandırması (24 saat)

#### Adım 1: Çalışma Saatleri Yapılandırması (8 saat)
- `config.yaml` dosyasına çalışma saatleri bölümü ekle
- `CoreConfig` sınıfına çalışma saatleri için metodlar ekle
- `Shape` sınıfına çalışma saatleri için alanlar ekle
- `file_handler.py` ve `pascal_voc_io.py` dosyalarını güncelle
- `BaseTracker.should_run()` metodunu çalışma saatlerine göre güncelle

#### Adım 2: Bildirim Sistemi Altyapısı (8 saat)
- `core/notifications` dizinini oluştur
- `core/notifications/__init__.py` dosyasını oluştur
- `core/notifications/base_notifier.py` dosyasını oluştur ve `BaseNotifier` sınıfını tanımla
- `core/notifications/email_notifier.py` dosyasını oluştur ve `EmailNotifier` sınıfını tanımla
- `core/notifications/webhook_notifier.py` dosyasını oluştur ve `WebhookNotifier` sınıfını tanımla

#### Adım 3: Tracker Sınıflarına Bildirim Desteği (4 saat)
- `BaseTracker` sınıfına `send_notification()` metodunu ekle
- `ShelfTracker`, `PresenceStateTracker` ve `PersonTracker` sınıflarına bildirim desteği ekle
- `TrackerScheduler._process_tracker_task()` metoduna bildirim kontrolü ekle

#### Adım 4: GUI Entegrasyonu (4 saat)
- GUI'ye çalışma saatleri yapılandırması için arayüz ekle
- GUI'ye bildirim tercihleri için arayüz ekle
- Test ve hata ayıklama

### 3. Raporlama Sürelerinin Yapılandırılması (24 saat)

#### Adım 1: Raporlama Süreleri Yapılandırması (8 saat)
- `config.yaml` dosyasına raporlama süreleri bölümü ekle
- `CoreConfig` sınıfına raporlama süreleri için metodlar ekle
- `Shape` sınıfına raporlama süreleri için alanlar ekle
- `file_handler.py` ve `pascal_voc_io.py` dosyalarını güncelle

#### Adım 2: Tracker Sınıflarının Güncellenmesi (8 saat)
- `BaseTracker.should_report()` metodunu raporlama sürelerine göre güncelle
- `BaseTracker.set_shape_data()` metodunu süre bilgilerini alacak şekilde güncelle
- `ShelfTracker`, `PresenceStateTracker` ve `PersonTracker` sınıflarını güncelle
- `DetectionModeManager.get_tracker_for_shape()` metodunu güncelle

#### Adım 3: Şablon Sistemi (4 saat)
- Şablonlar için JSON formatında yapılandırma dosyaları oluştur
- Şablonların kaydedilmesi ve yüklenmesi için mekanizma ekle

#### Adım 4: GUI Entegrasyonu (4 saat)
- GUI'ye raporlama süreleri yapılandırması için arayüz ekle
- GUI'ye şablon yönetimi için yeni bir sekme ekle
- Test ve hata ayıklama

### 4. Test ve Hata Ayıklama (10 saat)

#### Adım 1: Birim Testleri (4 saat)
- `empty-shelf-detection/tests` dizinini oluştur
- `tests/test_trackers.py` dosyasını oluştur ve izleyici testlerini yaz
- `tests/test_scheduler.py` dosyasını oluştur ve zamanlayıcı testlerini yaz
- `tests/test_reporting.py` dosyasını oluştur ve raporlama testlerini yaz
- `tests/test_notifications.py` dosyasını oluştur ve bildirim testlerini yaz

#### Adım 2: Entegrasyon Testleri (3 saat)
- `tests/test_integration.py` dosyasını oluştur
- Farklı izleyici türlerinin birlikte çalışmasını test et
- API ve çevrimdışı mod arasında geçişi test et
- Çalışma saatleri ve raporlama sürelerini test et

#### Adım 3: Performans Testleri (2 saat)
- `core/utils/performance.py` modülünü oluştur
- Bellek kullanımını izle ve optimize et
- CPU kullanımını izle ve optimize et
- Çoklu kamera senaryolarında performans testi yap

#### Adım 4: Paketleme (1 saat)
- cx_Freeze için setup.py dosyasını güncelle
- Paket oluştur ve test et
