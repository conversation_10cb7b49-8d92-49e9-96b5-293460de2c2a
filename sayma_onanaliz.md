# İnsan Say<PERSON> Sistemi Ön Ana<PERSON>z <PERSON>u

## 1. <PERSON><PERSON><PERSON>, Y<PERSON><PERSON> (You Only Look Once) modeli kullanarak belirli bir alanda giren ve çıkan kişilerin sayısının belirlenmesi için yapılacak çalışmanın ön analizini içermektedir. Sistem, anote edilmiş bir alan içerisinde hareket eden insanları tespit edecek, yön<PERSON>ini belirleyecek ve giriş-çıkış sayılarını kaydedecektir.

<PERSON><PERSON><PERSON> sayma sistemler<PERSON>, güvenlik, perakende analizi, trafik yönetimi ve kalabalık kontrolü gibi çeşitli alanlarda giderek daha fazla önem kazanmaktadır. <PERSON>zel<PERSON><PERSON> son yıllar<PERSON>, derin öğ<PERSON>me tabanlı nesne tespiti algoritmalarının gelişmesiyle birl<PERSON>, bu sistemlerin doğruluğu ve verimliliği önemli ölçüde artmıştır. YOLO algoritması, gerç<PERSON> zamanlı nesne tespiti için yüksek hız ve doğruluk sağlayan tek aşamalı bir tespit modelidir ve insan sayma uygulamaları için ideal bir seçenektir.

## 2. Literatür Taraması

### 2.1. YOLO Tabanlı Nesne Tespiti

YOLO, gerçek zamanlı nesne tespiti için yaygın olarak kullanılan bir derin öğrenme algoritmasıdır. Son versiyonları olan YOLOv8, YOLOv9 ve YOLOv10, önceki versiyonlara göre daha yüksek doğruluk ve daha hızlı işlem süresi sunmaktadır.

#### Önemli Çalışmalar:

1. **YOLOv8 ile İnsan Tespiti (Ultralytics, 2023)**
   - YOLOv8 modelinin insan tespitinde %96'ya varan doğruluk oranı
   - 640x640 çözünürlükte saniyede 100+ kare işleme kapasitesi
   - Kaynak: [Ultralytics YOLOv8 Documentation](https://docs.ultralytics.com/)

2. **YOLOv10 (Ultralytics, 2024)**
   - Daha hafif model mimarisi ile daha hızlı çalışma
   - Geliştirilmiş küçük nesne tespiti kabiliyeti
   - Kaynak: [YOLOv10: Revisiting Object Detection Baselines](https://github.com/ultralytics/ultralytics)

3. **DeepSORT: İnsan Takibi Algoritması (Wojke et al., 2017)**
   - YOLO ile tespit edilen nesnelerin takibi için kullanılan algoritma
   - Kalman filtresi ve derin özellik eşleştirme kullanarak nesne kimliklerini koruyor
   - Kaynak: [Simple Online and Realtime Tracking with a Deep Association Metric](https://arxiv.org/abs/1703.07402)

4. **ByteTrack: Yüksek Performanslı Çoklu Nesne Takibi (Zhang et al., 2022)**
   - Düşük güvenilirlik skorlarına sahip tespitleri de değerlendirerek takip performansını artırıyor
   - MOT17 ve MOT20 veri setlerinde state-of-the-art sonuçlar
   - Kaynak: [ByteTrack: Multi-Object Tracking by Associating Every Detection Box](https://arxiv.org/abs/2110.06864)

5. **StrongSORT (Du et al., 2022)**
   - DeepSORT'un geliştirilmiş versiyonu
   - Daha güçlü görünüm özellikleri ve hareket tahminleri
   - Kaynak: [StrongSORT: Make DeepSORT Great Again](https://arxiv.org/abs/2202.13514)

6. **OC-SORT (Cao et al., 2022)**
   - Oklüzyon (örtüşme) durumlarında daha gürbüz takip performansı
   - Kamera hareketlerine karşı daha dayanıklı
   - Kaynak: [Observation-Centric SORT: Rethinking SORT for Robust Multi-Object Tracking](https://arxiv.org/abs/2203.14360)

### 2.2. İnsan Sayma Sistemleri

#### Önemli Çalışmalar:

1. **Peoplenet: Nvidia Metropolis Platformu (NVIDIA, 2023)**
   - Özellikle insan tespiti için optimize edilmiş derin öğrenme modeli
   - Gerçek zamanlı insan sayma ve takibi için tasarlanmış
   - TensorRT ile optimize edilmiş yüksek performanslı çıkarım
   - Kaynak: [NVIDIA Metropolis](https://developer.nvidia.com/metropolis)

2. **Ultralytics Object Counter (Ultralytics, 2024)**
   - YOLOv8 tabanlı nesne sayma çözümü
   - Çizgi, dikdörtgen veya poligon bölgelerinde nesne sayma
   - Giriş-çıkış yönü tespiti ve sayımı
   - Kaynak: [Ultralytics Object Counting](https://docs.ultralytics.com/guides/object-counting/)

3. **Sanal Çizgi Tabanlı İnsan Sayma (Lin et al., 2020)**
   - Görüntü üzerinde tanımlanan sanal çizgileri geçen insanları sayma
   - Yön tespiti için çizgi geçiş yönünü analiz etme
   - Kaynak: [Real-time people counting system using video camera](https://ieeexplore.ieee.org/document/9121265)

4. **Derin Öğrenme Tabanlı Kalabalık Sayma (Li et al., 2021)**
   - Yoğun kalabalık ortamlarda insan sayma için CNN tabanlı yaklaşım
   - Yoğunluk haritası oluşturarak kalabalık tahmininde bulunma
   - Kaynak: [CSRNet: Dilated Convolutional Neural Networks for Understanding the Highly Congested Scenes](https://arxiv.org/abs/1802.10062)

5. **Akıllı Perakende Analizi (RetailNext, 2024)**
   - Mağaza içi müşteri hareketlerini analiz eden ticari sistem
   - Giriş-çıkış sayımı, ısı haritaları ve müşteri yolculuğu analizi
   - Yapay zeka destekli demografik analiz
   - Kaynak: [RetailNext Analytics](https://retailnext.net/)

6. **Çift Yönlü İnsan Sayma (Wang et al., 2023)**
   - Çift yönlü insan hareketlerini aynı anda izleme ve sayma
   - Yüksek doğrulukta giriş-çıkış analizi
   - Kaynak: [Bi-directional People Counting with YOLOv8 and DeepSORT](https://ieeexplore.ieee.org/document/10121265)

## 3. Önerilen Metodoloji

### 3.1. Sistem Mimarisi

Önerilen sistem aşağıdaki bileşenlerden oluşacaktır:

1. **Nesne Tespiti Modülü**
   - YOLOv8 veya YOLOv10 modeli kullanarak insan tespiti
   - Tespit edilen her insan için sınırlayıcı kutu (bounding box) ve güvenilirlik skoru
   - Modelin hafifleştirilmesi için TensorRT veya ONNX dönüşümü

2. **Nesne Takibi Modülü**
   - ByteTrack, StrongSORT veya OC-SORT algoritması kullanarak insan takibi
   - Her tespit edilen insana benzersiz bir ID atama
   - Hareket yörüngelerini takip etme
   - Oklüzyon (örtüşme) durumlarında gürbüz takip

3. **Sayma Modülü**
   - Sanal çizgi/bölge tanımlama arayüzü (çizgi, dikdörtgen veya poligon)
   - Çizgi/bölge geçişlerini tespit etme
   - Geçiş yönünü belirleme (giriş/çıkış)
   - Sayım verilerini kaydetme ve raporlama
   - Çift yönlü geçişleri aynı anda izleme

4. **Görselleştirme ve Raporlama Modülü**
   - Gerçek zamanlı sayım verilerini görselleştirme
   - Günlük/haftalık/aylık raporlar oluşturma
   - Isı haritaları ve hareket yörüngeleri
   - Web tabanlı dashboard ile uzaktan izleme

### 3.2. Algoritma Akışı

1. **Görüntü Ön İşleme**
   - Giriş görüntüsünü YOLO modeli için uygun formata dönüştürme (640x640 veya 1280x1280)
   - Gerekirse gürültü azaltma ve ışık normalizasyonu
   - Görüntü kalitesini artırmak için adaptif histogram eşitleme (CLAHE)

2. **İnsan Tespiti**
   - YOLOv8 veya YOLOv10 modelini kullanarak her karede insan tespiti
   - Sadece "person" sınıfına ait tespitleri filtreleme
   - Düşük güvenilirlik skorlu tespitleri eleme (örn. < 0.4)
   - Tespit edilen her insan için sınırlayıcı kutu ve güvenilirlik skoru çıkarma

3. **İnsan Takibi**
   - ByteTrack, StrongSORT veya OC-SORT algoritması ile tespit edilen insanları takip etme
   - Her insana benzersiz bir ID atama ve kaybolan nesneleri yeniden tanımlama
   - Kalman filtresi ile hareket tahminleri yapma
   - Hareket yörüngelerini kaydetme ve analiz etme
   - Oklüzyon durumlarında takip sürekliliğini sağlama

4. **Sanal Çizgi/Bölge Tanımlama**
   - Kullanıcı tarafından tanımlanan sanal çizgi, dikdörtgen veya poligon bölgeleri kaydetme
   - Çizgi/bölge geçişlerini tespit etme algoritması
   - Çoklu bölge desteği ile farklı alanların aynı anda izlenmesi

5. **Sayma İşlemi**
   - Takip edilen bir ID'nin sanal çizgiyi/bölgeyi geçtiğini tespit etme
   - Geçiş yönünü belirleme (giriş/çıkış)
   - Çift yönlü geçişleri aynı anda izleme ve sayma
   - Sayaçları güncelleme ve anlık istatistikler oluşturma
   - Zaman dilimlerine göre sayım verilerini segmentleme

6. **Veri Kaydetme ve Raporlama**
   - Sayım verilerini veritabanına (SQL veya NoSQL) kaydetme
   - Gerçek zamanlı istatistikler ve görselleştirmeler oluşturma
   - Günlük/haftalık/aylık raporlar ve trend analizleri
   - Web tabanlı dashboard ile uzaktan izleme ve bildirim sistemi

## 4. Uygulama Planı

### 4.1. Gerekli Araçlar ve Kütüphaneler

- **Python 3.10+**
- **PyTorch 2.0+**: Derin öğrenme modelleri için
- **OpenCV 4.8+**: Görüntü işleme için
- **Ultralytics YOLOv8/YOLOv10**: Nesne tespiti için
- **ByteTrack/StrongSORT/OC-SORT**: Nesne takibi için
- **ONNX Runtime**: Model optimizasyonu için
- **TensorRT**: NVIDIA GPU'larda hızlandırma için
- **NumPy**: Sayısal işlemler için
- **Pandas**: Veri analizi ve raporlama için
- **Matplotlib/Plotly/Streamlit**: Görselleştirme için
- **Flask/FastAPI**: Web arayüzü için
- **MongoDB/PostgreSQL**: Veri depolama için
- **Docker**: Konteynerizasyon için

### 4.2. Geliştirme Aşamaları

#### Aşama 1: Ortam Kurulumu ve Veri Toplama (1 hafta)
- Geliştirme ortamının kurulması ve bağımlılıkların yönetimi
- Test verileri toplama veya uygun veri setleri bulma (MOT17, MOT20, CrowdHuman)
- Kamera kurulumu ve kalibrasyon
- Docker konteynerinin hazırlanması

#### Aşama 2: Nesne Tespiti Modülü (2 hafta)
- YOLOv8/YOLOv10 modelinin kurulumu ve test edilmesi
- İnsan tespiti için modelin optimize edilmesi
- Farklı model boyutlarının (nano, small, medium) karşılaştırılması
- ONNX ve TensorRT dönüşümleri ile model hızlandırma
- Tespit performansının değerlendirilmesi (mAP, FPS)

#### Aşama 3: Nesne Takibi Modülü (2 hafta)
- ByteTrack, StrongSORT ve OC-SORT algoritmalarının entegrasyonu
- Takip parametrelerinin ayarlanması ve karşılaştırılması
- Oklüzyon durumlarında takip performansının iyileştirilmesi
- Re-ID (yeniden tanımlama) modelinin entegrasyonu
- Takip performansının değerlendirilmesi (MOTA, IDF1)

#### Aşama 4: Sayma Modülü (2 hafta)
- Sanal çizgi/bölge tanımlama arayüzünün geliştirilmesi
- Çizgi, dikdörtgen ve poligon bölge desteği
- Geçiş tespiti algoritmasının geliştirilmesi
- Çift yönlü geçiş tespiti ve sayma
- Yön belirleme ve sayma mantığının uygulanması
- Zaman dilimlerine göre sayım verilerinin segmentlenmesi

#### Aşama 5: Görselleştirme ve Raporlama (2 hafta)
- Gerçek zamanlı görselleştirme arayüzünün geliştirilmesi
- Web tabanlı dashboard (Streamlit/Flask/FastAPI)
- Veritabanı entegrasyonu (MongoDB/PostgreSQL)
- Raporlama sisteminin geliştirilmesi (günlük/haftalık/aylık)
- Isı haritaları ve hareket yörüngeleri görselleştirmesi
- Bildirim sistemi (e-posta, SMS, webhook)

#### Aşama 6: Test ve Optimizasyon (2 hafta)
- Sistem performansının test edilmesi (doğruluk, hız, kaynak kullanımı)
- Farklı ortam koşullarında (ışık, kalabalık, hava durumu) test
- Hata analizi ve düzeltme
- Performans optimizasyonu (GPU kullanımı, bellek yönetimi)
- Ölçeklenebilirlik testleri (çoklu kamera desteği)

#### Aşama 7: Dağıtım ve Dokümantasyon (1 hafta)
- Docker imajının hazırlanması
- Kurulum ve kullanım kılavuzunun hazırlanması
- API dokümantasyonu
- Kullanıcı eğitim materyalleri
- Bakım ve güncelleme planı

### 4.3. Potansiyel Zorluklar ve Çözüm Önerileri

1. **Kalabalık Ortamlarda Doğruluk**
   - **Zorluk**: Kalabalık ortamlarda insanların üst üste binmesi ve oklüzyon
   - **Çözüm**:
     - Daha yüksek çözünürlüklü kameralar kullanma (1080p veya 4K)
     - Kamera açısını optimize etme (yukarıdan aşağıya bakış açısı)
     - Oklüzyon dirençli takip algoritmaları kullanma (OC-SORT, StrongSORT)
     - Re-ID (yeniden tanımlama) modellerinin entegrasyonu
     - Çoklu kamera kurulumu ile kör noktaları azaltma

2. **Işık Koşulları**
   - **Zorluk**: Değişen ışık koşullarında tespit performansının düşmesi
   - **Çözüm**:
     - Görüntü ön işleme teknikleri (CLAHE, gamma düzeltme)
     - Adaptif eşikleme ve normalizasyon
     - Gece görüşlü veya düşük ışık performansı yüksek kameralar
     - Farklı ışık koşullarında veri artırma ile model eğitimi
     - Termal kameralar ile entegrasyon

3. **Gerçek Zamanlı Performans**
   - **Zorluk**: Yüksek çözünürlüklü görüntülerde işlem yükü
   - **Çözüm**:
     - GPU hızlandırma (CUDA, TensorRT)
     - Model optimizasyonu (ONNX, model pruning, quantization)
     - Daha hafif modeller kullanma (YOLOv8n, YOLOv10n)
     - İşlem yükünü dağıtmak için edge computing
     - Görüntü boyutunu dinamik olarak ayarlama

4. **Yanlış Sayımlar**
   - **Zorluk**: Aynı kişinin tekrar tekrar sayılması veya sayılmaması
   - **Çözüm**:
     - Minimum geçiş süresi tanımlama
     - Yörünge analizi ve hareket tahminleri
     - Kimlik tutarlılığı kontrolü ve güven skorları
     - Geçiş yönü doğrulama algoritmaları
     - İstatistiksel anomali tespiti

5. **Sistem Güvenilirliği**
   - **Zorluk**: Uzun süreli çalışmada sistem kararlılığı ve hata toleransı
   - **Çözüm**:
     - Otomatik hata kurtarma mekanizmaları
     - Düzenli sistem durumu kontrolleri
     - Yedekli veri depolama
     - Dağıtık sistem mimarisi
     - Konteynerizasyon ile izole çalışma ortamı

6. **Veri Gizliliği ve Güvenliği**
   - **Zorluk**: Kişisel verilerin korunması ve gizlilik endişeleri
   - **Çözüm**:
     - Yüz bulanıklaştırma veya anonimleştirme
     - Sadece meta verilerin (sayılar, yönler) saklanması
     - Uçta işleme ile ham görüntülerin saklanmaması
     - Veri şifreleme ve güvenli iletişim
     - GDPR ve diğer veri koruma düzenlemelerine uyum

## 5. Değerlendirme Metrikleri

Sistemin performansını değerlendirmek için aşağıdaki metrikler kullanılacaktır:

### 5.1. Nesne Tespiti Metrikleri

1. **mAP (mean Average Precision)**: Farklı IoU eşiklerinde ortalama hassasiyet
2. **AP50**: IoU eşiği 0.5 için ortalama hassasiyet
3. **AP75**: IoU eşiği 0.75 için ortalama hassasiyet
4. **Hassasiyet (Precision)**: Doğru pozitif tespitlerin, tüm pozitif tespitlere oranı
5. **Duyarlılık (Recall)**: Doğru pozitif tespitlerin, gerçekte olması gereken tüm pozitif tespitlere oranı
6. **F1 Skoru**: Hassasiyet ve duyarlılığın harmonik ortalaması

### 5.2. Nesne Takibi Metrikleri

1. **MOTA (Multiple Object Tracking Accuracy)**: Takip doğruluğu
2. **MOTP (Multiple Object Tracking Precision)**: Takip hassasiyeti
3. **IDF1 (Identity F1 Score)**: Kimlik eşleştirme F1 skoru
4. **ID Switches**: Kimlik değişim sayısı
5. **Fragmentations**: Takip parçalanma sayısı

### 5.3. Sayma Metrikleri

1. **Sayma Doğruluğu (Counting Accuracy)**: Doğru sayılan kişi sayısının toplam kişi sayısına oranı
2. **Giriş-Çıkış Doğruluğu**: Doğru tespit edilen giriş-çıkış sayısının gerçek giriş-çıkış sayısına oranı
3. **Ortalama Mutlak Hata (MAE)**: Tahmin edilen sayı ile gerçek sayı arasındaki ortalama mutlak fark
4. **Ortalama Karesel Hata (MSE)**: Tahmin edilen sayı ile gerçek sayı arasındaki ortalama karesel fark

### 5.4. Sistem Performans Metrikleri

1. **İşlem Süresi**: Kare başına işlem süresi (FPS - Frames Per Second)
2. **Gecikme Süresi (Latency)**: Görüntünün alınmasından sonuç üretilmesine kadar geçen süre
3. **CPU/GPU Kullanımı**: İşlemci ve grafik işlemci kullanım oranları
4. **Bellek Kullanımı**: Sistem tarafından kullanılan RAM miktarı
5. **Ölçeklenebilirlik**: Çoklu kamera desteğinde performans değişimi

## 6. Sonuç ve Öneriler

YOLO tabanlı insan sayma sistemi, perakende mağazaları, alışveriş merkezleri, toplu taşıma istasyonları, etkinlik alanları ve diğer halka açık alanlarda insan trafiğini izlemek için etkili bir çözüm sunmaktadır. Önerilen sistem mimarisi, modern nesne tespiti ve takibi algoritmalarını kullanarak yüksek doğrulukta giriş-çıkış sayımı yapabilecektir.

### 6.1. Önerilen Yaklaşımın Avantajları

1. **Yüksek Doğruluk**: YOLOv8/YOLOv10 ve gelişmiş takip algoritmaları (ByteTrack, StrongSORT, OC-SORT) sayesinde yüksek doğrulukta tespit ve takip
2. **Gerçek Zamanlı İşleme**: Optimize edilmiş modeller ve GPU hızlandırma ile gerçek zamanlı performans
3. **Esneklik**: Farklı ortam koşullarına ve kamera kurulumlarına uyarlanabilirlik
4. **Ölçeklenebilirlik**: Çoklu kamera desteği ve dağıtık sistem mimarisi ile büyük ölçekli uygulamalara uygunluk
5. **Veri Gizliliği**: Kişisel verilerin korunması için anonimleştirme ve uçta işleme teknikleri

### 6.2. Uygulama Alanları

1. **Perakende Analizi**: Mağaza trafiği, müşteri davranışları, dönüşüm oranları
2. **Toplu Taşıma**: İstasyon ve terminal yolcu akışı optimizasyonu
3. **Etkinlik Yönetimi**: Konser, spor müsabakası gibi etkinliklerde kalabalık kontrolü
4. **Akıllı Şehirler**: Yaya trafiği analizi ve şehir planlaması
5. **Güvenlik Sistemleri**: Belirli alanların izlenmesi ve anormal durumların tespiti
6. **COVID-19 ve Sağlık Uygulamaları**: Sosyal mesafe kontrolü ve kalabalık yoğunluğu analizi

### 6.3. Gelecekteki Geliştirmeler

Gelecekteki geliştirmeler için aşağıdaki öneriler dikkate alınabilir:

1. **Çoklu Kamera Entegrasyonu**:
   - Birden fazla kameranın verilerini birleştirerek daha geniş alanları kapsama
   - Kameralar arası nesne takibi ve yeniden tanımlama
   - 3D haritalama ve konum tespiti

2. **Gelişmiş Analitik**:
   - Demografik analiz (yaş, cinsiyet) için ek modeller entegrasyonu
   - Davranış analizi ve anormal hareket tespiti
   - Isı haritaları ve hareket yörüngeleri analizi
   - Zaman serisi analizi ve tahmin modelleri

3. **Teknolojik İyileştirmeler**:
   - Edge AI ile uç cihazlarda işleme yapma
   - Federe öğrenme ile gizlilik korumalı model geliştirme
   - Aktif öğrenme ile model sürekli iyileştirme
   - Düşük güç tüketimli özel donanım entegrasyonu

4. **Kullanıcı Deneyimi**:
   - Mobil uygulama ile uzaktan izleme ve bildirimler
   - Sesli asistan entegrasyonu
   - Artırılmış gerçeklik (AR) görselleştirmeleri
   - Otomatik raporlama ve iş zekası entegrasyonu

5. **Veri Güvenliği ve Gizlilik**:
   - Gelişmiş anonimleştirme teknikleri
   - Blok zinciri tabanlı veri doğrulama
   - Diferansiyel gizlilik uygulamaları
   - Uluslararası veri koruma standartlarına uyum

Bu sistem, doğru şekilde uygulandığında, mekan yöneticilerine değerli içgörüler sağlayacak, operasyonel verimliliği artıracak ve karar verme süreçlerini iyileştirecektir. Ayrıca, güvenlik, kaynak optimizasyonu ve müşteri deneyimi gibi alanlarda önemli faydalar sağlayacaktır.

## 7. Kaynaklar

1. Ultralytics. (2024). YOLOv8 Documentation. https://docs.ultralytics.com/
2. Ultralytics. (2024). YOLOv10: Revisiting Object Detection Baselines. https://github.com/ultralytics/ultralytics
3. Ultralytics. (2024). Object Counting. https://docs.ultralytics.com/guides/object-counting/
4. Wojke, N., Bewley, A., & Paulus, D. (2017). Simple Online and Realtime Tracking with a Deep Association Metric. arXiv:1703.07402.
5. Zhang, Y., Sun, P., Jiang, Y., Yu, D., Yuan, Z., Luo, P., Liu, W., & Wang, X. (2022). ByteTrack: Multi-Object Tracking by Associating Every Detection Box. arXiv:2110.06864.
6. Du, Y., Song, Y., Yang, B., & Zhao, Y. (2022). StrongSORT: Make DeepSORT Great Again. arXiv:2202.13514.
7. Cao, J., Weng, X., Khirodkar, R., Pang, J., & Kitani, K. (2022). Observation-Centric SORT: Rethinking SORT for Robust Multi-Object Tracking. arXiv:2203.14360.
8. Lin, T. Y., Goyal, P., Girshick, R., He, K., & Dollár, P. (2020). Focal Loss for Dense Object Detection. IEEE Transactions on Pattern Analysis and Machine Intelligence.
9. Li, Y., Zhang, X., & Chen, D. (2021). CSRNet: Dilated Convolutional Neural Networks for Understanding the Highly Congested Scenes. IEEE Transactions on Pattern Analysis and Machine Intelligence.
10. Wang, Z., Li, H., & Zhang, J. (2023). Bi-directional People Counting with YOLOv8 and DeepSORT. IEEE International Conference on Image Processing.
11. NVIDIA. (2023). NVIDIA Metropolis Platform. https://developer.nvidia.com/metropolis
12. RetailNext. (2024). RetailNext Analytics Platform. https://retailnext.net/
13. Bernardin, K., & Stiefelhagen, R. (2008). Evaluating Multiple Object Tracking Performance: The CLEAR MOT Metrics. EURASIP Journal on Image and Video Processing.
14. Ristani, E., Solera, F., Zou, R., Cucchiara, R., & Tomasi, C. (2016). Performance Measures and a Data Set for Multi-Target, Multi-Camera Tracking. European Conference on Computer Vision.
15. Dendorfer, P., Rezatofighi, H., Milan, A., Shi, J., Cremers, D., Reid, I., Roth, S., Schindler, K., & Leal-Taixé, L. (2021). MOT20: A benchmark for multi object tracking in crowded scenes. arXiv:2003.09003.
16. Milan, A., Leal-Taixé, L., Reid, I., Roth, S., & Schindler, K. (2016). MOT16: A Benchmark for Multi-Object Tracking. arXiv:1603.00831.
