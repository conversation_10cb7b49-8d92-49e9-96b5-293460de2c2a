#!/bin/bash

# Activate the Anaconda environment
if [ -f "/opt/anaconda3/etc/profile.d/conda.sh" ]; then
    . "/opt/anaconda3/etc/profile.d/conda.sh"
    conda activate myenv
else
    echo "Conda installation not found at /opt/anaconda3"
fi

# Set PYTHONPATH for the project
export PYTHONPATH="${PYTHONPATH}:$(pwd):$(pwd)/empty-shelf-detection"

echo "Anaconda environment 'myenv' activated"
echo "PYTHONPATH set to: $PYTHONPATH"
