[flake8]
max-line-length = 160
# Exclude auto-generated files and specific directories
exclude =
    .git,
    __pycache__,
    .pytest_cache,
    build,
    dist,
    venv,
    .venv,
    resources.py,
    empty-shelf-detection/

# Ignore specific errors globally
ignore =
    E203, W503, F401, E302, E305, E231, E225, E261, E262, E265, E266,
    E402, E722, F403, F405, W291, W292, W293, W391,  E271, E111, E114, E115, E116, E117, E118, E119, E121, E122, E123, E124, E125, E126, E127, E128, E129, E131, E133, E134, E135, E136, E137, E138,
    E139, E141, E142, E143, E144, E145, E146, E147, E148, E149, E150, E151, E152, E153, E154, E155

